{"ast": null, "code": "import React,{useEffect}from'react';import{<PERSON>}from'react-router-dom';import ParticleBackground from'./ParticleBackground';import KuberaAnimation from'./KuberaAnimation';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const zodiacIcons={aries:'♈',taurus:'♉',gemini:'♊',cancer:'♋',leo:'♌',virgo:'♍',libra:'♎',scorpio:'♏',sagittarius:'♐',capricorn:'♑',aquarius:'♒',pisces:'♓'};const LandingPage=_ref=>{let{zodiacSigns}=_ref;useEffect(()=>{// Add floating animation to zodiac cards with staggered delay\nconst cards=document.querySelectorAll('.zodiac-card');cards.forEach((card,index)=>{card.style.animationDelay=\"\".concat(index*0.1,\"s\");card.classList.add('floating');});},[]);return/*#__PURE__*/_jsxs(\"div\",{className:\"landing-page\",children:[/*#__PURE__*/_jsx(ParticleBackground,{}),/*#__PURE__*/_jsx(KuberaAnimation,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"landing-header\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"main-title\",children:\"\\u0DC3\\u0DD2\\u0D82\\u0DC4\\u0DBD \\u0DA2\\u0DCA\\u200D\\u0DBA\\u0DDC\\u0DAD\\u0DD2\\u0DC2 \\u0DC0\\u0DD9\\u0DB6\\u0DCA \\u0D85\\u0DA9\\u0DC0\\u0DD2\\u0DBA\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"subtitle\",children:\"\\u0DAF\\u0DDB\\u0DB1\\u0DD2\\u0D9A \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DC3\\u0DC4 \\u0D86\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DB8\\u0D9C \\u0DB4\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DD3\\u0DB8\"}),/*#__PURE__*/_jsx(\"p\",{className:\"description\",children:\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\u0D94\\u0DB6\\u0DDA \\u0DAF\\u0DDB\\u0DB1\\u0DD2\\u0D9A \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DAF\\u0DD0\\u0DB1\\u0D9C\\u0DB1\\u0DCA\\u0DB1. \\u0DB4\\u0DD4\\u0DBB\\u0DCF\\u0DAB \\u0DA2\\u0DCA\\u200D\\u0DBA\\u0DDC\\u0DAD\\u0DD2\\u0DC2 \\u0DC1\\u0DCF\\u0DC3\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0D85\\u0DB1\\u0DD4\\u0DC0 \\u0D94\\u0DB6\\u0DDA \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DBA\\u0DA7 \\u0DB8\\u0D9C \\u0DB4\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DD3\\u0DB8 \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DB1\\u0DCA\\u0DB1.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"zodiac-grid\",children:zodiacSigns.map((sign,index)=>/*#__PURE__*/_jsxs(Link,{to:\"/\".concat(sign.id),className:\"zodiac-card\",style:{animationDelay:\"\".concat(index*0.1,\"s\")},children:[/*#__PURE__*/_jsx(\"span\",{className:\"zodiac-icon\",children:zodiacIcons[sign.id]}),/*#__PURE__*/_jsx(\"h3\",{className:\"zodiac-name\",children:sign.sinhala}),/*#__PURE__*/_jsx(\"p\",{className:\"zodiac-english\",children:sign.english})]},sign.id))})]});};export default LandingPage;", "map": {"version": 3, "names": ["React", "useEffect", "Link", "ParticleBackground", "KuberaAnimation", "jsx", "_jsx", "jsxs", "_jsxs", "zodiacIcons", "aries", "taurus", "gemini", "cancer", "leo", "virgo", "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces", "LandingPage", "_ref", "zodiacSigns", "cards", "document", "querySelectorAll", "for<PERSON>ach", "card", "index", "style", "animationDelay", "concat", "classList", "add", "className", "children", "map", "sign", "to", "id", "sinhala", "english"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\n\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\nconst LandingPage = ({ zodiacSigns }) => {\n  useEffect(() => {\n    // Add floating animation to zodiac cards with staggered delay\n    const cards = document.querySelectorAll('.zodiac-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.1}s`;\n      card.classList.add('floating');\n    });\n  }, []);\n\n  return (\n    <div className=\"landing-page\">\n      <ParticleBackground />\n      <KuberaAnimation />\n      \n      <div className=\"landing-header\">\n        <h1 className=\"main-title\">සිංහල ජ්‍යොතිෂ වෙබ් අඩවිය</h1>\n        <h2 className=\"subtitle\">දෛනික රාශිඵල සහ ආධ්‍යාත්මික මග පෙන්වීම</h2>\n        <p className=\"description\">\n          කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ ඔබේ දෛනික රාශිඵල දැනගන්න. \n          පුරාණ ජ්‍යොතිෂ ශාස්ත්‍රය අනුව ඔබේ ජීවිතයට මග පෙන්වීම ලබා ගන්න.\n        </p>\n      </div>\n\n      <div className=\"zodiac-grid\">\n        {zodiacSigns.map((sign, index) => (\n          <Link \n            key={sign.id} \n            to={`/${sign.id}`} \n            className=\"zodiac-card\"\n            style={{ animationDelay: `${index * 0.1}s` }}\n          >\n            <span className=\"zodiac-icon\">{zodiacIcons[sign.id]}</span>\n            <h3 className=\"zodiac-name\">{sign.sinhala}</h3>\n            <p className=\"zodiac-english\">{sign.english}</p>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default LandingPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CACrD,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,WAAW,CAAG,CAClBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,GAAG,CACXC,GAAG,CAAE,GAAG,CACRC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,GAAG,CACVC,OAAO,CAAE,GAAG,CACZC,WAAW,CAAE,GAAG,CAChBC,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAE,GACV,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAqB,IAApB,CAAEC,WAAY,CAAC,CAAAD,IAAA,CAClCtB,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAwB,KAAK,CAAGC,QAAQ,CAACC,gBAAgB,CAAC,cAAc,CAAC,CACvDF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CAC7BD,IAAI,CAACE,KAAK,CAACC,cAAc,IAAAC,MAAA,CAAMH,KAAK,CAAG,GAAG,KAAG,CAC7CD,IAAI,CAACK,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC,CAChC,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN,mBACE3B,KAAA,QAAK4B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B/B,IAAA,CAACH,kBAAkB,GAAE,CAAC,cACtBG,IAAA,CAACF,eAAe,GAAE,CAAC,cAEnBI,KAAA,QAAK4B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/B,IAAA,OAAI8B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,yIAAyB,CAAI,CAAC,cACzD/B,IAAA,OAAI8B,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,6MAAsC,CAAI,CAAC,cACpE/B,IAAA,MAAG8B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,umBAG3B,CAAG,CAAC,EACD,CAAC,cAEN/B,IAAA,QAAK8B,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBb,WAAW,CAACc,GAAG,CAAC,CAACC,IAAI,CAAET,KAAK,gBAC3BtB,KAAA,CAACN,IAAI,EAEHsC,EAAE,KAAAP,MAAA,CAAMM,IAAI,CAACE,EAAE,CAAG,CAClBL,SAAS,CAAC,aAAa,CACvBL,KAAK,CAAE,CAAEC,cAAc,IAAAC,MAAA,CAAKH,KAAK,CAAG,GAAG,KAAI,CAAE,CAAAO,QAAA,eAE7C/B,IAAA,SAAM8B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAE5B,WAAW,CAAC8B,IAAI,CAACE,EAAE,CAAC,CAAO,CAAC,cAC3DnC,IAAA,OAAI8B,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEE,IAAI,CAACG,OAAO,CAAK,CAAC,cAC/CpC,IAAA,MAAG8B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEE,IAAI,CAACI,OAAO,CAAI,CAAC,GAP3CJ,IAAI,CAACE,EAQN,CACP,CAAC,CACC,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}