{"ast": null, "code": "import React,{useEffect,useRef}from'react';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const SmokeAnimation=()=>{const containerRef=useRef(null);const intervalRef=useRef(null);useEffect(()=>{const container=containerRef.current;if(!container)return;const createSmokeParticle=()=>{const smoke=document.createElement('div');smoke.className='smoke-particle';// Random horizontal position\nconst leftPosition=Math.random()*100;// Random size and opacity\nconst size=Math.random()*60+40;// 40-100px\nconst opacity=Math.random()*0.3+0.1;// 0.1-0.4\n// Random animation duration\nconst duration=Math.random()*4+6;// 6-10 seconds\nsmoke.style.cssText=\"\\n        position: absolute;\\n        bottom: -50px;\\n        left: \".concat(leftPosition,\"%;\\n        width: \").concat(size,\"px;\\n        height: \").concat(size,\"px;\\n        background: radial-gradient(circle, rgba(244, 208, 63, \").concat(opacity,\") 0%, rgba(244, 208, 63, \").concat(opacity*0.5,\") 30%, transparent 70%);\\n        border-radius: 50%;\\n        pointer-events: none;\\n        animation: smokeRise \").concat(duration,\"s linear forwards;\\n        filter: blur(1px);\\n      \");container.appendChild(smoke);// Remove particle after animation\nsetTimeout(()=>{if(smoke.parentNode){smoke.parentNode.removeChild(smoke);}},duration*1000);};// Create smoke particles at intervals\nconst startSmokeAnimation=()=>{intervalRef.current=setInterval(()=>{createSmokeParticle();},800);// Create new particle every 800ms\n};startSmokeAnimation();// Cleanup\nreturn()=>{if(intervalRef.current){clearInterval(intervalRef.current);}};},[]);return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{ref:containerRef,style:{position:'fixed',bottom:0,left:0,width:'100%',height:'100%',pointerEvents:'none',zIndex:1,overflow:'hidden'}}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        @keyframes smokeRise {\\n          0% {\\n            transform: translateY(0) scale(1) rotate(0deg);\\n            opacity: 0.7;\\n          }\\n          25% {\\n            opacity: 0.8;\\n          }\\n          50% {\\n            transform: translateY(-50vh) scale(1.5) rotate(180deg);\\n            opacity: 0.6;\\n          }\\n          75% {\\n            opacity: 0.3;\\n          }\\n          100% {\\n            transform: translateY(-100vh) scale(2) rotate(360deg);\\n            opacity: 0;\\n          }\\n        }\\n        \\n        .smoke-particle {\\n          will-change: transform, opacity;\\n        }\\n      \"})]});};export default SmokeAnimation;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "SmokeAnimation", "containerRef", "intervalRef", "container", "current", "createSmokeParticle", "smoke", "document", "createElement", "className", "leftPosition", "Math", "random", "size", "opacity", "duration", "style", "cssText", "concat", "append<PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "startSmokeAnimation", "setInterval", "clearInterval", "children", "ref", "position", "bottom", "left", "width", "height", "pointerEvents", "zIndex", "overflow"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/SmokeAnimation.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst SmokeAnimation = () => {\n  const containerRef = useRef(null);\n  const intervalRef = useRef(null);\n\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    const createSmokeParticle = () => {\n      const smoke = document.createElement('div');\n      smoke.className = 'smoke-particle';\n      \n      // Random horizontal position\n      const leftPosition = Math.random() * 100;\n      \n      // Random size and opacity\n      const size = Math.random() * 60 + 40; // 40-100px\n      const opacity = Math.random() * 0.3 + 0.1; // 0.1-0.4\n      \n      // Random animation duration\n      const duration = Math.random() * 4 + 6; // 6-10 seconds\n      \n      smoke.style.cssText = `\n        position: absolute;\n        bottom: -50px;\n        left: ${leftPosition}%;\n        width: ${size}px;\n        height: ${size}px;\n        background: radial-gradient(circle, rgba(244, 208, 63, ${opacity}) 0%, rgba(244, 208, 63, ${opacity * 0.5}) 30%, transparent 70%);\n        border-radius: 50%;\n        pointer-events: none;\n        animation: smokeRise ${duration}s linear forwards;\n        filter: blur(1px);\n      `;\n      \n      container.appendChild(smoke);\n      \n      // Remove particle after animation\n      setTimeout(() => {\n        if (smoke.parentNode) {\n          smoke.parentNode.removeChild(smoke);\n        }\n      }, duration * 1000);\n    };\n\n    // Create smoke particles at intervals\n    const startSmokeAnimation = () => {\n      intervalRef.current = setInterval(() => {\n        createSmokeParticle();\n      }, 800); // Create new particle every 800ms\n    };\n\n    startSmokeAnimation();\n\n    // Cleanup\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <>\n      <div\n        ref={containerRef}\n        style={{\n          position: 'fixed',\n          bottom: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 1,\n          overflow: 'hidden'\n        }}\n      />\n      \n      <style jsx>{`\n        @keyframes smokeRise {\n          0% {\n            transform: translateY(0) scale(1) rotate(0deg);\n            opacity: 0.7;\n          }\n          25% {\n            opacity: 0.8;\n          }\n          50% {\n            transform: translateY(-50vh) scale(1.5) rotate(180deg);\n            opacity: 0.6;\n          }\n          75% {\n            opacity: 0.3;\n          }\n          100% {\n            transform: translateY(-100vh) scale(2) rotate(360deg);\n            opacity: 0;\n          }\n        }\n        \n        .smoke-particle {\n          will-change: transform, opacity;\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default SmokeAnimation;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEjD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,YAAY,CAAGR,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAS,WAAW,CAAGT,MAAM,CAAC,IAAI,CAAC,CAEhCD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAW,SAAS,CAAGF,YAAY,CAACG,OAAO,CACtC,GAAI,CAACD,SAAS,CAAE,OAEhB,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,KAAK,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAC3CF,KAAK,CAACG,SAAS,CAAG,gBAAgB,CAElC;AACA,KAAM,CAAAC,YAAY,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAExC;AACA,KAAM,CAAAC,IAAI,CAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAE;AACtC,KAAM,CAAAE,OAAO,CAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,GAAG,CAAE;AAE3C;AACA,KAAM,CAAAG,QAAQ,CAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CAAE;AAExCN,KAAK,CAACU,KAAK,CAACC,OAAO,yEAAAC,MAAA,CAGTR,YAAY,wBAAAQ,MAAA,CACXL,IAAI,0BAAAK,MAAA,CACHL,IAAI,yEAAAK,MAAA,CAC2CJ,OAAO,8BAAAI,MAAA,CAA4BJ,OAAO,CAAG,GAAG,wHAAAI,MAAA,CAGlFH,QAAQ,0DAEhC,CAEDZ,SAAS,CAACgB,WAAW,CAACb,KAAK,CAAC,CAE5B;AACAc,UAAU,CAAC,IAAM,CACf,GAAId,KAAK,CAACe,UAAU,CAAE,CACpBf,KAAK,CAACe,UAAU,CAACC,WAAW,CAAChB,KAAK,CAAC,CACrC,CACF,CAAC,CAAES,QAAQ,CAAG,IAAI,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAQ,mBAAmB,CAAGA,CAAA,GAAM,CAChCrB,WAAW,CAACE,OAAO,CAAGoB,WAAW,CAAC,IAAM,CACtCnB,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,GAAG,CAAC,CAAE;AACX,CAAC,CAEDkB,mBAAmB,CAAC,CAAC,CAErB;AACA,MAAO,IAAM,CACX,GAAIrB,WAAW,CAACE,OAAO,CAAE,CACvBqB,aAAa,CAACvB,WAAW,CAACE,OAAO,CAAC,CACpC,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEL,KAAA,CAAAF,SAAA,EAAA6B,QAAA,eACE/B,IAAA,QACEgC,GAAG,CAAE1B,YAAa,CAClBe,KAAK,CAAE,CACLY,QAAQ,CAAE,OAAO,CACjBC,MAAM,CAAE,CAAC,CACTC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdC,aAAa,CAAE,MAAM,CACrBC,MAAM,CAAE,CAAC,CACTC,QAAQ,CAAE,QACZ,CAAE,CACH,CAAC,cAEFxC,IAAA,UAAOD,GAAG,MAAAgC,QAAA,onBAyBD,CAAC,EACV,CAAC,CAEP,CAAC,CAED,cAAe,CAAA1B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}