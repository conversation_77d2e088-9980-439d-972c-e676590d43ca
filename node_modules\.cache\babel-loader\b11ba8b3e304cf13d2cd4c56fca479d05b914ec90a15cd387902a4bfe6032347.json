{"ast": null, "code": "import _objectSpread from\"/mnt/c/Users/<USER>/Desktop/Horoscope/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useRef}from'react';import{Link}from'react-router-dom';import ParticleBackground from'./ParticleBackground';import SmokeAnimation from'./SmokeAnimation';import KuberaAnimation from'./KuberaAnimation';import HoroscopeService from'../services/HoroscopeService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const zodiacIcons={aries:'♈',taurus:'♉',gemini:'♊',cancer:'♋',leo:'♌',virgo:'♍',libra:'♎',scorpio:'♏',sagittarius:'♐',capricorn:'♑',aquarius:'♒',pisces:'♓'};// Advanced horoscope parser that extracts structured content\nconst parseHoroscopeIntoStructuredCategories=rawText=>{// Check if rawText is valid\nif(!rawText||typeof rawText!=='string'){return[];}// Clean the raw text first\nconst cleanText=rawText.replace(/\\*\\*/g,'').replace(/##/g,'').replace(/\\*/g,'').replace(/\\[.*?\\]/g,'').trim();const categories={love:{id:'love',title:'ආදරය සහ සම්බන්ධතා',emoji:'💕',icon:'❤️',content:'',keywords:['ආදර','සම්බන්ධතා','ප්‍රේම','විවාහ','මිත්‍ර']},career:{id:'career',title:'වෘත්තීය ජීවිතය',emoji:'💼',icon:'🏢',content:'',keywords:['වෘත්ති','කාර්','රැකියා','ව්‍යාපාර','සේවා']},health:{id:'health',title:'සෞඛ්‍ය සහ යහපැවැත්ම',emoji:'🌿',icon:'🏥',content:'',keywords:['සෞඛ්','සෞඛ','යහපැවැත්ම','ශරීර','මානසික']},finance:{id:'finance',title:'මූල්‍ය කටයුතු',emoji:'💰',icon:'💳',content:'',keywords:['මූල්','මුදල්','ආර්ථික','ආදායම','වියදම']},general:{id:'general',title:'සාමාන්‍ය උපදෙස්',emoji:'✨',icon:'🔮',content:'',keywords:['සාමාන්','උපදෙස්','සාර්ථක','ජීවිත','දිනය']}};// Split text into lines and process\nconst lines=cleanText.split('\\n').filter(line=>line.trim().length>0);let currentCategory=null;let contentBuffer=[];// If no clear structure, distribute content evenly across categories\nif(lines.length<5){// Short content - put everything in general\ncategories.general.content=cleanText;}else{// Process each line to categorize content\nfor(let i=0;i<lines.length;i++){const line=lines[i].trim();if(!line||line.length<2)continue;// Detect category by keywords or numbered sections\nlet detectedCategory=null;// Check for numbered sections (1., 2., 3., etc.)\nconst numberedMatch=line.match(/^(\\d+)\\./);if(numberedMatch){const num=parseInt(numberedMatch[1]);const categoryOrder=['love','career','health','finance','general'];if(num>=1&&num<=5){detectedCategory=categoryOrder[num-1];}}// Check for keyword-based detection (more flexible)\nif(!detectedCategory){for(const[catId,catData]of Object.entries(categories)){for(const keyword of catData.keywords){if(line.toLowerCase().includes(keyword.toLowerCase())){detectedCategory=catId;break;}}if(detectedCategory)break;}}// If we found a new category, save previous content\nif(detectedCategory&&detectedCategory!==currentCategory){if(currentCategory&&contentBuffer.length>0){categories[currentCategory].content=contentBuffer.join(' ').trim();}currentCategory=detectedCategory;contentBuffer=[];// Clean the line and add to buffer\nlet cleanContent=line.replace(/^\\d+\\.\\s*/,'').replace(/^[•-]\\s*/,'').replace(new RegExp(categories[detectedCategory].title,'gi'),'').replace(/:/g,'').trim();if(cleanContent.length>0){contentBuffer.push(cleanContent);}}else if(currentCategory){// Add content to current category\nlet cleanContent=line.trim();if(cleanContent.length>0){contentBuffer.push(cleanContent);}}else{// No category detected yet, start with general and add content\ncurrentCategory='general';contentBuffer.push(line.trim());}}// If no categories were detected, distribute content intelligently\nif(!Object.values(categories).some(cat=>cat.content)){const sentences=cleanText.split(/[.!?]/).filter(s=>s.trim().length>10);const categoriesArray=Object.keys(categories);sentences.forEach((sentence,index)=>{const categoryIndex=index%categoriesArray.length;const categoryKey=categoriesArray[categoryIndex];if(!categories[categoryKey].content){categories[categoryKey].content=sentence.trim();}else{categories[categoryKey].content+=' '+sentence.trim();}});}}// Save final category content\nif(currentCategory&&contentBuffer.length>0){categories[currentCategory].content=contentBuffer.join(' ').trim();}// Ensure all categories have meaningful content\nObject.values(categories).forEach((category,index)=>{if(!category.content||category.content.length<5){// If still no content, use a portion of the original text\nconst sentences=cleanText.split(/[.!?]/).filter(s=>s.trim().length>5);if(sentences.length>index){category.content=sentences[index].trim()||cleanText.substring(index*50,(index+1)*50).trim();}else{// Last resort - use generic content based on category\nconst genericContent={love:'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.',health:'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.',general:'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'};category.content=genericContent[category.id]||'ධනාත්මක වෙනස්කම් සහ සාර්ථකත්වය අපේක්ෂා කරන්න.';}}});// Ensure each category has its id properly set and return as array\nreturn Object.entries(categories).map(_ref=>{let[key,category]=_ref;return _objectSpread(_objectSpread({},category),{},{id:key// Ensure id is properly set\n});});};// Beautiful category card component\nconst CategoryCard=_ref2=>{let{category,index}=_ref2;const cardStyles={love:{background:'linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.05) 100%)',border:'1px solid rgba(255, 182, 193, 0.3)',shadow:'0 8px 32px rgba(255, 105, 180, 0.1)'},career:{background:'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(30, 144, 255, 0.05) 100%)',border:'1px solid rgba(70, 130, 180, 0.3)',shadow:'0 8px 32px rgba(30, 144, 255, 0.1)'},health:{background:'linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.05) 100%)',border:'1px solid rgba(144, 238, 144, 0.3)',shadow:'0 8px 32px rgba(50, 205, 50, 0.1)'},finance:{background:'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%)',border:'1px solid rgba(255, 215, 0, 0.3)',shadow:'0 8px 32px rgba(255, 165, 0, 0.1)'},general:{background:'linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%)',border:'1px solid rgba(221, 160, 221, 0.3)',shadow:'0 8px 32px rgba(147, 112, 219, 0.1)'}};const style=cardStyles[category.id]||cardStyles.general;return/*#__PURE__*/_jsxs(\"div\",{className:\"horoscope-category-card\",style:{marginBottom:'2rem',padding:'2rem',background:style.background,border:style.border,borderRadius:'20px',boxShadow:style.shadow,backdropFilter:'blur(10px)',transition:'all 0.3s ease',position:'relative',overflow:'hidden'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-5px)';e.currentTarget.style.boxShadow=style.shadow.replace('0.1)','0.2)');},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow=style.shadow;},children:[/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'-50%',right:'-50%',width:'200%',height:'200%',background:\"radial-gradient(circle, \".concat(style.border.replace('1px solid ','').replace('0.3)','0.05)'),\" 1px, transparent 1px)\"),backgroundSize:'20px 20px',opacity:0.3,pointerEvents:'none'}}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'1.5rem',position:'relative',zIndex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'2.5rem',marginRight:'1rem',filter:'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'},children:category.emoji}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{style:{color:'#f4d03f',fontSize:'1.4rem',margin:0,fontFamily:'Noto Sans Sinhala, sans-serif',fontWeight:'600',textShadow:'0 2px 4px rgba(0,0,0,0.1)'},children:category.title}),/*#__PURE__*/_jsx(\"div\",{style:{width:'50px',height:'3px',background:'linear-gradient(90deg, #f4d03f, transparent)',marginTop:'0.5rem',borderRadius:'2px'}})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{position:'relative',zIndex:1},children:/*#__PURE__*/_jsx(\"p\",{style:{color:'#e8f4fd',lineHeight:'1.8',fontSize:'1.1rem',margin:0,fontFamily:'Noto Sans Sinhala, sans-serif',textAlign:'justify',textShadow:'0 1px 2px rgba(0,0,0,0.1)'},children:category.content})}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',bottom:0,left:0,right:0,height:'4px',background:\"linear-gradient(90deg, \".concat(style.border.replace('1px solid ','').replace('0.3)','0.6)'),\", transparent)\"),borderRadius:'0 0 20px 20px'}})]});};// Main display component\nconst StructuredHoroscopeDisplay=_ref3=>{let{horoscope}=_ref3;let categories;// Check if we have structured data from the new API\nif(horoscope&&horoscope.structured&&horoscope.categories){// Handle both object and array formats\nif(Array.isArray(horoscope.categories)){categories=horoscope.categories;}else{// Convert object to array with proper ids\ncategories=Object.entries(horoscope.categories).map(_ref4=>{let[key,category]=_ref4;return _objectSpread(_objectSpread({},category),{},{id:category.id||key});});}}else if(typeof horoscope==='string'){// Fallback to parsing raw text\ncategories=parseHoroscopeIntoStructuredCategories(horoscope);}else{categories=[];}// Fallback if no categories found\nif(!categories||categories.length===0){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'2rem',color:'#e8f4fd',fontFamily:'Noto Sans Sinhala, sans-serif'},children:\"\\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DC3\\u0D9A\\u0DC3\\u0DCA \\u0D9A\\u0DBB\\u0DB8\\u0DD2\\u0DB1\\u0DCA... \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DBB\\u0DD0\\u0DB3\\u0DD3 \\u0DC3\\u0DD2\\u0DA7\\u0DD2\\u0DB1\\u0DCA\\u0DB1.\"});}return/*#__PURE__*/_jsx(\"div\",{className:\"structured-horoscope-display\",style:{maxWidth:'800px',margin:'0 auto',padding:'1rem'},children:categories.map((category,index)=>/*#__PURE__*/_jsx(CategoryCard,{category:category,index:index},category.id||\"category-\".concat(index)))});};const ZodiacPage=_ref5=>{let{sign}=_ref5;const[horoscope,setHoroscope]=useState('');const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[soundEnabled,setSoundEnabled]=useState(true);// Default to true (on)\nconst[lastUpdated,setLastUpdated]=useState(null);const[refreshing,setRefreshing]=useState(false);const audioRef=useRef(null);const fetchHoroscope=useCallback(async function(){let forceRefresh=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{if(forceRefresh){setRefreshing(true);}else{setLoading(true);}setError('');// Check cache first (skip cache if force refresh)\nif(!forceRefresh){const cachedHoroscope=HoroscopeService.getCachedHoroscope(sign.id);if(cachedHoroscope){setHoroscope(cachedHoroscope);setLastUpdated(new Date());setLoading(false);return;}}// Fetch structured horoscope data from the new API\nconst horoscopeData=await HoroscopeService.getHoroscope(sign.id,forceRefresh);// Check if we got structured data\nif(horoscopeData&&horoscopeData.categories){// Convert API response to the format expected by the display component\nconst categoryConfig={love:{id:'love',title:'ආදරය සහ සම්බන්ධතා',emoji:'💕'},career:{id:'career',title:'වෘත්තීය ජීවිතය',emoji:'💼'},health:{id:'health',title:'සෞඛ්‍ය සහ යහපැවැත්ම',emoji:'🌿'},finance:{id:'finance',title:'මූල්‍ය කටයුතු',emoji:'💰'},general:{id:'general',title:'සාමාන්‍ය උපදෙස්',emoji:'✨'}};const categories=Object.entries(horoscopeData.categories).map(_ref6=>{let[key,content]=_ref6;return _objectSpread(_objectSpread({},categoryConfig[key]),{},{content:content||'අද දිනය සඳහා විශේෂ තොරතුරු නොමැත.'});});setHoroscope({categories,structured:true,dateCreated:horoscopeData.date_created,createdAt:horoscopeData.created_at,rawContent:horoscopeData.raw_content});}else{// Fallback to old parsing method if we get raw text\nsetHoroscope(horoscopeData);}setLastUpdated(new Date());// Cache the result\nHoroscopeService.setCachedHoroscope(sign.id,horoscopeData);}catch(err){setError('රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');console.error('Error fetching horoscope:',err);}finally{setLoading(false);setRefreshing(false);}},[sign.id]);useEffect(()=>{fetchHoroscope();},[fetchHoroscope]);// Auto-play background music when component mounts\nuseEffect(()=>{if(audioRef.current&&soundEnabled){// Set audio properties\naudioRef.current.loop=true;audioRef.current.volume=0.3;// Set volume to 30%\n// Try to play the audio\nconst playPromise=audioRef.current.play();if(playPromise!==undefined){playPromise.catch(error=>{console.log('Auto-play was prevented:',error);// Auto-play was prevented, user will need to interact first\n});}}},[soundEnabled]);const handleRefresh=()=>{fetchHoroscope(true);};const toggleSound=()=>{const newSoundState=!soundEnabled;setSoundEnabled(newSoundState);if(audioRef.current){if(newSoundState){audioRef.current.play().catch(console.error);}else{audioRef.current.pause();}}};const getCurrentDate=()=>{const today=new Date();const options={year:'numeric',month:'long',day:'numeric',weekday:'long'};return today.toLocaleDateString('si-LK',options);};return/*#__PURE__*/_jsxs(\"div\",{className:\"zodiac-page\",children:[/*#__PURE__*/_jsx(\"audio\",{ref:audioRef,src:\"/music.mp3\",loop:true,style:{display:'none'}}),/*#__PURE__*/_jsx(\"div\",{className:\"divine-background\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/god.jpg\",alt:\"Divine Blessing\",className:\"god-image\"})}),/*#__PURE__*/_jsx(ParticleBackground,{}),/*#__PURE__*/_jsx(SmokeAnimation,{}),/*#__PURE__*/_jsx(KuberaAnimation,{}),/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"back-button\",children:\"\\u2190 \\u0DB8\\u0DD4\\u0DBD\\u0DCA \\u0DB4\\u0DD2\\u0DA7\\u0DD4\\u0DC0\\u0DA7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"zodiac-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"zodiac-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"zodiac-icon\",style:{fontSize:'5rem',marginBottom:'1rem'},children:zodiacIcons[sign.id]}),/*#__PURE__*/_jsx(\"h1\",{className:\"zodiac-title\",children:sign.sinhala}),/*#__PURE__*/_jsxs(\"h2\",{className:\"zodiac-subtitle\",children:[sign.english,\" \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DBA\"]}),horoscope&&horoscope.dateCreated?/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(244, 208, 63, 0.15)',border:'1px solid rgba(244, 208, 63, 0.3)',borderRadius:'15px',padding:'1rem',marginBottom:'2rem',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#f4d03f',fontSize:'1.1rem',fontWeight:'bold',marginBottom:'0.5rem'},children:\"\\uD83D\\uDCC5 \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DAF\\u0DD2\\u0DB1\\u0DBA\"}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#ffffff',fontSize:'1rem'},children:new Date(horoscope.dateCreated).toLocaleDateString('si-LK',{year:'numeric',month:'long',day:'numeric',weekday:'long'})}),horoscope.createdAt&&/*#__PURE__*/_jsxs(\"div\",{style:{color:'#aeb6bf',fontSize:'0.85rem',marginTop:'0.5rem'},children:[\"\\u0DA2\\u0DB1\\u0DB1\\u0DBA \\u0D9A\\u0DC5\\u0DDA: \",new Date(horoscope.createdAt).toLocaleString('si-LK')]})]}):/*#__PURE__*/_jsx(\"p\",{style:{color:'#aeb6bf',marginBottom:'2rem'},children:getCurrentDate()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"horoscope-section\",children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1.5rem'},children:/*#__PURE__*/_jsx(\"h3\",{className:\"horoscope-title\",style:{margin:0},children:\"\\u0D85\\u0DAF \\u0DAF\\u0DD2\\u0DB1\\u0DBA\\u0DDA \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD\"})}),lastUpdated&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.85rem',color:'#aeb6bf',marginBottom:'1rem',textAlign:'center',fontStyle:'italic'},children:[\"\\u0D85\\u0DC0\\u0DC3\\u0DB1\\u0DCA \\u0DC0\\u0DBB\\u0DA7 \\u0DBA\\u0DCF\\u0DC0\\u0DAD\\u0DCA\\u0D9A\\u0DCF\\u0DBD\\u0DD3\\u0DB1 \\u0D9A\\u0DC5\\u0DDA: \",lastUpdated.toLocaleTimeString('si-LK',{hour:'2-digit',minute:'2-digit',hour12:true})]}),loading&&/*#__PURE__*/_jsx(\"div\",{className:\"loading\",children:\"\\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DB1\\u0DD2\\u0DB8\\u0DD2\\u0DB1\\u0DCA... \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DBB\\u0DD0\\u0DB3\\u0DD3 \\u0DC3\\u0DD2\\u0DA7\\u0DD2\\u0DB1\\u0DCA\\u0DB1.\"}),refreshing&&/*#__PURE__*/_jsx(\"div\",{className:\"loading\",children:\"\\u0DB1\\u0DC0 \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DB1\\u0DD2\\u0DB8\\u0DD2\\u0DB1\\u0DCA... \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DBB\\u0DD0\\u0DB3\\u0DD3 \\u0DC3\\u0DD2\\u0DA7\\u0DD2\\u0DB1\\u0DCA\\u0DB1.\"}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error\",children:[error,/*#__PURE__*/_jsx(\"button\",{onClick:handleRefresh,style:{marginLeft:'1rem',background:'rgba(231, 76, 60, 0.1)',border:'1px solid #e74c3c',color:'#e74c3c',padding:'0.4rem 0.8rem',borderRadius:'15px',cursor:'pointer',fontSize:'0.8rem'},children:\"\\u0DB1\\u0DD0\\u0DC0\\u0DAD \\u0D8B\\u0DAD\\u0DCA\\u0DC3\\u0DCF\\u0DC4 \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1\"})]}),!loading&&!refreshing&&!error&&horoscope&&/*#__PURE__*/_jsx(StructuredHoroscopeDisplay,{horoscope:horoscope})]}),/*#__PURE__*/_jsx(\"div\",{className:\"controls\",style:{marginTop:'2rem'},children:/*#__PURE__*/_jsx(\"button\",{onClick:toggleSound,className:\"sound-toggle\",style:{background:soundEnabled?'rgba(244, 208, 63, 0.2)':'rgba(255, 255, 255, 0.1)',border:'1px solid #f4d03f',color:'#f4d03f',padding:'0.8rem 1.5rem',borderRadius:'25px',cursor:'pointer',fontFamily:'Noto Sans Sinhala, sans-serif',transition:'all 0.3s ease'},children:soundEnabled?'🔊 ශබ්දය නිශ්ශබ්ද කරන්න':'🔇 ශබ්දය සක්‍රිය කරන්න'})}),/*#__PURE__*/_jsx(\"div\",{className:\"spiritual-message\",style:{marginTop:'3rem',padding:'2rem',background:'rgba(244, 208, 63, 0.1)',borderRadius:'15px',border:'1px solid rgba(244, 208, 63, 0.3)',textAlign:'center'},children:/*#__PURE__*/_jsx(\"p\",{style:{color:'#f4d03f',fontStyle:'italic',fontSize:'1.1rem'},children:\"\\\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0D94\\u0DB6 \\u0DC3\\u0DB8\\u0D9F \\u0DC0\\u0DDA\\u0DC0\\u0DCF\\\"\"})})]})]});};export default ZodiacPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Link", "ParticleBackground", "SmokeAnimation", "KuberaAnimation", "HoroscopeService", "jsx", "_jsx", "jsxs", "_jsxs", "zodiacIcons", "aries", "taurus", "gemini", "cancer", "leo", "virgo", "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces", "parseHoroscopeIntoStructuredCategories", "rawText", "cleanText", "replace", "trim", "categories", "love", "id", "title", "emoji", "icon", "content", "keywords", "career", "health", "finance", "general", "lines", "split", "filter", "line", "length", "currentCategory", "contentBuffer", "i", "detectedCategory", "numberedMatch", "match", "num", "parseInt", "categoryOrder", "catId", "catData", "Object", "entries", "keyword", "toLowerCase", "includes", "join", "cleanContent", "RegExp", "push", "values", "some", "cat", "sentences", "s", "categoriesArray", "keys", "for<PERSON>ach", "sentence", "index", "categoryIndex", "categoryKey", "category", "substring", "genericContent", "map", "_ref", "key", "_objectSpread", "CategoryCard", "_ref2", "cardStyles", "background", "border", "shadow", "style", "className", "marginBottom", "padding", "borderRadius", "boxShadow", "<PERSON><PERSON>ilter", "transition", "position", "overflow", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "children", "top", "right", "width", "height", "concat", "backgroundSize", "opacity", "pointerEvents", "display", "alignItems", "zIndex", "fontSize", "marginRight", "color", "margin", "fontFamily", "fontWeight", "textShadow", "marginTop", "lineHeight", "textAlign", "bottom", "left", "StructuredHoroscopeDisplay", "_ref3", "horoscope", "structured", "Array", "isArray", "_ref4", "max<PERSON><PERSON><PERSON>", "ZodiacPage", "_ref5", "sign", "setHoroscope", "loading", "setLoading", "error", "setError", "soundEnabled", "setSoundEnabled", "lastUpdated", "setLastUpdated", "refreshing", "setRefreshing", "audioRef", "fetchHoroscope", "forceRefresh", "arguments", "undefined", "cachedHoroscope", "getCachedHoroscope", "Date", "horoscopeData", "getHoroscope", "categoryConfig", "_ref6", "dateCreated", "date_created", "createdAt", "created_at", "rawContent", "raw_content", "setCachedHoroscope", "err", "console", "current", "loop", "volume", "playPromise", "play", "catch", "log", "handleRefresh", "toggleSound", "newSoundState", "pause", "getCurrentDate", "today", "options", "year", "month", "day", "weekday", "toLocaleDateString", "ref", "src", "alt", "to", "sinhala", "english", "toLocaleString", "fontStyle", "toLocaleTimeString", "hour", "minute", "hour12", "onClick", "marginLeft", "cursor"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport SmokeAnimation from './SmokeAnimation';\nimport KuberaAnimation from './KuberaAnimation';\nimport HoroscopeService from '../services/HoroscopeService';\n\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\n// Advanced horoscope parser that extracts structured content\nconst parseHoroscopeIntoStructuredCategories = (rawText) => {\n  // Check if rawText is valid\n  if (!rawText || typeof rawText !== 'string') {\n    return [];\n  }\n  \n  // Clean the raw text first\n  const cleanText = rawText\n    .replace(/\\*\\*/g, '')\n    .replace(/##/g, '')\n    .replace(/\\*/g, '')\n    .replace(/\\[.*?\\]/g, '')\n    .trim();\n\n  const categories = {\n    love: {\n      id: 'love',\n      title: 'ආදරය සහ සම්බන්ධතා',\n      emoji: '💕',\n      icon: '❤️',\n      content: '',\n      keywords: ['ආදර', 'සම්බන්ධතා', 'ප්‍රේම', 'විවාහ', 'මිත්‍ර']\n    },\n    career: {\n      id: 'career',\n      title: 'වෘත්තීය ජීවිතය',\n      emoji: '💼',\n      icon: '🏢',\n      content: '',\n      keywords: ['වෘත්ති', 'කාර්', 'රැකියා', 'ව්‍යාපාර', 'සේවා']\n    },\n    health: {\n      id: 'health',\n      title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n      emoji: '🌿',\n      icon: '🏥',\n      content: '',\n      keywords: ['සෞඛ්', 'සෞඛ', 'යහපැවැත්ම', 'ශරීර', 'මානසික']\n    },\n    finance: {\n      id: 'finance',\n      title: 'මූල්‍ය කටයුතු',\n      emoji: '💰',\n      icon: '💳',\n      content: '',\n      keywords: ['මූල්', 'මුදල්', 'ආර්ථික', 'ආදායම', 'වියදම']\n    },\n    general: {\n      id: 'general',\n      title: 'සාමාන්‍ය උපදෙස්',\n      emoji: '✨',\n      icon: '🔮',\n      content: '',\n      keywords: ['සාමාන්', 'උපදෙස්', 'සාර්ථක', 'ජීවිත', 'දිනය']\n    }\n  };\n\n  // Split text into lines and process\n  const lines = cleanText.split('\\n').filter(line => line.trim().length > 0);\n  let currentCategory = null;\n  let contentBuffer = [];\n  \n  // If no clear structure, distribute content evenly across categories\n  if (lines.length < 5) {\n    // Short content - put everything in general\n    categories.general.content = cleanText;\n  } else {\n    // Process each line to categorize content\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n      \n      if (!line || line.length < 2) continue;\n      \n      // Detect category by keywords or numbered sections\n      let detectedCategory = null;\n      \n      // Check for numbered sections (1., 2., 3., etc.)\n      const numberedMatch = line.match(/^(\\d+)\\./); \n      if (numberedMatch) {\n        const num = parseInt(numberedMatch[1]);\n        const categoryOrder = ['love', 'career', 'health', 'finance', 'general'];\n        if (num >= 1 && num <= 5) {\n          detectedCategory = categoryOrder[num - 1];\n        }\n      }\n      \n      // Check for keyword-based detection (more flexible)\n      if (!detectedCategory) {\n        for (const [catId, catData] of Object.entries(categories)) {\n          for (const keyword of catData.keywords) {\n            if (line.toLowerCase().includes(keyword.toLowerCase())) {\n              detectedCategory = catId;\n              break;\n            }\n          }\n          if (detectedCategory) break;\n        }\n      }\n      \n      // If we found a new category, save previous content\n      if (detectedCategory && detectedCategory !== currentCategory) {\n        if (currentCategory && contentBuffer.length > 0) {\n          categories[currentCategory].content = contentBuffer.join(' ').trim();\n        }\n        currentCategory = detectedCategory;\n        contentBuffer = [];\n        \n        // Clean the line and add to buffer\n        let cleanContent = line\n          .replace(/^\\d+\\.\\s*/, '')\n          .replace(/^[•-]\\s*/, '')\n          .replace(new RegExp(categories[detectedCategory].title, 'gi'), '')\n          .replace(/:/g, '')\n          .trim();\n        \n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else if (currentCategory) {\n        // Add content to current category\n        let cleanContent = line.trim();\n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else {\n        // No category detected yet, start with general and add content\n        currentCategory = 'general';\n        contentBuffer.push(line.trim());\n       }\n    }\n    \n    // If no categories were detected, distribute content intelligently\n    if (!Object.values(categories).some(cat => cat.content)) {\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 10);\n      const categoriesArray = Object.keys(categories);\n      \n      sentences.forEach((sentence, index) => {\n        const categoryIndex = index % categoriesArray.length;\n        const categoryKey = categoriesArray[categoryIndex];\n        if (!categories[categoryKey].content) {\n          categories[categoryKey].content = sentence.trim();\n        } else {\n          categories[categoryKey].content += ' ' + sentence.trim();\n        }\n      });\n    }\n   }\n   \n   // Save final category content\n   if (currentCategory && contentBuffer.length > 0) {\n     categories[currentCategory].content = contentBuffer.join(' ').trim();\n   }\n   \n   // Ensure all categories have meaningful content\n  Object.values(categories).forEach((category, index) => {\n    if (!category.content || category.content.length < 5) {\n      // If still no content, use a portion of the original text\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 5);\n      if (sentences.length > index) {\n        category.content = sentences[index].trim() || cleanText.substring(index * 50, (index + 1) * 50).trim();\n      } else {\n        // Last resort - use generic content based on category\n        const genericContent = {\n          love: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n          career: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.',\n          health: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.',\n          finance: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.',\n          general: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n        };\n        category.content = genericContent[category.id] || 'ධනාත්මක වෙනස්කම් සහ සාර්ථකත්වය අපේක්ෂා කරන්න.';\n      }\n    }\n  });\n   \n   // Ensure each category has its id properly set and return as array\n   return Object.entries(categories).map(([key, category]) => ({\n     ...category,\n     id: key // Ensure id is properly set\n   }));\n };\n \n // Beautiful category card component\n const CategoryCard = ({ category, index }) => {\n   const cardStyles = {\n     love: {\n       background: 'linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.05) 100%)',\n       border: '1px solid rgba(255, 182, 193, 0.3)',\n       shadow: '0 8px 32px rgba(255, 105, 180, 0.1)'\n     },\n     career: {\n       background: 'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(30, 144, 255, 0.05) 100%)',\n       border: '1px solid rgba(70, 130, 180, 0.3)',\n       shadow: '0 8px 32px rgba(30, 144, 255, 0.1)'\n     },\n     health: {\n       background: 'linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.05) 100%)',\n       border: '1px solid rgba(144, 238, 144, 0.3)',\n       shadow: '0 8px 32px rgba(50, 205, 50, 0.1)'\n     },\n     finance: {\n       background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%)',\n       border: '1px solid rgba(255, 215, 0, 0.3)',\n       shadow: '0 8px 32px rgba(255, 165, 0, 0.1)'\n     },\n     general: {\n       background: 'linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%)',\n       border: '1px solid rgba(221, 160, 221, 0.3)',\n       shadow: '0 8px 32px rgba(147, 112, 219, 0.1)'\n     }\n   };\n   \n   const style = cardStyles[category.id] || cardStyles.general;\n   \n   return (\n     <div \n       className=\"horoscope-category-card\"\n       style={{\n         marginBottom: '2rem',\n         padding: '2rem',\n         background: style.background,\n         border: style.border,\n         borderRadius: '20px',\n         boxShadow: style.shadow,\n         backdropFilter: 'blur(10px)',\n         transition: 'all 0.3s ease',\n         position: 'relative',\n         overflow: 'hidden'\n       }}\n       onMouseEnter={(e) => {\n         e.currentTarget.style.transform = 'translateY(-5px)';\n         e.currentTarget.style.boxShadow = style.shadow.replace('0.1)', '0.2)');\n       }}\n       onMouseLeave={(e) => {\n         e.currentTarget.style.transform = 'translateY(0)';\n         e.currentTarget.style.boxShadow = style.shadow;\n       }}\n     >\n       {/* Decorative background pattern */}\n       <div \n         style={{\n           position: 'absolute',\n           top: '-50%',\n           right: '-50%',\n           width: '200%',\n           height: '200%',\n           background: `radial-gradient(circle, ${style.border.replace('1px solid ', '').replace('0.3)', '0.05)')} 1px, transparent 1px)`,\n           backgroundSize: '20px 20px',\n           opacity: 0.3,\n           pointerEvents: 'none'\n         }}\n       />\n       \n       {/* Header */}\n       <div \n         style={{\n           display: 'flex',\n           alignItems: 'center',\n           marginBottom: '1.5rem',\n           position: 'relative',\n           zIndex: 1\n         }}\n       >\n         <div \n           style={{\n             fontSize: '2.5rem',\n             marginRight: '1rem',\n             filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n           }}\n         >\n           {category.emoji}\n         </div>\n         <div>\n           <h3 \n             style={{\n               color: '#f4d03f',\n               fontSize: '1.4rem',\n               margin: 0,\n               fontFamily: 'Noto Sans Sinhala, sans-serif',\n               fontWeight: '600',\n               textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n             }}\n           >\n             {category.title}\n           </h3>\n           <div \n             style={{\n               width: '50px',\n               height: '3px',\n               background: 'linear-gradient(90deg, #f4d03f, transparent)',\n               marginTop: '0.5rem',\n               borderRadius: '2px'\n             }}\n           />\n         </div>\n       </div>\n       \n       {/* Content */}\n       <div \n         style={{\n           position: 'relative',\n           zIndex: 1\n         }}\n       >\n         <p \n           style={{\n             color: '#e8f4fd',\n             lineHeight: '1.8',\n             fontSize: '1.1rem',\n             margin: 0,\n             fontFamily: 'Noto Sans Sinhala, sans-serif',\n             textAlign: 'justify',\n             textShadow: '0 1px 2px rgba(0,0,0,0.1)'\n           }}\n         >\n           {category.content}\n         </p>\n       </div>\n       \n       {/* Bottom accent */}\n       <div \n         style={{\n           position: 'absolute',\n           bottom: 0,\n           left: 0,\n           right: 0,\n           height: '4px',\n           background: `linear-gradient(90deg, ${style.border.replace('1px solid ', '').replace('0.3)', '0.6)')}, transparent)`,\n           borderRadius: '0 0 20px 20px'\n         }}\n       />\n     </div>\n   );\n };\n \n // Main display component\nconst StructuredHoroscopeDisplay = ({ horoscope }) => {\n  let categories;\n  \n  // Check if we have structured data from the new API\n  if (horoscope && horoscope.structured && horoscope.categories) {\n    // Handle both object and array formats\n    if (Array.isArray(horoscope.categories)) {\n      categories = horoscope.categories;\n    } else {\n      // Convert object to array with proper ids\n      categories = Object.entries(horoscope.categories).map(([key, category]) => ({\n        ...category,\n        id: category.id || key\n      }));\n    }\n  } else if (typeof horoscope === 'string') {\n    // Fallback to parsing raw text\n    categories = parseHoroscopeIntoStructuredCategories(horoscope);\n  } else {\n    categories = [];\n  }\n  \n  // Fallback if no categories found\n  if (!categories || categories.length === 0) {\n    return (\n      <div style={{\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#e8f4fd',\n        fontFamily: 'Noto Sans Sinhala, sans-serif'\n      }}>\n        රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.\n      </div>\n    );\n  }\n  \n  return (\n    <div \n      className=\"structured-horoscope-display\"\n      style={{\n        maxWidth: '800px',\n        margin: '0 auto',\n        padding: '1rem'\n      }}\n    >\n      {categories.map((category, index) => (\n        <CategoryCard \n          key={category.id || `category-${index}`} \n          category={category} \n          index={index} \n        />\n      ))}\n    </div>\n  );\n};\n\nconst ZodiacPage = ({ sign }) => {\n  const [horoscope, setHoroscope] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [soundEnabled, setSoundEnabled] = useState(true); // Default to true (on)\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const audioRef = useRef(null);\n\n  const fetchHoroscope = useCallback(async (forceRefresh = false) => {\n    try {\n      if (forceRefresh) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError('');\n      \n      // Check cache first (skip cache if force refresh)\n      if (!forceRefresh) {\n        const cachedHoroscope = HoroscopeService.getCachedHoroscope(sign.id);\n        if (cachedHoroscope) {\n          setHoroscope(cachedHoroscope);\n          setLastUpdated(new Date());\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Fetch structured horoscope data from the new API\n      const horoscopeData = await HoroscopeService.getHoroscope(sign.id, forceRefresh);\n      \n      // Check if we got structured data\n      if (horoscopeData && horoscopeData.categories) {\n        // Convert API response to the format expected by the display component\n        const categoryConfig = {\n          love: { id: 'love', title: 'ආදරය සහ සම්බන්ධතා', emoji: '💕' },\n          career: { id: 'career', title: 'වෘත්තීය ජීවිතය', emoji: '💼' },\n          health: { id: 'health', title: 'සෞඛ්‍ය සහ යහපැවැත්ම', emoji: '🌿' },\n          finance: { id: 'finance', title: 'මූල්‍ය කටයුතු', emoji: '💰' },\n          general: { id: 'general', title: 'සාමාන්‍ය උපදෙස්', emoji: '✨' }\n        };\n        \n        const categories = Object.entries(horoscopeData.categories).map(([key, content]) => ({\n          ...categoryConfig[key],\n          content: content || 'අද දිනය සඳහා විශේෂ තොරතුරු නොමැත.'\n        }));\n        \n        setHoroscope({ \n          categories, \n          structured: true,\n          dateCreated: horoscopeData.date_created,\n          createdAt: horoscopeData.created_at,\n          rawContent: horoscopeData.raw_content\n        });\n      } else {\n        // Fallback to old parsing method if we get raw text\n        setHoroscope(horoscopeData);\n      }\n      \n      setLastUpdated(new Date());\n      \n      // Cache the result\n      HoroscopeService.setCachedHoroscope(sign.id, horoscopeData);\n      \n    } catch (err) {\n      setError('රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');\n      console.error('Error fetching horoscope:', err);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [sign.id]);\n\n  useEffect(() => {\n    fetchHoroscope();\n  }, [fetchHoroscope]);\n\n  // Auto-play background music when component mounts\n  useEffect(() => {\n    if (audioRef.current && soundEnabled) {\n      // Set audio properties\n      audioRef.current.loop = true;\n      audioRef.current.volume = 0.3; // Set volume to 30%\n      \n      // Try to play the audio\n      const playPromise = audioRef.current.play();\n      if (playPromise !== undefined) {\n        playPromise.catch(error => {\n          console.log('Auto-play was prevented:', error);\n          // Auto-play was prevented, user will need to interact first\n        });\n      }\n    }\n  }, [soundEnabled]);\n\n  const handleRefresh = () => {\n    fetchHoroscope(true);\n  };\n\n  const toggleSound = () => {\n    const newSoundState = !soundEnabled;\n    setSoundEnabled(newSoundState);\n    \n    if (audioRef.current) {\n      if (newSoundState) {\n        audioRef.current.play().catch(console.error);\n      } else {\n        audioRef.current.pause();\n      }\n    }\n  };\n\n  const getCurrentDate = () => {\n    const today = new Date();\n    const options = { \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric',\n      weekday: 'long'\n    };\n    return today.toLocaleDateString('si-LK', options);\n  };\n\n  return (\n    <div className=\"zodiac-page\">\n      {/* Background Music Audio Element */}\n      <audio \n        ref={audioRef}\n        src=\"/music.mp3\"\n        loop\n        style={{ display: 'none' }}\n      />\n      \n      {/* Divine Background Image */}\n      <div className=\"divine-background\">\n        <img \n          src=\"/god.jpg\" \n          alt=\"Divine Blessing\" \n          className=\"god-image\"\n        />\n      </div>\n      \n      <ParticleBackground />\n      <SmokeAnimation />\n      <KuberaAnimation />\n      \n      <Link to=\"/\" className=\"back-button\">\n        ← මුල් පිටුවට\n      </Link>\n\n      <div className=\"zodiac-content\">\n        <div className=\"zodiac-header\">\n          <div className=\"zodiac-icon\" style={{ fontSize: '5rem', marginBottom: '1rem' }}>\n            {zodiacIcons[sign.id]}\n          </div>\n          <h1 className=\"zodiac-title\">{sign.sinhala}</h1>\n          <h2 className=\"zodiac-subtitle\">{sign.english} රාශිය</h2>\n          \n          {/* Display horoscope date if available */}\n          {horoscope && horoscope.dateCreated ? (\n            <div style={{ \n              background: 'rgba(244, 208, 63, 0.15)',\n              border: '1px solid rgba(244, 208, 63, 0.3)',\n              borderRadius: '15px',\n              padding: '1rem',\n              marginBottom: '2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{ color: '#f4d03f', fontSize: '1.1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>\n                📅 රාශිඵල දිනය\n              </div>\n              <div style={{ color: '#ffffff', fontSize: '1rem' }}>\n                {new Date(horoscope.dateCreated).toLocaleDateString('si-LK', {\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric',\n                  weekday: 'long'\n                })}\n              </div>\n              {horoscope.createdAt && (\n                <div style={{ color: '#aeb6bf', fontSize: '0.85rem', marginTop: '0.5rem' }}>\n                  ජනනය කළේ: {new Date(horoscope.createdAt).toLocaleString('si-LK')}\n                </div>\n              )}\n            </div>\n          ) : (\n            <p style={{ color: '#aeb6bf', marginBottom: '2rem' }}>\n              {getCurrentDate()}\n            </p>\n          )}\n        </div>\n\n        <div className=\"horoscope-section\">\n          <div style={{ marginBottom: '1.5rem' }}>\n            <h3 className=\"horoscope-title\" style={{ margin: 0 }}>අද දිනයේ රාශිඵල</h3>\n          </div>\n          \n          {lastUpdated && (\n            <div style={{ \n              fontSize: '0.85rem', \n              color: '#aeb6bf', \n              marginBottom: '1rem',\n              textAlign: 'center',\n              fontStyle: 'italic'\n            }}>\n              අවසන් වරට යාවත්කාලීන කළේ: {lastUpdated.toLocaleTimeString('si-LK', { \n                hour: '2-digit', \n                minute: '2-digit',\n                hour12: true\n              })}\n            </div>\n          )}\n          \n          {loading && (\n            <div className=\"loading\">\n              රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.\n            </div>\n          )}\n          \n          {refreshing && (\n            <div className=\"loading\">\n              නව රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.\n            </div>\n          )}\n          \n          {error && (\n            <div className=\"error\">\n              {error}\n              <button \n                onClick={handleRefresh}\n                style={{\n                  marginLeft: '1rem',\n                  background: 'rgba(231, 76, 60, 0.1)',\n                  border: '1px solid #e74c3c',\n                  color: '#e74c3c',\n                  padding: '0.4rem 0.8rem',\n                  borderRadius: '15px',\n                  cursor: 'pointer',\n                  fontSize: '0.8rem'\n                }}\n              >\n                නැවත උත්සාහ කරන්න\n              </button>\n            </div>\n          )}\n          \n          {!loading && !refreshing && !error && horoscope && (\n            <StructuredHoroscopeDisplay horoscope={horoscope} />\n          )}\n        </div>\n\n        <div className=\"controls\" style={{ marginTop: '2rem' }}>\n          <button \n            onClick={toggleSound}\n            className=\"sound-toggle\"\n            style={{\n              background: soundEnabled ? 'rgba(244, 208, 63, 0.2)' : 'rgba(255, 255, 255, 0.1)',\n              border: '1px solid #f4d03f',\n              color: '#f4d03f',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '25px',\n              cursor: 'pointer',\n              fontFamily: 'Noto Sans Sinhala, sans-serif',\n              transition: 'all 0.3s ease'\n            }}\n          >\n            {soundEnabled ? '🔊 ශබ්දය නිශ්ශබ්ද කරන්න' : '🔇 ශබ්දය සක්‍රිය කරන්න'}\n          </button>\n        </div>\n\n        <div className=\"spiritual-message\" style={{\n          marginTop: '3rem',\n          padding: '2rem',\n          background: 'rgba(244, 208, 63, 0.1)',\n          borderRadius: '15px',\n          border: '1px solid rgba(244, 208, 63, 0.3)',\n          textAlign: 'center'\n        }}>\n          <p style={{ color: '#f4d03f', fontStyle: 'italic', fontSize: '1.1rem' }}>\n            \"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා\"\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ZodiacPage;"], "mappings": "yHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CACvE,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CACrD,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,gBAAgB,KAAM,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,WAAW,CAAG,CAClBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,GAAG,CACXC,GAAG,CAAE,GAAG,CACRC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,GAAG,CACVC,OAAO,CAAE,GAAG,CACZC,WAAW,CAAE,GAAG,CAChBC,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAE,GACV,CAAC,CAED;AACA,KAAM,CAAAC,sCAAsC,CAAIC,OAAO,EAAK,CAC1D;AACA,GAAI,CAACA,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,CAC3C,MAAO,EAAE,CACX,CAEA;AACA,KAAM,CAAAC,SAAS,CAAGD,OAAO,CACtBE,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,UAAU,CAAE,EAAE,CAAC,CACvBC,IAAI,CAAC,CAAC,CAET,KAAM,CAAAC,UAAU,CAAG,CACjBC,IAAI,CAAE,CACJC,EAAE,CAAE,MAAM,CACVC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,KAAK,CAAE,WAAW,CAAE,QAAQ,CAAE,OAAO,CAAE,QAAQ,CAC5D,CAAC,CACDC,MAAM,CAAE,CACNN,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,gBAAgB,CACvBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,QAAQ,CAAE,MAAM,CAAE,QAAQ,CAAE,UAAU,CAAE,MAAM,CAC3D,CAAC,CACDE,MAAM,CAAE,CACNP,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,MAAM,CAAE,KAAK,CAAE,WAAW,CAAE,MAAM,CAAE,QAAQ,CACzD,CAAC,CACDG,OAAO,CAAE,CACPR,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,MAAM,CAAE,OAAO,CAAE,QAAQ,CAAE,OAAO,CAAE,OAAO,CACxD,CAAC,CACDI,OAAO,CAAE,CACPT,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,GAAG,CACVC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,QAAQ,CAAE,OAAO,CAAE,MAAM,CAC1D,CACF,CAAC,CAED;AACA,KAAM,CAAAK,KAAK,CAAGf,SAAS,CAACgB,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAAChB,IAAI,CAAC,CAAC,CAACiB,MAAM,CAAG,CAAC,CAAC,CAC1E,GAAI,CAAAC,eAAe,CAAG,IAAI,CAC1B,GAAI,CAAAC,aAAa,CAAG,EAAE,CAEtB;AACA,GAAIN,KAAK,CAACI,MAAM,CAAG,CAAC,CAAE,CACpB;AACAhB,UAAU,CAACW,OAAO,CAACL,OAAO,CAAGT,SAAS,CACxC,CAAC,IAAM,CACL;AACA,IAAK,GAAI,CAAAsB,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGP,KAAK,CAACI,MAAM,CAAEG,CAAC,EAAE,CAAE,CACrC,KAAM,CAAAJ,IAAI,CAAGH,KAAK,CAACO,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC,CAE5B,GAAI,CAACgB,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAG,CAAC,CAAE,SAE9B;AACA,GAAI,CAAAI,gBAAgB,CAAG,IAAI,CAE3B;AACA,KAAM,CAAAC,aAAa,CAAGN,IAAI,CAACO,KAAK,CAAC,UAAU,CAAC,CAC5C,GAAID,aAAa,CAAE,CACjB,KAAM,CAAAE,GAAG,CAAGC,QAAQ,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC,CACtC,KAAM,CAAAI,aAAa,CAAG,CAAC,MAAM,CAAE,QAAQ,CAAE,QAAQ,CAAE,SAAS,CAAE,SAAS,CAAC,CACxE,GAAIF,GAAG,EAAI,CAAC,EAAIA,GAAG,EAAI,CAAC,CAAE,CACxBH,gBAAgB,CAAGK,aAAa,CAACF,GAAG,CAAG,CAAC,CAAC,CAC3C,CACF,CAEA;AACA,GAAI,CAACH,gBAAgB,CAAE,CACrB,IAAK,KAAM,CAACM,KAAK,CAAEC,OAAO,CAAC,EAAI,CAAAC,MAAM,CAACC,OAAO,CAAC7B,UAAU,CAAC,CAAE,CACzD,IAAK,KAAM,CAAA8B,OAAO,GAAI,CAAAH,OAAO,CAACpB,QAAQ,CAAE,CACtC,GAAIQ,IAAI,CAACgB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,CAAE,CACtDX,gBAAgB,CAAGM,KAAK,CACxB,MACF,CACF,CACA,GAAIN,gBAAgB,CAAE,MACxB,CACF,CAEA;AACA,GAAIA,gBAAgB,EAAIA,gBAAgB,GAAKH,eAAe,CAAE,CAC5D,GAAIA,eAAe,EAAIC,aAAa,CAACF,MAAM,CAAG,CAAC,CAAE,CAC/ChB,UAAU,CAACiB,eAAe,CAAC,CAACX,OAAO,CAAGY,aAAa,CAACe,IAAI,CAAC,GAAG,CAAC,CAAClC,IAAI,CAAC,CAAC,CACtE,CACAkB,eAAe,CAAGG,gBAAgB,CAClCF,aAAa,CAAG,EAAE,CAElB;AACA,GAAI,CAAAgB,YAAY,CAAGnB,IAAI,CACpBjB,OAAO,CAAC,WAAW,CAAE,EAAE,CAAC,CACxBA,OAAO,CAAC,UAAU,CAAE,EAAE,CAAC,CACvBA,OAAO,CAAC,GAAI,CAAAqC,MAAM,CAACnC,UAAU,CAACoB,gBAAgB,CAAC,CAACjB,KAAK,CAAE,IAAI,CAAC,CAAE,EAAE,CAAC,CACjEL,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,CACjBC,IAAI,CAAC,CAAC,CAET,GAAImC,YAAY,CAAClB,MAAM,CAAG,CAAC,CAAE,CAC3BE,aAAa,CAACkB,IAAI,CAACF,YAAY,CAAC,CAClC,CACF,CAAC,IAAM,IAAIjB,eAAe,CAAE,CAC1B;AACA,GAAI,CAAAiB,YAAY,CAAGnB,IAAI,CAAChB,IAAI,CAAC,CAAC,CAC9B,GAAImC,YAAY,CAAClB,MAAM,CAAG,CAAC,CAAE,CAC3BE,aAAa,CAACkB,IAAI,CAACF,YAAY,CAAC,CAClC,CACF,CAAC,IAAM,CACL;AACAjB,eAAe,CAAG,SAAS,CAC3BC,aAAa,CAACkB,IAAI,CAACrB,IAAI,CAAChB,IAAI,CAAC,CAAC,CAAC,CAChC,CACH,CAEA;AACA,GAAI,CAAC6B,MAAM,CAACS,MAAM,CAACrC,UAAU,CAAC,CAACsC,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACjC,OAAO,CAAC,CAAE,CACvD,KAAM,CAAAkC,SAAS,CAAG3C,SAAS,CAACgB,KAAK,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC2B,CAAC,EAAIA,CAAC,CAAC1C,IAAI,CAAC,CAAC,CAACiB,MAAM,CAAG,EAAE,CAAC,CAC5E,KAAM,CAAA0B,eAAe,CAAGd,MAAM,CAACe,IAAI,CAAC3C,UAAU,CAAC,CAE/CwC,SAAS,CAACI,OAAO,CAAC,CAACC,QAAQ,CAAEC,KAAK,GAAK,CACrC,KAAM,CAAAC,aAAa,CAAGD,KAAK,CAAGJ,eAAe,CAAC1B,MAAM,CACpD,KAAM,CAAAgC,WAAW,CAAGN,eAAe,CAACK,aAAa,CAAC,CAClD,GAAI,CAAC/C,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,CAAE,CACpCN,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,CAAGuC,QAAQ,CAAC9C,IAAI,CAAC,CAAC,CACnD,CAAC,IAAM,CACLC,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,EAAI,GAAG,CAAGuC,QAAQ,CAAC9C,IAAI,CAAC,CAAC,CAC1D,CACF,CAAC,CAAC,CACJ,CACD,CAEA;AACA,GAAIkB,eAAe,EAAIC,aAAa,CAACF,MAAM,CAAG,CAAC,CAAE,CAC/ChB,UAAU,CAACiB,eAAe,CAAC,CAACX,OAAO,CAAGY,aAAa,CAACe,IAAI,CAAC,GAAG,CAAC,CAAClC,IAAI,CAAC,CAAC,CACtE,CAEA;AACD6B,MAAM,CAACS,MAAM,CAACrC,UAAU,CAAC,CAAC4C,OAAO,CAAC,CAACK,QAAQ,CAAEH,KAAK,GAAK,CACrD,GAAI,CAACG,QAAQ,CAAC3C,OAAO,EAAI2C,QAAQ,CAAC3C,OAAO,CAACU,MAAM,CAAG,CAAC,CAAE,CACpD;AACA,KAAM,CAAAwB,SAAS,CAAG3C,SAAS,CAACgB,KAAK,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC2B,CAAC,EAAIA,CAAC,CAAC1C,IAAI,CAAC,CAAC,CAACiB,MAAM,CAAG,CAAC,CAAC,CAC3E,GAAIwB,SAAS,CAACxB,MAAM,CAAG8B,KAAK,CAAE,CAC5BG,QAAQ,CAAC3C,OAAO,CAAGkC,SAAS,CAACM,KAAK,CAAC,CAAC/C,IAAI,CAAC,CAAC,EAAIF,SAAS,CAACqD,SAAS,CAACJ,KAAK,CAAG,EAAE,CAAE,CAACA,KAAK,CAAG,CAAC,EAAI,EAAE,CAAC,CAAC/C,IAAI,CAAC,CAAC,CACxG,CAAC,IAAM,CACL;AACA,KAAM,CAAAoD,cAAc,CAAG,CACrBlD,IAAI,CAAE,8DAA8D,CACpEO,MAAM,CAAE,yDAAyD,CACjEC,MAAM,CAAE,mDAAmD,CAC3DC,OAAO,CAAE,0DAA0D,CACnEC,OAAO,CAAE,2DACX,CAAC,CACDsC,QAAQ,CAAC3C,OAAO,CAAG6C,cAAc,CAACF,QAAQ,CAAC/C,EAAE,CAAC,EAAI,+CAA+C,CACnG,CACF,CACF,CAAC,CAAC,CAED;AACA,MAAO,CAAA0B,MAAM,CAACC,OAAO,CAAC7B,UAAU,CAAC,CAACoD,GAAG,CAACC,IAAA,MAAC,CAACC,GAAG,CAAEL,QAAQ,CAAC,CAAAI,IAAA,QAAAE,aAAA,CAAAA,aAAA,IACjDN,QAAQ,MACX/C,EAAE,CAAEoD,GAAI;AAAA,IACR,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAE,YAAY,CAAGC,KAAA,EAAyB,IAAxB,CAAER,QAAQ,CAAEH,KAAM,CAAC,CAAAW,KAAA,CACvC,KAAM,CAAAC,UAAU,CAAG,CACjBzD,IAAI,CAAE,CACJ0D,UAAU,CAAE,sFAAsF,CAClGC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,qCACV,CAAC,CACDrD,MAAM,CAAE,CACNmD,UAAU,CAAE,oFAAoF,CAChGC,MAAM,CAAE,mCAAmC,CAC3CC,MAAM,CAAE,oCACV,CAAC,CACDpD,MAAM,CAAE,CACNkD,UAAU,CAAE,oFAAoF,CAChGC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,mCACV,CAAC,CACDnD,OAAO,CAAE,CACPiD,UAAU,CAAE,kFAAkF,CAC9FC,MAAM,CAAE,kCAAkC,CAC1CC,MAAM,CAAE,mCACV,CAAC,CACDlD,OAAO,CAAE,CACPgD,UAAU,CAAE,sFAAsF,CAClGC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,qCACV,CACF,CAAC,CAED,KAAM,CAAAC,KAAK,CAAGJ,UAAU,CAACT,QAAQ,CAAC/C,EAAE,CAAC,EAAIwD,UAAU,CAAC/C,OAAO,CAE3D,mBACE9B,KAAA,QACEkF,SAAS,CAAC,yBAAyB,CACnCD,KAAK,CAAE,CACLE,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,MAAM,CACfN,UAAU,CAAEG,KAAK,CAACH,UAAU,CAC5BC,MAAM,CAAEE,KAAK,CAACF,MAAM,CACpBM,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAEL,KAAK,CAACD,MAAM,CACvBO,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,eAAe,CAC3BC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QACZ,CAAE,CACFC,YAAY,CAAGC,CAAC,EAAK,CACnBA,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACa,SAAS,CAAG,kBAAkB,CACpDF,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACK,SAAS,CAAGL,KAAK,CAACD,MAAM,CAAC/D,OAAO,CAAC,MAAM,CAAE,MAAM,CAAC,CACxE,CAAE,CACF8E,YAAY,CAAGH,CAAC,EAAK,CACnBA,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACa,SAAS,CAAG,eAAe,CACjDF,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACK,SAAS,CAAGL,KAAK,CAACD,MAAM,CAChD,CAAE,CAAAgB,QAAA,eAGFlG,IAAA,QACEmF,KAAK,CAAE,CACLQ,QAAQ,CAAE,UAAU,CACpBQ,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACbC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdtB,UAAU,4BAAAuB,MAAA,CAA6BpB,KAAK,CAACF,MAAM,CAAC9D,OAAO,CAAC,YAAY,CAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,CAAE,OAAO,CAAC,0BAAwB,CAC9HqF,cAAc,CAAE,WAAW,CAC3BC,OAAO,CAAE,GAAG,CACZC,aAAa,CAAE,MACjB,CAAE,CACH,CAAC,cAGFxG,KAAA,QACEiF,KAAK,CAAE,CACLwB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBvB,YAAY,CAAE,QAAQ,CACtBM,QAAQ,CAAE,UAAU,CACpBkB,MAAM,CAAE,CACV,CAAE,CAAAX,QAAA,eAEFlG,IAAA,QACEmF,KAAK,CAAE,CACL2B,QAAQ,CAAE,QAAQ,CAClBC,WAAW,CAAE,MAAM,CACnB5E,MAAM,CAAE,wCACV,CAAE,CAAA+D,QAAA,CAED5B,QAAQ,CAAC7C,KAAK,CACZ,CAAC,cACNvB,KAAA,QAAAgG,QAAA,eACElG,IAAA,OACEmF,KAAK,CAAE,CACL6B,KAAK,CAAE,SAAS,CAChBF,QAAQ,CAAE,QAAQ,CAClBG,MAAM,CAAE,CAAC,CACTC,UAAU,CAAE,+BAA+B,CAC3CC,UAAU,CAAE,KAAK,CACjBC,UAAU,CAAE,2BACd,CAAE,CAAAlB,QAAA,CAED5B,QAAQ,CAAC9C,KAAK,CACb,CAAC,cACLxB,IAAA,QACEmF,KAAK,CAAE,CACLkB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbtB,UAAU,CAAE,8CAA8C,CAC1DqC,SAAS,CAAE,QAAQ,CACnB9B,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,EACC,CAAC,EACH,CAAC,cAGNvF,IAAA,QACEmF,KAAK,CAAE,CACLQ,QAAQ,CAAE,UAAU,CACpBkB,MAAM,CAAE,CACV,CAAE,CAAAX,QAAA,cAEFlG,IAAA,MACEmF,KAAK,CAAE,CACL6B,KAAK,CAAE,SAAS,CAChBM,UAAU,CAAE,KAAK,CACjBR,QAAQ,CAAE,QAAQ,CAClBG,MAAM,CAAE,CAAC,CACTC,UAAU,CAAE,+BAA+B,CAC3CK,SAAS,CAAE,SAAS,CACpBH,UAAU,CAAE,2BACd,CAAE,CAAAlB,QAAA,CAED5B,QAAQ,CAAC3C,OAAO,CAChB,CAAC,CACD,CAAC,cAGN3B,IAAA,QACEmF,KAAK,CAAE,CACLQ,QAAQ,CAAE,UAAU,CACpB6B,MAAM,CAAE,CAAC,CACTC,IAAI,CAAE,CAAC,CACPrB,KAAK,CAAE,CAAC,CACRE,MAAM,CAAE,KAAK,CACbtB,UAAU,2BAAAuB,MAAA,CAA4BpB,KAAK,CAACF,MAAM,CAAC9D,OAAO,CAAC,YAAY,CAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,CAAE,MAAM,CAAC,kBAAgB,CACpHoE,YAAY,CAAE,eAChB,CAAE,CACH,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACD,KAAM,CAAAmC,0BAA0B,CAAGC,KAAA,EAAmB,IAAlB,CAAEC,SAAU,CAAC,CAAAD,KAAA,CAC/C,GAAI,CAAAtG,UAAU,CAEd;AACA,GAAIuG,SAAS,EAAIA,SAAS,CAACC,UAAU,EAAID,SAAS,CAACvG,UAAU,CAAE,CAC7D;AACA,GAAIyG,KAAK,CAACC,OAAO,CAACH,SAAS,CAACvG,UAAU,CAAC,CAAE,CACvCA,UAAU,CAAGuG,SAAS,CAACvG,UAAU,CACnC,CAAC,IAAM,CACL;AACAA,UAAU,CAAG4B,MAAM,CAACC,OAAO,CAAC0E,SAAS,CAACvG,UAAU,CAAC,CAACoD,GAAG,CAACuD,KAAA,MAAC,CAACrD,GAAG,CAAEL,QAAQ,CAAC,CAAA0D,KAAA,QAAApD,aAAA,CAAAA,aAAA,IACjEN,QAAQ,MACX/C,EAAE,CAAE+C,QAAQ,CAAC/C,EAAE,EAAIoD,GAAG,IACtB,CAAC,CACL,CACF,CAAC,IAAM,IAAI,MAAO,CAAAiD,SAAS,GAAK,QAAQ,CAAE,CACxC;AACAvG,UAAU,CAAGL,sCAAsC,CAAC4G,SAAS,CAAC,CAChE,CAAC,IAAM,CACLvG,UAAU,CAAG,EAAE,CACjB,CAEA;AACA,GAAI,CAACA,UAAU,EAAIA,UAAU,CAACgB,MAAM,GAAK,CAAC,CAAE,CAC1C,mBACErC,IAAA,QAAKmF,KAAK,CAAE,CACVoC,SAAS,CAAE,QAAQ,CACnBjC,OAAO,CAAE,MAAM,CACf0B,KAAK,CAAE,SAAS,CAChBE,UAAU,CAAE,+BACd,CAAE,CAAAhB,QAAA,CAAC,uNAEH,CAAK,CAAC,CAEV,CAEA,mBACElG,IAAA,QACEoF,SAAS,CAAC,8BAA8B,CACxCD,KAAK,CAAE,CACL8C,QAAQ,CAAE,OAAO,CACjBhB,MAAM,CAAE,QAAQ,CAChB3B,OAAO,CAAE,MACX,CAAE,CAAAY,QAAA,CAED7E,UAAU,CAACoD,GAAG,CAAC,CAACH,QAAQ,CAAEH,KAAK,gBAC9BnE,IAAA,CAAC6E,YAAY,EAEXP,QAAQ,CAAEA,QAAS,CACnBH,KAAK,CAAEA,KAAM,EAFRG,QAAQ,CAAC/C,EAAE,cAAAgF,MAAA,CAAgBpC,KAAK,CAGtC,CACF,CAAC,CACC,CAAC,CAEV,CAAC,CAED,KAAM,CAAA+D,UAAU,CAAGC,KAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,KAAA,CAC1B,KAAM,CAACP,SAAS,CAAES,YAAY,CAAC,CAAG/I,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgJ,OAAO,CAAEC,UAAU,CAAC,CAAGjJ,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACkJ,KAAK,CAAEC,QAAQ,CAAC,CAAGnJ,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACoJ,YAAY,CAAEC,eAAe,CAAC,CAAGrJ,QAAQ,CAAC,IAAI,CAAC,CAAE;AACxD,KAAM,CAACsJ,WAAW,CAAEC,cAAc,CAAC,CAAGvJ,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACwJ,UAAU,CAAEC,aAAa,CAAC,CAAGzJ,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAA0J,QAAQ,CAAGvJ,MAAM,CAAC,IAAI,CAAC,CAE7B,KAAM,CAAAwJ,cAAc,CAAGzJ,WAAW,CAAC,gBAAgC,IAAzB,CAAA0J,YAAY,CAAAC,SAAA,CAAA9G,MAAA,IAAA8G,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,CAC5D,GAAI,CACF,GAAID,YAAY,CAAE,CAChBH,aAAa,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACLR,UAAU,CAAC,IAAI,CAAC,CAClB,CACAE,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,GAAI,CAACS,YAAY,CAAE,CACjB,KAAM,CAAAG,eAAe,CAAGvJ,gBAAgB,CAACwJ,kBAAkB,CAAClB,IAAI,CAAC7G,EAAE,CAAC,CACpE,GAAI8H,eAAe,CAAE,CACnBhB,YAAY,CAACgB,eAAe,CAAC,CAC7BR,cAAc,CAAC,GAAI,CAAAU,IAAI,CAAC,CAAC,CAAC,CAC1BhB,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACF,CAEA;AACA,KAAM,CAAAiB,aAAa,CAAG,KAAM,CAAA1J,gBAAgB,CAAC2J,YAAY,CAACrB,IAAI,CAAC7G,EAAE,CAAE2H,YAAY,CAAC,CAEhF;AACA,GAAIM,aAAa,EAAIA,aAAa,CAACnI,UAAU,CAAE,CAC7C;AACA,KAAM,CAAAqI,cAAc,CAAG,CACrBpI,IAAI,CAAE,CAAEC,EAAE,CAAE,MAAM,CAAEC,KAAK,CAAE,mBAAmB,CAAEC,KAAK,CAAE,IAAK,CAAC,CAC7DI,MAAM,CAAE,CAAEN,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,gBAAgB,CAAEC,KAAK,CAAE,IAAK,CAAC,CAC9DK,MAAM,CAAE,CAAEP,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,qBAAqB,CAAEC,KAAK,CAAE,IAAK,CAAC,CACnEM,OAAO,CAAE,CAAER,EAAE,CAAE,SAAS,CAAEC,KAAK,CAAE,eAAe,CAAEC,KAAK,CAAE,IAAK,CAAC,CAC/DO,OAAO,CAAE,CAAET,EAAE,CAAE,SAAS,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,KAAK,CAAE,GAAI,CACjE,CAAC,CAED,KAAM,CAAAJ,UAAU,CAAG4B,MAAM,CAACC,OAAO,CAACsG,aAAa,CAACnI,UAAU,CAAC,CAACoD,GAAG,CAACkF,KAAA,MAAC,CAAChF,GAAG,CAAEhD,OAAO,CAAC,CAAAgI,KAAA,QAAA/E,aAAA,CAAAA,aAAA,IAC1E8E,cAAc,CAAC/E,GAAG,CAAC,MACtBhD,OAAO,CAAEA,OAAO,EAAI,mCAAmC,IACvD,CAAC,CAEH0G,YAAY,CAAC,CACXhH,UAAU,CACVwG,UAAU,CAAE,IAAI,CAChB+B,WAAW,CAAEJ,aAAa,CAACK,YAAY,CACvCC,SAAS,CAAEN,aAAa,CAACO,UAAU,CACnCC,UAAU,CAAER,aAAa,CAACS,WAC5B,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA5B,YAAY,CAACmB,aAAa,CAAC,CAC7B,CAEAX,cAAc,CAAC,GAAI,CAAAU,IAAI,CAAC,CAAC,CAAC,CAE1B;AACAzJ,gBAAgB,CAACoK,kBAAkB,CAAC9B,IAAI,CAAC7G,EAAE,CAAEiI,aAAa,CAAC,CAE7D,CAAE,MAAOW,GAAG,CAAE,CACZ1B,QAAQ,CAAC,gEAAgE,CAAC,CAC1E2B,OAAO,CAAC5B,KAAK,CAAC,2BAA2B,CAAE2B,GAAG,CAAC,CACjD,CAAC,OAAS,CACR5B,UAAU,CAAC,KAAK,CAAC,CACjBQ,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAAE,CAACX,IAAI,CAAC7G,EAAE,CAAC,CAAC,CAEbhC,SAAS,CAAC,IAAM,CACd0J,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAACA,cAAc,CAAC,CAAC,CAEpB;AACA1J,SAAS,CAAC,IAAM,CACd,GAAIyJ,QAAQ,CAACqB,OAAO,EAAI3B,YAAY,CAAE,CACpC;AACAM,QAAQ,CAACqB,OAAO,CAACC,IAAI,CAAG,IAAI,CAC5BtB,QAAQ,CAACqB,OAAO,CAACE,MAAM,CAAG,GAAG,CAAE;AAE/B;AACA,KAAM,CAAAC,WAAW,CAAGxB,QAAQ,CAACqB,OAAO,CAACI,IAAI,CAAC,CAAC,CAC3C,GAAID,WAAW,GAAKpB,SAAS,CAAE,CAC7BoB,WAAW,CAACE,KAAK,CAAClC,KAAK,EAAI,CACzB4B,OAAO,CAACO,GAAG,CAAC,0BAA0B,CAAEnC,KAAK,CAAC,CAC9C;AACF,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAE,CAACE,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAkC,aAAa,CAAGA,CAAA,GAAM,CAC1B3B,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED,KAAM,CAAA4B,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,aAAa,CAAG,CAACpC,YAAY,CACnCC,eAAe,CAACmC,aAAa,CAAC,CAE9B,GAAI9B,QAAQ,CAACqB,OAAO,CAAE,CACpB,GAAIS,aAAa,CAAE,CACjB9B,QAAQ,CAACqB,OAAO,CAACI,IAAI,CAAC,CAAC,CAACC,KAAK,CAACN,OAAO,CAAC5B,KAAK,CAAC,CAC9C,CAAC,IAAM,CACLQ,QAAQ,CAACqB,OAAO,CAACU,KAAK,CAAC,CAAC,CAC1B,CACF,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAA1B,IAAI,CAAC,CAAC,CACxB,KAAM,CAAA2B,OAAO,CAAG,CACdC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,OAAO,CAAE,MACX,CAAC,CACD,MAAO,CAAAL,KAAK,CAACM,kBAAkB,CAAC,OAAO,CAAEL,OAAO,CAAC,CACnD,CAAC,CAED,mBACEhL,KAAA,QAAKkF,SAAS,CAAC,aAAa,CAAAc,QAAA,eAE1BlG,IAAA,UACEwL,GAAG,CAAExC,QAAS,CACdyC,GAAG,CAAC,YAAY,CAChBnB,IAAI,MACJnF,KAAK,CAAE,CAAEwB,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,cAGF3G,IAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAc,QAAA,cAChClG,IAAA,QACEyL,GAAG,CAAC,UAAU,CACdC,GAAG,CAAC,iBAAiB,CACrBtG,SAAS,CAAC,WAAW,CACtB,CAAC,CACC,CAAC,cAENpF,IAAA,CAACL,kBAAkB,GAAE,CAAC,cACtBK,IAAA,CAACJ,cAAc,GAAE,CAAC,cAClBI,IAAA,CAACH,eAAe,GAAE,CAAC,cAEnBG,IAAA,CAACN,IAAI,EAACiM,EAAE,CAAC,GAAG,CAACvG,SAAS,CAAC,aAAa,CAAAc,QAAA,CAAC,sEAErC,CAAM,CAAC,cAEPhG,KAAA,QAAKkF,SAAS,CAAC,gBAAgB,CAAAc,QAAA,eAC7BhG,KAAA,QAAKkF,SAAS,CAAC,eAAe,CAAAc,QAAA,eAC5BlG,IAAA,QAAKoF,SAAS,CAAC,aAAa,CAACD,KAAK,CAAE,CAAE2B,QAAQ,CAAE,MAAM,CAAEzB,YAAY,CAAE,MAAO,CAAE,CAAAa,QAAA,CAC5E/F,WAAW,CAACiI,IAAI,CAAC7G,EAAE,CAAC,CAClB,CAAC,cACNvB,IAAA,OAAIoF,SAAS,CAAC,cAAc,CAAAc,QAAA,CAAEkC,IAAI,CAACwD,OAAO,CAAK,CAAC,cAChD1L,KAAA,OAAIkF,SAAS,CAAC,iBAAiB,CAAAc,QAAA,EAAEkC,IAAI,CAACyD,OAAO,CAAC,iCAAM,EAAI,CAAC,CAGxDjE,SAAS,EAAIA,SAAS,CAACgC,WAAW,cACjC1J,KAAA,QAAKiF,KAAK,CAAE,CACVH,UAAU,CAAE,0BAA0B,CACtCC,MAAM,CAAE,mCAAmC,CAC3CM,YAAY,CAAE,MAAM,CACpBD,OAAO,CAAE,MAAM,CACfD,YAAY,CAAE,MAAM,CACpBkC,SAAS,CAAE,QACb,CAAE,CAAArB,QAAA,eACAlG,IAAA,QAAKmF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAEF,QAAQ,CAAE,QAAQ,CAAEK,UAAU,CAAE,MAAM,CAAE9B,YAAY,CAAE,QAAS,CAAE,CAAAa,QAAA,CAAC,4EAElG,CAAK,CAAC,cACNlG,IAAA,QAAKmF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAEF,QAAQ,CAAE,MAAO,CAAE,CAAAZ,QAAA,CAChD,GAAI,CAAAqD,IAAI,CAAC3B,SAAS,CAACgC,WAAW,CAAC,CAAC2B,kBAAkB,CAAC,OAAO,CAAE,CAC3DJ,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,OAAO,CAAE,MACX,CAAC,CAAC,CACC,CAAC,CACL1D,SAAS,CAACkC,SAAS,eAClB5J,KAAA,QAAKiF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAEF,QAAQ,CAAE,SAAS,CAAEO,SAAS,CAAE,QAAS,CAAE,CAAAnB,QAAA,EAAC,+CAChE,CAAC,GAAI,CAAAqD,IAAI,CAAC3B,SAAS,CAACkC,SAAS,CAAC,CAACgC,cAAc,CAAC,OAAO,CAAC,EAC7D,CACN,EACE,CAAC,cAEN9L,IAAA,MAAGmF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAE3B,YAAY,CAAE,MAAO,CAAE,CAAAa,QAAA,CAClD8E,cAAc,CAAC,CAAC,CAChB,CACJ,EACE,CAAC,cAEN9K,KAAA,QAAKkF,SAAS,CAAC,mBAAmB,CAAAc,QAAA,eAChClG,IAAA,QAAKmF,KAAK,CAAE,CAAEE,YAAY,CAAE,QAAS,CAAE,CAAAa,QAAA,cACrClG,IAAA,OAAIoF,SAAS,CAAC,iBAAiB,CAACD,KAAK,CAAE,CAAE8B,MAAM,CAAE,CAAE,CAAE,CAAAf,QAAA,CAAC,kFAAe,CAAI,CAAC,CACvE,CAAC,CAEL0C,WAAW,eACV1I,KAAA,QAAKiF,KAAK,CAAE,CACV2B,QAAQ,CAAE,SAAS,CACnBE,KAAK,CAAE,SAAS,CAChB3B,YAAY,CAAE,MAAM,CACpBkC,SAAS,CAAE,QAAQ,CACnBwE,SAAS,CAAE,QACb,CAAE,CAAA7F,QAAA,EAAC,qIACyB,CAAC0C,WAAW,CAACoD,kBAAkB,CAAC,OAAO,CAAE,CACjEC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,IACV,CAAC,CAAC,EACC,CACN,CAEA7D,OAAO,eACNtI,IAAA,QAAKoF,SAAS,CAAC,SAAS,CAAAc,QAAA,CAAC,uNAEzB,CAAK,CACN,CAEA4C,UAAU,eACT9I,IAAA,QAAKoF,SAAS,CAAC,SAAS,CAAAc,QAAA,CAAC,oOAEzB,CAAK,CACN,CAEAsC,KAAK,eACJtI,KAAA,QAAKkF,SAAS,CAAC,OAAO,CAAAc,QAAA,EACnBsC,KAAK,cACNxI,IAAA,WACEoM,OAAO,CAAExB,aAAc,CACvBzF,KAAK,CAAE,CACLkH,UAAU,CAAE,MAAM,CAClBrH,UAAU,CAAE,wBAAwB,CACpCC,MAAM,CAAE,mBAAmB,CAC3B+B,KAAK,CAAE,SAAS,CAChB1B,OAAO,CAAE,eAAe,CACxBC,YAAY,CAAE,MAAM,CACpB+G,MAAM,CAAE,SAAS,CACjBxF,QAAQ,CAAE,QACZ,CAAE,CAAAZ,QAAA,CACH,8FAED,CAAQ,CAAC,EACN,CACN,CAEA,CAACoC,OAAO,EAAI,CAACQ,UAAU,EAAI,CAACN,KAAK,EAAIZ,SAAS,eAC7C5H,IAAA,CAAC0H,0BAA0B,EAACE,SAAS,CAAEA,SAAU,CAAE,CACpD,EACE,CAAC,cAEN5H,IAAA,QAAKoF,SAAS,CAAC,UAAU,CAACD,KAAK,CAAE,CAAEkC,SAAS,CAAE,MAAO,CAAE,CAAAnB,QAAA,cACrDlG,IAAA,WACEoM,OAAO,CAAEvB,WAAY,CACrBzF,SAAS,CAAC,cAAc,CACxBD,KAAK,CAAE,CACLH,UAAU,CAAE0D,YAAY,CAAG,yBAAyB,CAAG,0BAA0B,CACjFzD,MAAM,CAAE,mBAAmB,CAC3B+B,KAAK,CAAE,SAAS,CAChB1B,OAAO,CAAE,eAAe,CACxBC,YAAY,CAAE,MAAM,CACpB+G,MAAM,CAAE,SAAS,CACjBpF,UAAU,CAAE,+BAA+B,CAC3CxB,UAAU,CAAE,eACd,CAAE,CAAAQ,QAAA,CAEDwC,YAAY,CAAG,yBAAyB,CAAG,wBAAwB,CAC9D,CAAC,CACN,CAAC,cAEN1I,IAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAACD,KAAK,CAAE,CACxCkC,SAAS,CAAE,MAAM,CACjB/B,OAAO,CAAE,MAAM,CACfN,UAAU,CAAE,yBAAyB,CACrCO,YAAY,CAAE,MAAM,CACpBN,MAAM,CAAE,mCAAmC,CAC3CsC,SAAS,CAAE,QACb,CAAE,CAAArB,QAAA,cACAlG,IAAA,MAAGmF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAE+E,SAAS,CAAE,QAAQ,CAAEjF,QAAQ,CAAE,QAAS,CAAE,CAAAZ,QAAA,CAAC,2MAEzE,CAAG,CAAC,CACD,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAgC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}