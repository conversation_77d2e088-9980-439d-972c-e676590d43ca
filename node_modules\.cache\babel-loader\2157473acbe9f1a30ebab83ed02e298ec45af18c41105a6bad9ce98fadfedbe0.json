{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\n\n/**\n * Determines whether the payload is an error thrown by Axios\n *\n * @param {*} payload The value to test\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\nmodule.exports = function isAxiosError(payload) {\n  return utils.isObject(payload) && payload.isAxiosError === true;\n};", "map": {"version": 3, "names": ["utils", "require", "module", "exports", "isAxiosError", "payload", "isObject"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/helpers/isAxiosError.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\n\n/**\n * Determines whether the payload is an error thrown by <PERSON>xios\n *\n * @param {*} payload The value to test\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\nmodule.exports = function isAxiosError(payload) {\n  return utils.isObject(payload) && (payload.isAxiosError === true);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC9C,OAAOL,KAAK,CAACM,QAAQ,CAACD,OAAO,CAAC,IAAKA,OAAO,CAACD,YAAY,KAAK,IAAK;AACnE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}