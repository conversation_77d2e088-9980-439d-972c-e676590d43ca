{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\KuberaAnimation.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KuberaAnimation = () => {\n  _s();\n  const kuberaRef = useRef(null);\n  useEffect(() => {\n    const kubera = kuberaRef.current;\n    if (!kubera) return;\n\n    // Add floating animation with divine glow\n    let glowIntensity = 0;\n    let glowDirection = 1;\n    const animateGlow = () => {\n      glowIntensity += glowDirection * 0.02;\n      if (glowIntensity >= 1) {\n        glowDirection = -1;\n      } else if (glowIntensity <= 0) {\n        glowDirection = 1;\n      }\n      const shadowIntensity = 20 + glowIntensity * 30;\n      const opacity = 0.3 + glowIntensity * 0.4;\n      kubera.style.filter = `\n        drop-shadow(0 0 ${shadowIntensity}px rgba(244, 208, 63, ${opacity}))\n        drop-shadow(0 0 ${shadowIntensity * 1.5}px rgba(244, 208, 63, ${opacity * 0.6}))\n        brightness(${1 + glowIntensity * 0.3})\n      `;\n      requestAnimationFrame(animateGlow);\n    };\n    animateGlow();\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '10%',\n      right: '5%',\n      zIndex: 1,\n      pointerEvents: 'none',\n      opacity: 0.6\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: kuberaRef,\n      style: {\n        width: '120px',\n        height: '120px',\n        animation: 'kuberaFloat 4s ease-in-out infinite',\n        transformOrigin: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"svg\", {\n        width: \"120\",\n        height: \"120\",\n        viewBox: \"0 0 120 120\",\n        fill: \"none\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: \"60\",\n          cy: \"60\",\n          r: \"55\",\n          fill: \"url(#divineAura)\",\n          opacity: \"0.3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n          cx: \"60\",\n          cy: \"75\",\n          rx: \"25\",\n          ry: \"35\",\n          fill: \"url(#bodyGradient)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: \"60\",\n          cy: \"45\",\n          r: \"18\",\n          fill: \"url(#skinGradient)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n          d: \"M42 35 L60 25 L78 35 L75 45 L45 45 Z\",\n          fill: \"url(#crownGradient)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: \"55\",\n          cy: \"42\",\n          r: \"2\",\n          fill: \"#2c3e50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: \"65\",\n          cy: \"42\",\n          r: \"2\",\n          fill: \"#2c3e50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: \"60\",\n          cy: \"38\",\n          r: \"1.5\",\n          fill: \"url(#thirdEyeGradient)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n          cx: \"40\",\n          cy: \"65\",\n          rx: \"8\",\n          ry: \"20\",\n          fill: \"url(#skinGradient)\",\n          transform: \"rotate(-20 40 65)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n          cx: \"80\",\n          cy: \"65\",\n          rx: \"8\",\n          ry: \"20\",\n          fill: \"url(#skinGradient)\",\n          transform: \"rotate(20 80 65)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ellipse\", {\n          cx: \"45\",\n          cy: \"70\",\n          rx: \"6\",\n          ry: \"8\",\n          fill: \"url(#potGradient)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          cx: \"75\",\n          cy: \"70\",\n          r: \"5\",\n          fill: \"url(#skinGradient)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"g\", {\n          opacity: \"0.6\",\n          children: [/*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"60\",\n            y1: \"20\",\n            x2: \"60\",\n            y2: \"10\",\n            stroke: \"#f4d03f\",\n            strokeWidth: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"75\",\n            y1: \"25\",\n            x2: \"82\",\n            y2: \"18\",\n            stroke: \"#f4d03f\",\n            strokeWidth: \"1.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"85\",\n            y1: \"40\",\n            x2: \"92\",\n            y2: \"40\",\n            stroke: \"#f4d03f\",\n            strokeWidth: \"1.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"45\",\n            y1: \"25\",\n            x2: \"38\",\n            y2: \"18\",\n            stroke: \"#f4d03f\",\n            strokeWidth: \"1.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"line\", {\n            x1: \"35\",\n            y1: \"40\",\n            x2: \"28\",\n            y2: \"40\",\n            stroke: \"#f4d03f\",\n            strokeWidth: \"1.5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"defs\", {\n          children: [/*#__PURE__*/_jsxDEV(\"radialGradient\", {\n            id: \"divineAura\",\n            cx: \"50%\",\n            cy: \"50%\",\n            r: \"50%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#f4d03f\",\n              stopOpacity: \"0.2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"70%\",\n              stopColor: \"#f39c12\",\n              stopOpacity: \"0.1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"transparent\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n            id: \"bodyGradient\",\n            x1: \"0%\",\n            y1: \"0%\",\n            x2: \"0%\",\n            y2: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#e67e22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#d35400\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n            id: \"skinGradient\",\n            x1: \"0%\",\n            y1: \"0%\",\n            x2: \"0%\",\n            y2: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#f39c12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#e67e22\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n            id: \"crownGradient\",\n            x1: \"0%\",\n            y1: \"0%\",\n            x2: \"0%\",\n            y2: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#f4d03f\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#f39c12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"radialGradient\", {\n            id: \"thirdEyeGradient\",\n            cx: \"50%\",\n            cy: \"50%\",\n            r: \"50%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#fff\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#f4d03f\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"linearGradient\", {\n            id: \"potGradient\",\n            x1: \"0%\",\n            y1: \"0%\",\n            x2: \"0%\",\n            y2: \"100%\",\n            children: [/*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"0%\",\n              stopColor: \"#f4d03f\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"stop\", {\n              offset: \"100%\",\n              stopColor: \"#d4ac0d\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '20px',\n        left: '20px',\n        width: '80px',\n        height: '80px',\n        pointerEvents: 'none'\n      },\n      children: [...Array(5)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          width: '8px',\n          height: '8px',\n          background: 'radial-gradient(circle, #f4d03f 0%, #d4ac0d 100%)',\n          borderRadius: '50%',\n          animation: `floatingCoin${index} ${3 + index}s ease-in-out infinite`,\n          animationDelay: `${index * 0.5}s`,\n          left: `${20 + index * 15}px`,\n          top: `${30 + index * 10}px`\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes kuberaFloat {\n          0%, 100% {\n            transform: translateY(0px) rotate(0deg);\n          }\n          50% {\n            transform: translateY(-15px) rotate(2deg);\n          }\n        }\n        \n        @keyframes floatingCoin0 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-20px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin1 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-25px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin2 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-18px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin3 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-22px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin4 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-16px) rotate(180deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 214,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(KuberaAnimation, \"kRqLdTDQa7qwW5igilYBCbSN05o=\");\n_c = KuberaAnimation;\nexport default KuberaAnimation;\nvar _c;\n$RefreshReg$(_c, \"KuberaAnimation\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "KuberaAnimation", "_s", "kuberaRef", "kubera", "current", "glowIntensity", "glowDirection", "animateGlow", "shadowIntensity", "opacity", "style", "filter", "requestAnimationFrame", "position", "top", "right", "zIndex", "pointerEvents", "children", "ref", "width", "height", "animation", "transform<PERSON><PERSON>in", "viewBox", "fill", "xmlns", "cx", "cy", "r", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rx", "ry", "d", "transform", "x1", "y1", "x2", "y2", "stroke", "strokeWidth", "id", "offset", "stopColor", "stopOpacity", "left", "Array", "map", "_", "index", "background", "borderRadius", "animationDelay", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/KuberaAnimation.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst KuberaAnimation = () => {\n  const kuberaRef = useRef(null);\n\n  useEffect(() => {\n    const kubera = kuberaRef.current;\n    if (!kubera) return;\n\n    // Add floating animation with divine glow\n    let glowIntensity = 0;\n    let glowDirection = 1;\n    \n    const animateGlow = () => {\n      glowIntensity += glowDirection * 0.02;\n      \n      if (glowIntensity >= 1) {\n        glowDirection = -1;\n      } else if (glowIntensity <= 0) {\n        glowDirection = 1;\n      }\n      \n      const shadowIntensity = 20 + (glowIntensity * 30);\n      const opacity = 0.3 + (glowIntensity * 0.4);\n      \n      kubera.style.filter = `\n        drop-shadow(0 0 ${shadowIntensity}px rgba(244, 208, 63, ${opacity}))\n        drop-shadow(0 0 ${shadowIntensity * 1.5}px rgba(244, 208, 63, ${opacity * 0.6}))\n        brightness(${1 + glowIntensity * 0.3})\n      `;\n      \n      requestAnimationFrame(animateGlow);\n    };\n    \n    animateGlow();\n  }, []);\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        top: '10%',\n        right: '5%',\n        zIndex: 1,\n        pointerEvents: 'none',\n        opacity: 0.6\n      }}\n    >\n      <div\n        ref={kuberaRef}\n        style={{\n          width: '120px',\n          height: '120px',\n          animation: 'kuberaFloat 4s ease-in-out infinite',\n          transformOrigin: 'center'\n        }}\n      >\n        {/* Kubera God SVG */}\n        <svg\n          width=\"120\"\n          height=\"120\"\n          viewBox=\"0 0 120 120\"\n          fill=\"none\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          {/* Divine Aura */}\n          <circle\n            cx=\"60\"\n            cy=\"60\"\n            r=\"55\"\n            fill=\"url(#divineAura)\"\n            opacity=\"0.3\"\n          />\n          \n          {/* Kubera's Body */}\n          <ellipse\n            cx=\"60\"\n            cy=\"75\"\n            rx=\"25\"\n            ry=\"35\"\n            fill=\"url(#bodyGradient)\"\n          />\n          \n          {/* Kubera's Head */}\n          <circle\n            cx=\"60\"\n            cy=\"45\"\n            r=\"18\"\n            fill=\"url(#skinGradient)\"\n          />\n          \n          {/* Crown */}\n          <path\n            d=\"M42 35 L60 25 L78 35 L75 45 L45 45 Z\"\n            fill=\"url(#crownGradient)\"\n          />\n          \n          {/* Eyes */}\n          <circle cx=\"55\" cy=\"42\" r=\"2\" fill=\"#2c3e50\" />\n          <circle cx=\"65\" cy=\"42\" r=\"2\" fill=\"#2c3e50\" />\n          \n          {/* Third Eye */}\n          <circle cx=\"60\" cy=\"38\" r=\"1.5\" fill=\"url(#thirdEyeGradient)\" />\n          \n          {/* Arms */}\n          <ellipse\n            cx=\"40\"\n            cy=\"65\"\n            rx=\"8\"\n            ry=\"20\"\n            fill=\"url(#skinGradient)\"\n            transform=\"rotate(-20 40 65)\"\n          />\n          <ellipse\n            cx=\"80\"\n            cy=\"65\"\n            rx=\"8\"\n            ry=\"20\"\n            fill=\"url(#skinGradient)\"\n            transform=\"rotate(20 80 65)\"\n          />\n          \n          {/* Money Pot */}\n          <ellipse\n            cx=\"45\"\n            cy=\"70\"\n            rx=\"6\"\n            ry=\"8\"\n            fill=\"url(#potGradient)\"\n          />\n          \n          {/* Blessing Hand */}\n          <circle\n            cx=\"75\"\n            cy=\"70\"\n            r=\"5\"\n            fill=\"url(#skinGradient)\"\n          />\n          \n          {/* Divine Light Rays */}\n          <g opacity=\"0.6\">\n            <line x1=\"60\" y1=\"20\" x2=\"60\" y2=\"10\" stroke=\"#f4d03f\" strokeWidth=\"2\" />\n            <line x1=\"75\" y1=\"25\" x2=\"82\" y2=\"18\" stroke=\"#f4d03f\" strokeWidth=\"1.5\" />\n            <line x1=\"85\" y1=\"40\" x2=\"92\" y2=\"40\" stroke=\"#f4d03f\" strokeWidth=\"1.5\" />\n            <line x1=\"45\" y1=\"25\" x2=\"38\" y2=\"18\" stroke=\"#f4d03f\" strokeWidth=\"1.5\" />\n            <line x1=\"35\" y1=\"40\" x2=\"28\" y2=\"40\" stroke=\"#f4d03f\" strokeWidth=\"1.5\" />\n          </g>\n          \n          {/* Gradients */}\n          <defs>\n            <radialGradient id=\"divineAura\" cx=\"50%\" cy=\"50%\" r=\"50%\">\n              <stop offset=\"0%\" stopColor=\"#f4d03f\" stopOpacity=\"0.2\" />\n              <stop offset=\"70%\" stopColor=\"#f39c12\" stopOpacity=\"0.1\" />\n              <stop offset=\"100%\" stopColor=\"transparent\" />\n            </radialGradient>\n            \n            <linearGradient id=\"bodyGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#e67e22\" />\n              <stop offset=\"100%\" stopColor=\"#d35400\" />\n            </linearGradient>\n            \n            <linearGradient id=\"skinGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#f39c12\" />\n              <stop offset=\"100%\" stopColor=\"#e67e22\" />\n            </linearGradient>\n            \n            <linearGradient id=\"crownGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#f4d03f\" />\n              <stop offset=\"100%\" stopColor=\"#f39c12\" />\n            </linearGradient>\n            \n            <radialGradient id=\"thirdEyeGradient\" cx=\"50%\" cy=\"50%\" r=\"50%\">\n              <stop offset=\"0%\" stopColor=\"#fff\" />\n              <stop offset=\"100%\" stopColor=\"#f4d03f\" />\n            </radialGradient>\n            \n            <linearGradient id=\"potGradient\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#f4d03f\" />\n              <stop offset=\"100%\" stopColor=\"#d4ac0d\" />\n            </linearGradient>\n          </defs>\n        </svg>\n      </div>\n      \n      {/* Floating coins animation */}\n      <div\n        style={{\n          position: 'absolute',\n          top: '20px',\n          left: '20px',\n          width: '80px',\n          height: '80px',\n          pointerEvents: 'none'\n        }}\n      >\n        {[...Array(5)].map((_, index) => (\n          <div\n            key={index}\n            style={{\n              position: 'absolute',\n              width: '8px',\n              height: '8px',\n              background: 'radial-gradient(circle, #f4d03f 0%, #d4ac0d 100%)',\n              borderRadius: '50%',\n              animation: `floatingCoin${index} ${3 + index}s ease-in-out infinite`,\n              animationDelay: `${index * 0.5}s`,\n              left: `${20 + index * 15}px`,\n              top: `${30 + index * 10}px`\n            }}\n          />\n        ))}\n      </div>\n      \n      <style jsx>{`\n        @keyframes kuberaFloat {\n          0%, 100% {\n            transform: translateY(0px) rotate(0deg);\n          }\n          50% {\n            transform: translateY(-15px) rotate(2deg);\n          }\n        }\n        \n        @keyframes floatingCoin0 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-20px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin1 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-25px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin2 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-18px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin3 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-22px) rotate(180deg); }\n        }\n        \n        @keyframes floatingCoin4 {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-16px) rotate(180deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default KuberaAnimation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,SAAS,GAAGL,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACd,MAAMO,MAAM,GAAGD,SAAS,CAACE,OAAO;IAChC,IAAI,CAACD,MAAM,EAAE;;IAEb;IACA,IAAIE,aAAa,GAAG,CAAC;IACrB,IAAIC,aAAa,GAAG,CAAC;IAErB,MAAMC,WAAW,GAAGA,CAAA,KAAM;MACxBF,aAAa,IAAIC,aAAa,GAAG,IAAI;MAErC,IAAID,aAAa,IAAI,CAAC,EAAE;QACtBC,aAAa,GAAG,CAAC,CAAC;MACpB,CAAC,MAAM,IAAID,aAAa,IAAI,CAAC,EAAE;QAC7BC,aAAa,GAAG,CAAC;MACnB;MAEA,MAAME,eAAe,GAAG,EAAE,GAAIH,aAAa,GAAG,EAAG;MACjD,MAAMI,OAAO,GAAG,GAAG,GAAIJ,aAAa,GAAG,GAAI;MAE3CF,MAAM,CAACO,KAAK,CAACC,MAAM,GAAG;AAC5B,0BAA0BH,eAAe,yBAAyBC,OAAO;AACzE,0BAA0BD,eAAe,GAAG,GAAG,yBAAyBC,OAAO,GAAG,GAAG;AACrF,qBAAqB,CAAC,GAAGJ,aAAa,GAAG,GAAG;AAC5C,OAAO;MAEDO,qBAAqB,CAACL,WAAW,CAAC;IACpC,CAAC;IAEDA,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,oBACER,OAAA;IACEW,KAAK,EAAE;MACLG,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,KAAK;MACVC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,CAAC;MACTC,aAAa,EAAE,MAAM;MACrBR,OAAO,EAAE;IACX,CAAE;IAAAS,QAAA,gBAEFnB,OAAA;MACEoB,GAAG,EAAEjB,SAAU;MACfQ,KAAK,EAAE;QACLU,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,OAAO;QACfC,SAAS,EAAE,qCAAqC;QAChDC,eAAe,EAAE;MACnB,CAAE;MAAAL,QAAA,eAGFnB,OAAA;QACEqB,KAAK,EAAC,KAAK;QACXC,MAAM,EAAC,KAAK;QACZG,OAAO,EAAC,aAAa;QACrBC,IAAI,EAAC,MAAM;QACXC,KAAK,EAAC,4BAA4B;QAAAR,QAAA,gBAGlCnB,OAAA;UACE4B,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPC,CAAC,EAAC,IAAI;UACNJ,IAAI,EAAC,kBAAkB;UACvBhB,OAAO,EAAC;QAAK;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eAGFlC,OAAA;UACE4B,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPM,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPV,IAAI,EAAC;QAAoB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFlC,OAAA;UACE4B,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPC,CAAC,EAAC,IAAI;UACNJ,IAAI,EAAC;QAAoB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFlC,OAAA;UACEqC,CAAC,EAAC,sCAAsC;UACxCX,IAAI,EAAC;QAAqB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAGFlC,OAAA;UAAQ4B,EAAE,EAAC,IAAI;UAACC,EAAE,EAAC,IAAI;UAACC,CAAC,EAAC,GAAG;UAACJ,IAAI,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/ClC,OAAA;UAAQ4B,EAAE,EAAC,IAAI;UAACC,EAAE,EAAC,IAAI;UAACC,CAAC,EAAC,GAAG;UAACJ,IAAI,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG/ClC,OAAA;UAAQ4B,EAAE,EAAC,IAAI;UAACC,EAAE,EAAC,IAAI;UAACC,CAAC,EAAC,KAAK;UAACJ,IAAI,EAAC;QAAwB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhElC,OAAA;UACE4B,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPM,EAAE,EAAC,GAAG;UACNC,EAAE,EAAC,IAAI;UACPV,IAAI,EAAC,oBAAoB;UACzBY,SAAS,EAAC;QAAmB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACFlC,OAAA;UACE4B,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPM,EAAE,EAAC,GAAG;UACNC,EAAE,EAAC,IAAI;UACPV,IAAI,EAAC,oBAAoB;UACzBY,SAAS,EAAC;QAAkB;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAGFlC,OAAA;UACE4B,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPM,EAAE,EAAC,GAAG;UACNC,EAAE,EAAC,GAAG;UACNV,IAAI,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGFlC,OAAA;UACE4B,EAAE,EAAC,IAAI;UACPC,EAAE,EAAC,IAAI;UACPC,CAAC,EAAC,GAAG;UACLJ,IAAI,EAAC;QAAoB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAGFlC,OAAA;UAAGU,OAAO,EAAC,KAAK;UAAAS,QAAA,gBACdnB,OAAA;YAAMuC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAG;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzElC,OAAA;YAAMuC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3ElC,OAAA;YAAMuC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3ElC,OAAA;YAAMuC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3ElC,OAAA;YAAMuC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,MAAM,EAAC,SAAS;YAACC,WAAW,EAAC;UAAK;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAGJlC,OAAA;UAAAmB,QAAA,gBACEnB,OAAA;YAAgB6C,EAAE,EAAC,YAAY;YAACjB,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,CAAC,EAAC,KAAK;YAAAX,QAAA,gBACvDnB,OAAA;cAAM8C,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DlC,OAAA;cAAM8C,MAAM,EAAC,KAAK;cAACC,SAAS,EAAC,SAAS;cAACC,WAAW,EAAC;YAAK;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DlC,OAAA;cAAM8C,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC;YAAa;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,eAEjBlC,OAAA;YAAgB6C,EAAE,EAAC,cAAc;YAACN,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,MAAM;YAAAvB,QAAA,gBACjEnB,OAAA;cAAM8C,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxClC,OAAA;cAAM8C,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjBlC,OAAA;YAAgB6C,EAAE,EAAC,cAAc;YAACN,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,MAAM;YAAAvB,QAAA,gBACjEnB,OAAA;cAAM8C,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxClC,OAAA;cAAM8C,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjBlC,OAAA;YAAgB6C,EAAE,EAAC,eAAe;YAACN,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,MAAM;YAAAvB,QAAA,gBAClEnB,OAAA;cAAM8C,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxClC,OAAA;cAAM8C,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjBlC,OAAA;YAAgB6C,EAAE,EAAC,kBAAkB;YAACjB,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,CAAC,EAAC,KAAK;YAAAX,QAAA,gBAC7DnB,OAAA;cAAM8C,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC;YAAM;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrClC,OAAA;cAAM8C,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEjBlC,OAAA;YAAgB6C,EAAE,EAAC,aAAa;YAACN,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,MAAM;YAAAvB,QAAA,gBAChEnB,OAAA;cAAM8C,MAAM,EAAC,IAAI;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxClC,OAAA;cAAM8C,MAAM,EAAC,MAAM;cAACC,SAAS,EAAC;YAAS;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlC,OAAA;MACEW,KAAK,EAAE;QACLG,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXkC,IAAI,EAAE,MAAM;QACZ5B,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdJ,aAAa,EAAE;MACjB,CAAE;MAAAC,QAAA,EAED,CAAC,GAAG+B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC1BrD,OAAA;QAEEW,KAAK,EAAE;UACLG,QAAQ,EAAE,UAAU;UACpBO,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,KAAK;UACbgC,UAAU,EAAE,mDAAmD;UAC/DC,YAAY,EAAE,KAAK;UACnBhC,SAAS,EAAE,eAAe8B,KAAK,IAAI,CAAC,GAAGA,KAAK,wBAAwB;UACpEG,cAAc,EAAE,GAAGH,KAAK,GAAG,GAAG,GAAG;UACjCJ,IAAI,EAAE,GAAG,EAAE,GAAGI,KAAK,GAAG,EAAE,IAAI;UAC5BtC,GAAG,EAAE,GAAG,EAAE,GAAGsC,KAAK,GAAG,EAAE;QACzB;MAAE,GAXGA,KAAK;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENlC,OAAA;MAAOyD,GAAG;MAAAtC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAChC,EAAA,CAxPID,eAAe;AAAAyD,EAAA,GAAfzD,eAAe;AA0PrB,eAAeA,eAAe;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}