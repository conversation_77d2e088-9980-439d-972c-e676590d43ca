#!/bin/bash

# SSH Fix and Deployment Script
# Attempts to fix SSH connectivity issues and deploy

set -e

echo "🔧 SSH Troubleshooting and Deployment Script"
echo "============================================="

# Configuration
SERVER_HOST="kubera.help"
SERVER_USER="ubuntu"
APP_DIR="/var/www/kubera.help"
LOCAL_BUILD_DIR="./build"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }
echo_step() { echo -e "${YELLOW}🔄 $1${NC}"; }

# Find SSH keys
find_ssh_keys() {
    echo_step "Finding SSH keys..."
    
    SSH_KEYS=()
    for key in kubera_wsl.pem kubera.pem kubera_temp.pem; do
        if [ -f "$key" ]; then
            SSH_KEYS+=("$key")
            echo_info "Found key: $key"
        fi
    done
    
    if [ ${#SSH_KEYS[@]} -eq 0 ]; then
        echo_error "No SSH keys found!"
        exit 1
    fi
}

# Fix SSH key permissions and format
fix_ssh_key() {
    local key_file="$1"
    echo_step "Fixing SSH key: $key_file"
    
    # Copy to WSL temp location
    local wsl_key="/tmp/kubera_fixed.pem"
    cp "$key_file" "$wsl_key"
    
    # Fix permissions
    chmod 600 "$wsl_key"
    
    # Check if key needs format conversion
    if ! ssh-keygen -l -f "$wsl_key" >/dev/null 2>&1; then
        echo_info "Converting key format..."
        ssh-keygen -p -m PEM -f "$wsl_key" -N "" >/dev/null 2>&1 || true
    fi
    
    echo "$wsl_key"
}

# Test SSH connection with different methods
test_ssh_connection() {
    local ssh_key="$1"
    echo_step "Testing SSH connection with key: $ssh_key"
    
    # Test basic connection
    if ssh -i "$ssh_key" -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o BatchMode=yes "$SERVER_USER@$SERVER_HOST" "echo 'SSH Success'" 2>/dev/null; then
        echo_success "SSH connection successful!"
        return 0
    fi
    
    # Try with different SSH options
    echo_info "Trying alternative SSH options..."
    if ssh -i "$ssh_key" -o ConnectTimeout=10 -o StrictHostKeyChecking=no -o PasswordAuthentication=no -o IdentitiesOnly=yes "$SERVER_USER@$SERVER_HOST" "echo 'SSH Success'" 2>/dev/null; then
        echo_success "SSH connection successful with alternative options!"
        return 0
    fi
    
    return 1
}

# Upload files using rsync
upload_with_rsync() {
    local ssh_key="$1"
    echo_step "Uploading files with rsync..."
    
    if [ ! -d "$LOCAL_BUILD_DIR" ]; then
        echo_error "Build directory not found!"
        return 1
    fi
    
    # Upload build files
    if rsync -avz --delete -e "ssh -i $ssh_key -o StrictHostKeyChecking=no" "$LOCAL_BUILD_DIR/" "$SERVER_USER@$SERVER_HOST:$APP_DIR/build/"; then
        echo_success "Files uploaded successfully!"
        return 0
    else
        echo_error "File upload failed!"
        return 1
    fi
}

# Upload files using scp
upload_with_scp() {
    local ssh_key="$1"
    echo_step "Uploading files with scp..."
    
    if [ ! -d "$LOCAL_BUILD_DIR" ]; then
        echo_error "Build directory not found!"
        return 1
    fi
    
    # Create tar archive and upload
    echo_info "Creating archive..."
    tar -czf /tmp/build.tar.gz -C "$LOCAL_BUILD_DIR" .
    
    if scp -i "$ssh_key" -o StrictHostKeyChecking=no /tmp/build.tar.gz "$SERVER_USER@$SERVER_HOST:/tmp/"; then
        echo_info "Archive uploaded, extracting on server..."
        if ssh -i "$ssh_key" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "
            cd $APP_DIR
            sudo rm -rf build_backup_$(date +%Y%m%d) 2>/dev/null || true
            sudo mv build build_backup_$(date +%Y%m%d) 2>/dev/null || true
            sudo mkdir -p build
            cd build
            sudo tar -xzf /tmp/build.tar.gz
            sudo chown -R www-data:www-data .
            sudo chmod -R 755 .
            rm /tmp/build.tar.gz
        "; then
            echo_success "Files extracted and permissions set!"
            rm /tmp/build.tar.gz
            return 0
        fi
    fi
    
    echo_error "SCP upload failed!"
    rm -f /tmp/build.tar.gz
    return 1
}

# Restart services
restart_services() {
    local ssh_key="$1"
    echo_step "Restarting services..."
    
    if ssh -i "$ssh_key" -o StrictHostKeyChecking=no "$SERVER_USER@$SERVER_HOST" "
        pm2 restart kubera-backend 2>/dev/null || echo 'PM2 restart failed'
        sudo systemctl reload nginx
        echo 'Services restarted'
    "; then
        echo_success "Services restarted successfully!"
        return 0
    else
        echo_error "Service restart failed!"
        return 1
    fi
}

# Verify deployment
verify_deployment() {
    echo_step "Verifying deployment..."
    
    # Test website
    if curl -s -o /dev/null -w "%{http_code}" "https://$SERVER_HOST" | grep -q "200"; then
        echo_success "Website is responding!"
        
        # Check if new design is live (look for new CSS file)
        if curl -s "https://$SERVER_HOST" | grep -q "main.f58d57ea.css"; then
            echo_success "New design is live!"
            return 0
        else
            echo_info "Website responding but new design may not be live yet"
            echo_info "Try hard refresh (Ctrl+F5) in your browser"
        fi
    else
        echo_error "Website not responding correctly"
        return 1
    fi
}

# Main function
main() {
    echo_info "Starting SSH fix and deployment process..."
    
    # Find SSH keys
    find_ssh_keys
    
    # Try each key
    for key_file in "${SSH_KEYS[@]}"; do
        echo_step "Trying SSH key: $key_file"
        
        # Fix the key
        fixed_key=$(fix_ssh_key "$key_file")
        
        # Test connection
        if test_ssh_connection "$fixed_key"; then
            echo_success "SSH working with key: $key_file"
            
            # Try to upload files
            if upload_with_rsync "$fixed_key" || upload_with_scp "$fixed_key"; then
                # Restart services
                restart_services "$fixed_key"
                
                # Verify deployment
                sleep 5
                verify_deployment
                
                echo ""
                echo_success "🎉 Deployment completed successfully!"
                echo ""
                echo -e "${BLUE}🌐 Website: https://$SERVER_HOST${NC}"
                echo -e "${YELLOW}🧪 Test the new premium dark glass design!${NC}"
                echo ""
                return 0
            fi
        else
            echo_error "SSH failed with key: $key_file"
        fi
    done
    
    echo_error "All SSH attempts failed!"
    echo_info "Please use manual deployment method (see deploy-manual.md)"
    return 1
}

# Run main function
main
