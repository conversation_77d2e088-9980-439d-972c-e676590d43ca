{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nfunction InterceptorManager() {\n  this.handlers = [];\n}\n\n/**\n * Add a new interceptor to the stack\n *\n * @param {Function} fulfilled The function to handle `then` for a `Promise`\n * @param {Function} rejected The function to handle `reject` for a `Promise`\n *\n * @return {Number} An ID used to remove interceptor later\n */\nInterceptorManager.prototype.use = function use(fulfilled, rejected, options) {\n  this.handlers.push({\n    fulfilled: fulfilled,\n    rejected: rejected,\n    synchronous: options ? options.synchronous : false,\n    runWhen: options ? options.runWhen : null\n  });\n  return this.handlers.length - 1;\n};\n\n/**\n * Remove an interceptor from the stack\n *\n * @param {Number} id The ID that was returned by `use`\n */\nInterceptorManager.prototype.eject = function eject(id) {\n  if (this.handlers[id]) {\n    this.handlers[id] = null;\n  }\n};\n\n/**\n * Iterate over all the registered interceptors\n *\n * This method is particularly useful for skipping over any\n * interceptors that may have become `null` calling `eject`.\n *\n * @param {Function} fn The function to call for each interceptor\n */\nInterceptorManager.prototype.forEach = function forEach(fn) {\n  utils.forEach(this.handlers, function forEachHandler(h) {\n    if (h !== null) {\n      fn(h);\n    }\n  });\n};\nmodule.exports = InterceptorManager;", "map": {"version": 3, "names": ["utils", "require", "InterceptorManager", "handlers", "prototype", "use", "fulfilled", "rejected", "options", "push", "synchronous", "runWhen", "length", "eject", "id", "for<PERSON>ach", "fn", "forEachHandler", "h", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/core/InterceptorManager.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\n\nfunction InterceptorManager() {\n  this.handlers = [];\n}\n\n/**\n * Add a new interceptor to the stack\n *\n * @param {Function} fulfilled The function to handle `then` for a `Promise`\n * @param {Function} rejected The function to handle `reject` for a `Promise`\n *\n * @return {Number} An ID used to remove interceptor later\n */\nInterceptorManager.prototype.use = function use(fulfilled, rejected, options) {\n  this.handlers.push({\n    fulfilled: fulfilled,\n    rejected: rejected,\n    synchronous: options ? options.synchronous : false,\n    runWhen: options ? options.runWhen : null\n  });\n  return this.handlers.length - 1;\n};\n\n/**\n * Remove an interceptor from the stack\n *\n * @param {Number} id The ID that was returned by `use`\n */\nInterceptorManager.prototype.eject = function eject(id) {\n  if (this.handlers[id]) {\n    this.handlers[id] = null;\n  }\n};\n\n/**\n * Iterate over all the registered interceptors\n *\n * This method is particularly useful for skipping over any\n * interceptors that may have become `null` calling `eject`.\n *\n * @param {Function} fn The function to call for each interceptor\n */\nInterceptorManager.prototype.forEach = function forEach(fn) {\n  utils.forEach(this.handlers, function forEachHandler(h) {\n    if (h !== null) {\n      fn(h);\n    }\n  });\n};\n\nmodule.exports = InterceptorManager;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjC,SAASC,kBAAkBA,CAAA,EAAG;EAC5B,IAAI,CAACC,QAAQ,GAAG,EAAE;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAD,kBAAkB,CAACE,SAAS,CAACC,GAAG,GAAG,SAASA,GAAGA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC5E,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC;IACjBH,SAAS,EAAEA,SAAS;IACpBC,QAAQ,EAAEA,QAAQ;IAClBG,WAAW,EAAEF,OAAO,GAAGA,OAAO,CAACE,WAAW,GAAG,KAAK;IAClDC,OAAO,EAAEH,OAAO,GAAGA,OAAO,CAACG,OAAO,GAAG;EACvC,CAAC,CAAC;EACF,OAAO,IAAI,CAACR,QAAQ,CAACS,MAAM,GAAG,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAV,kBAAkB,CAACE,SAAS,CAACS,KAAK,GAAG,SAASA,KAAKA,CAACC,EAAE,EAAE;EACtD,IAAI,IAAI,CAACX,QAAQ,CAACW,EAAE,CAAC,EAAE;IACrB,IAAI,CAACX,QAAQ,CAACW,EAAE,CAAC,GAAG,IAAI;EAC1B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAZ,kBAAkB,CAACE,SAAS,CAACW,OAAO,GAAG,SAASA,OAAOA,CAACC,EAAE,EAAE;EAC1DhB,KAAK,CAACe,OAAO,CAAC,IAAI,CAACZ,QAAQ,EAAE,SAASc,cAAcA,CAACC,CAAC,EAAE;IACtD,IAAIA,CAAC,KAAK,IAAI,EAAE;MACdF,EAAE,CAACE,CAAC,CAAC;IACP;EACF,CAAC,CAAC;AACJ,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAGlB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}