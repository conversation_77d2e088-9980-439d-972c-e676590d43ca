const t=(t,e,i)=>Math.min(Math.max(i,t),e),e=.3,i=0,s=0,a=0,n="ease",r=t=>"number"==typeof t,h=t=>Array.isArray(t)&&!r(t[0]),o=(t,e,i)=>{const s=e-t;return((i-t)%s+s)%s+t};const u=(t,e,i)=>-i*t+i*e+t,l=t=>t,c=(t,e,i)=>e-t==0?1:(i-t)/(e-t);function m(t,e){const i=t[t.length-1];for(let s=1;s<=e;s++){const a=c(0,e,s);t.push(u(i,1,a))}}function p(e,i=function(t){const e=[0];return m(e,t-1),e}(e.length),s=l){const a=e.length,n=a-i.length;return n>0&&m(i,n),n=>{let r=0;for(;r<a-2&&!(n<i[r+1]);r++);let l=t(0,1,c(i[r],i[r+1],n));const m=function(t,e){return h(t)?t[o(0,t.length,e)]:t}(s,r);return l=m(l),u(e[r],e[r+1],l)}}const f=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t,d=1e-7,y=12;function T(t,e,i,s){if(t===e&&i===s)return l;const a=e=>function(t,e,i,s,a){let n,r,h=0;do{r=e+(i-e)/2,n=f(r,s,a)-t,n>0?i=r:e=r}while(Math.abs(n)>d&&++h<y);return r}(e,0,1,t,i);return t=>0===t||1===t?t:f(a(t),e,s)}const v={ease:T(.25,.1,.25,1),"ease-in":T(.42,0,1,1),"ease-in-out":T(.42,0,.58,1),"ease-out":T(0,0,.58,1)},g=/\((.*?)\)/;function M(e){if("function"==typeof e)return e;var i;if(i=e,Array.isArray(i)&&r(i[0]))return T(...e);const s=v[e];if(s)return s;if(e.startsWith("steps")){const i=g.exec(e);if(i){const e=i[1].split(",");return((e,i="end")=>s=>{const a=(s="end"===i?Math.min(s,.999):Math.max(s,.001))*e,n="end"===i?Math.floor(a):Math.ceil(a);return t(0,1,n/e)})(parseFloat(e[0]),e[1].trim())}}return l}class S{constructor(t,r=[0,1],{easing:o,duration:u=e,delay:c=i,endDelay:m=s,repeat:f=a,offset:d,direction:y="normal",autoplay:T=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=l,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise(((t,e)=>{this.resolve=t,this.reject=e})),(t=>"object"==typeof t&&Boolean(t.createAnimation))(o=o||n)){const t=o.createAnimation(r);o=t.easing,r=t.keyframes||r,u=t.duration||u}this.repeat=f,this.easing=h(o)?l:M(o),this.updateDuration(u);const v=p(r,d,h(o)?o.map(M):l);this.tick=e=>{var i;let s=0;s=void 0!==this.pauseTime?this.pauseTime:(e-this.startTime)*this.rate,this.t=s,s/=1e3,s=Math.max(s-c,0),"finished"===this.playState&&void 0===this.pauseTime&&(s=this.totalDuration);const a=s/this.duration;let n=Math.floor(a),r=a%1;!r&&a>=1&&(r=1),1===r&&n--;const h=n%2;("reverse"===y||"alternate"===y&&h||"alternate-reverse"===y&&!h)&&(r=1-r);const o=s>=this.totalDuration?1:Math.min(r,1),u=v(this.easing(o));t(u);void 0===this.pauseTime&&("finished"===this.playState||s>=this.totalDuration+m)?(this.playState="finished",null===(i=this.resolve)||void 0===i||i.call(this,u)):"idle"!==this.playState&&(this.frameRequestId=requestAnimationFrame(this.tick))},T&&this.play()}play(){const t=performance.now();this.playState="running",void 0!==this.pauseTime?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",void 0!==this.frameRequestId&&cancelAnimationFrame(this.frameRequestId),null===(t=this.reject)||void 0===t||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){void 0!==this.pauseTime||0===this.rate?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}}export{S as Animation,M as getEasingFunction};
