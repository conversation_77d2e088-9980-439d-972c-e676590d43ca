{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nfunction encode(val) {\n  return encodeURIComponent(val).replace(/%3A/gi, ':').replace(/%24/g, '$').replace(/%2C/gi, ',').replace(/%20/g, '+').replace(/%5B/gi, '[').replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @returns {string} The formatted url\n */\nmodule.exports = function buildURL(url, params, paramsSerializer) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n  var serializedParams;\n  if (paramsSerializer) {\n    serializedParams = paramsSerializer(params);\n  } else if (utils.isURLSearchParams(params)) {\n    serializedParams = params.toString();\n  } else {\n    var parts = [];\n    utils.forEach(params, function serialize(val, key) {\n      if (val === null || typeof val === 'undefined') {\n        return;\n      }\n      if (utils.isArray(val)) {\n        key = key + '[]';\n      } else {\n        val = [val];\n      }\n      utils.forEach(val, function parseValue(v) {\n        if (utils.isDate(v)) {\n          v = v.toISOString();\n        } else if (utils.isObject(v)) {\n          v = JSON.stringify(v);\n        }\n        parts.push(encode(key) + '=' + encode(v));\n      });\n    });\n    serializedParams = parts.join('&');\n  }\n  if (serializedParams) {\n    var hashmarkIndex = url.indexOf('#');\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n  return url;\n};", "map": {"version": 3, "names": ["utils", "require", "encode", "val", "encodeURIComponent", "replace", "module", "exports", "buildURL", "url", "params", "paramsSerializer", "serializedParams", "isURLSearchParams", "toString", "parts", "for<PERSON>ach", "serialize", "key", "isArray", "parseValue", "v", "isDate", "toISOString", "isObject", "JSON", "stringify", "push", "join", "hashmarkIndex", "indexOf", "slice"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/helpers/buildURL.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\n\nfunction encode(val) {\n  return encodeURIComponent(val).\n    replace(/%3A/gi, ':').\n    replace(/%24/g, '$').\n    replace(/%2C/gi, ',').\n    replace(/%20/g, '+').\n    replace(/%5B/gi, '[').\n    replace(/%5D/gi, ']');\n}\n\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @returns {string} The formatted url\n */\nmodule.exports = function buildURL(url, params, paramsSerializer) {\n  /*eslint no-param-reassign:0*/\n  if (!params) {\n    return url;\n  }\n\n  var serializedParams;\n  if (paramsSerializer) {\n    serializedParams = paramsSerializer(params);\n  } else if (utils.isURLSearchParams(params)) {\n    serializedParams = params.toString();\n  } else {\n    var parts = [];\n\n    utils.forEach(params, function serialize(val, key) {\n      if (val === null || typeof val === 'undefined') {\n        return;\n      }\n\n      if (utils.isArray(val)) {\n        key = key + '[]';\n      } else {\n        val = [val];\n      }\n\n      utils.forEach(val, function parseValue(v) {\n        if (utils.isDate(v)) {\n          v = v.toISOString();\n        } else if (utils.isObject(v)) {\n          v = JSON.stringify(v);\n        }\n        parts.push(encode(key) + '=' + encode(v));\n      });\n    });\n\n    serializedParams = parts.join('&');\n  }\n\n  if (serializedParams) {\n    var hashmarkIndex = url.indexOf('#');\n    if (hashmarkIndex !== -1) {\n      url = url.slice(0, hashmarkIndex);\n    }\n\n    url += (url.indexOf('?') === -1 ? '?' : '&') + serializedParams;\n  }\n\n  return url;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEjC,SAASC,MAAMA,CAACC,GAAG,EAAE;EACnB,OAAOC,kBAAkB,CAACD,GAAG,CAAC,CAC5BE,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CACrBA,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,GAAG,EAAEC,MAAM,EAAEC,gBAAgB,EAAE;EAChE;EACA,IAAI,CAACD,MAAM,EAAE;IACX,OAAOD,GAAG;EACZ;EAEA,IAAIG,gBAAgB;EACpB,IAAID,gBAAgB,EAAE;IACpBC,gBAAgB,GAAGD,gBAAgB,CAACD,MAAM,CAAC;EAC7C,CAAC,MAAM,IAAIV,KAAK,CAACa,iBAAiB,CAACH,MAAM,CAAC,EAAE;IAC1CE,gBAAgB,GAAGF,MAAM,CAACI,QAAQ,CAAC,CAAC;EACtC,CAAC,MAAM;IACL,IAAIC,KAAK,GAAG,EAAE;IAEdf,KAAK,CAACgB,OAAO,CAACN,MAAM,EAAE,SAASO,SAASA,CAACd,GAAG,EAAEe,GAAG,EAAE;MACjD,IAAIf,GAAG,KAAK,IAAI,IAAI,OAAOA,GAAG,KAAK,WAAW,EAAE;QAC9C;MACF;MAEA,IAAIH,KAAK,CAACmB,OAAO,CAAChB,GAAG,CAAC,EAAE;QACtBe,GAAG,GAAGA,GAAG,GAAG,IAAI;MAClB,CAAC,MAAM;QACLf,GAAG,GAAG,CAACA,GAAG,CAAC;MACb;MAEAH,KAAK,CAACgB,OAAO,CAACb,GAAG,EAAE,SAASiB,UAAUA,CAACC,CAAC,EAAE;QACxC,IAAIrB,KAAK,CAACsB,MAAM,CAACD,CAAC,CAAC,EAAE;UACnBA,CAAC,GAAGA,CAAC,CAACE,WAAW,CAAC,CAAC;QACrB,CAAC,MAAM,IAAIvB,KAAK,CAACwB,QAAQ,CAACH,CAAC,CAAC,EAAE;UAC5BA,CAAC,GAAGI,IAAI,CAACC,SAAS,CAACL,CAAC,CAAC;QACvB;QACAN,KAAK,CAACY,IAAI,CAACzB,MAAM,CAACgB,GAAG,CAAC,GAAG,GAAG,GAAGhB,MAAM,CAACmB,CAAC,CAAC,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFT,gBAAgB,GAAGG,KAAK,CAACa,IAAI,CAAC,GAAG,CAAC;EACpC;EAEA,IAAIhB,gBAAgB,EAAE;IACpB,IAAIiB,aAAa,GAAGpB,GAAG,CAACqB,OAAO,CAAC,GAAG,CAAC;IACpC,IAAID,aAAa,KAAK,CAAC,CAAC,EAAE;MACxBpB,GAAG,GAAGA,GAAG,CAACsB,KAAK,CAAC,CAAC,EAAEF,aAAa,CAAC;IACnC;IAEApB,GAAG,IAAI,CAACA,GAAG,CAACqB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,IAAIlB,gBAAgB;EACjE;EAEA,OAAOH,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}