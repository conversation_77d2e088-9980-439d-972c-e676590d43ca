{"ast": null, "code": "'use strict';\n\nvar isAbsoluteURL = require('../helpers/isAbsoluteURL');\nvar combineURLs = require('../helpers/combineURLs');\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n * @returns {string} The combined full path\n */\nmodule.exports = function buildFullPath(baseURL, requestedURL) {\n  if (baseURL && !isAbsoluteURL(requestedURL)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n};", "map": {"version": 3, "names": ["isAbsoluteURL", "require", "combineURLs", "module", "exports", "buildFullPath", "baseURL", "requestedURL"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/core/buildFullPath.js"], "sourcesContent": ["'use strict';\n\nvar isAbsoluteURL = require('../helpers/isAbsoluteURL');\nvar combineURLs = require('../helpers/combineURLs');\n\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n * @returns {string} The combined full path\n */\nmodule.exports = function buildFullPath(baseURL, requestedURL) {\n  if (baseURL && !isAbsoluteURL(requestedURL)) {\n    return combineURLs(baseURL, requestedURL);\n  }\n  return requestedURL;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,aAAa,GAAGC,OAAO,CAAC,0BAA0B,CAAC;AACvD,IAAIC,WAAW,GAAGD,OAAO,CAAC,wBAAwB,CAAC;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACC,OAAO,EAAEC,YAAY,EAAE;EAC7D,IAAID,OAAO,IAAI,CAACN,aAAa,CAACO,YAAY,CAAC,EAAE;IAC3C,OAAOL,WAAW,CAACI,OAAO,EAAEC,YAAY,CAAC;EAC3C;EACA,OAAOA,YAAY;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}