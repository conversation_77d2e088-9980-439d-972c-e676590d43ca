{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\SmokeAnimation.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst SmokeAnimation = () => {\n  _s();\n  const containerRef = useRef(null);\n  const intervalRef = useRef(null);\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n    const createSmokeParticle = () => {\n      const smoke = document.createElement('div');\n      smoke.className = 'smoke-particle';\n\n      // Random horizontal position\n      const leftPosition = Math.random() * 100;\n\n      // Random size and opacity\n      const size = Math.random() * 60 + 40; // 40-100px\n      const opacity = Math.random() * 0.3 + 0.1; // 0.1-0.4\n\n      // Random animation duration\n      const duration = Math.random() * 4 + 6; // 6-10 seconds\n\n      smoke.style.cssText = `\n        position: absolute;\n        bottom: -50px;\n        left: ${leftPosition}%;\n        width: ${size}px;\n        height: ${size}px;\n        background: radial-gradient(circle, rgba(244, 208, 63, ${opacity}) 0%, rgba(244, 208, 63, ${opacity * 0.5}) 30%, transparent 70%);\n        border-radius: 50%;\n        pointer-events: none;\n        animation: smokeRise ${duration}s linear forwards;\n        filter: blur(1px);\n      `;\n      container.appendChild(smoke);\n\n      // Remove particle after animation\n      setTimeout(() => {\n        if (smoke.parentNode) {\n          smoke.parentNode.removeChild(smoke);\n        }\n      }, duration * 1000);\n    };\n\n    // Create smoke particles at intervals\n    const startSmokeAnimation = () => {\n      intervalRef.current = setInterval(() => {\n        createSmokeParticle();\n      }, 800); // Create new particle every 800ms\n    };\n    startSmokeAnimation();\n\n    // Cleanup\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      ref: containerRef,\n      style: {\n        position: 'fixed',\n        bottom: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: 1,\n        overflow: 'hidden'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes smokeRise {\n          0% {\n            transform: translateY(0) scale(1) rotate(0deg);\n            opacity: 0.7;\n          }\n          25% {\n            opacity: 0.8;\n          }\n          50% {\n            transform: translateY(-50vh) scale(1.5) rotate(180deg);\n            opacity: 0.6;\n          }\n          75% {\n            opacity: 0.3;\n          }\n          100% {\n            transform: translateY(-100vh) scale(2) rotate(360deg);\n            opacity: 0;\n          }\n        }\n        \n        .smoke-particle {\n          will-change: transform, opacity;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(SmokeAnimation, \"2SXd09JIhaYkRzSGKvQi9hZUyyc=\");\n_c = SmokeAnimation;\nexport default SmokeAnimation;\nvar _c;\n$RefreshReg$(_c, \"SmokeAnimation\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "SmokeAnimation", "_s", "containerRef", "intervalRef", "container", "current", "createSmokeParticle", "smoke", "document", "createElement", "className", "leftPosition", "Math", "random", "size", "opacity", "duration", "style", "cssText", "append<PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "startSmokeAnimation", "setInterval", "clearInterval", "children", "ref", "position", "bottom", "left", "width", "height", "pointerEvents", "zIndex", "overflow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/SmokeAnimation.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst SmokeAnimation = () => {\n  const containerRef = useRef(null);\n  const intervalRef = useRef(null);\n\n  useEffect(() => {\n    const container = containerRef.current;\n    if (!container) return;\n\n    const createSmokeParticle = () => {\n      const smoke = document.createElement('div');\n      smoke.className = 'smoke-particle';\n      \n      // Random horizontal position\n      const leftPosition = Math.random() * 100;\n      \n      // Random size and opacity\n      const size = Math.random() * 60 + 40; // 40-100px\n      const opacity = Math.random() * 0.3 + 0.1; // 0.1-0.4\n      \n      // Random animation duration\n      const duration = Math.random() * 4 + 6; // 6-10 seconds\n      \n      smoke.style.cssText = `\n        position: absolute;\n        bottom: -50px;\n        left: ${leftPosition}%;\n        width: ${size}px;\n        height: ${size}px;\n        background: radial-gradient(circle, rgba(244, 208, 63, ${opacity}) 0%, rgba(244, 208, 63, ${opacity * 0.5}) 30%, transparent 70%);\n        border-radius: 50%;\n        pointer-events: none;\n        animation: smokeRise ${duration}s linear forwards;\n        filter: blur(1px);\n      `;\n      \n      container.appendChild(smoke);\n      \n      // Remove particle after animation\n      setTimeout(() => {\n        if (smoke.parentNode) {\n          smoke.parentNode.removeChild(smoke);\n        }\n      }, duration * 1000);\n    };\n\n    // Create smoke particles at intervals\n    const startSmokeAnimation = () => {\n      intervalRef.current = setInterval(() => {\n        createSmokeParticle();\n      }, 800); // Create new particle every 800ms\n    };\n\n    startSmokeAnimation();\n\n    // Cleanup\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <>\n      <div\n        ref={containerRef}\n        style={{\n          position: 'fixed',\n          bottom: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          pointerEvents: 'none',\n          zIndex: 1,\n          overflow: 'hidden'\n        }}\n      />\n      \n      <style jsx>{`\n        @keyframes smokeRise {\n          0% {\n            transform: translateY(0) scale(1) rotate(0deg);\n            opacity: 0.7;\n          }\n          25% {\n            opacity: 0.8;\n          }\n          50% {\n            transform: translateY(-50vh) scale(1.5) rotate(180deg);\n            opacity: 0.6;\n          }\n          75% {\n            opacity: 0.3;\n          }\n          100% {\n            transform: translateY(-100vh) scale(2) rotate(360deg);\n            opacity: 0;\n          }\n        }\n        \n        .smoke-particle {\n          will-change: transform, opacity;\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default SmokeAnimation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,YAAY,GAAGP,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMQ,WAAW,GAAGR,MAAM,CAAC,IAAI,CAAC;EAEhCD,SAAS,CAAC,MAAM;IACd,MAAMU,SAAS,GAAGF,YAAY,CAACG,OAAO;IACtC,IAAI,CAACD,SAAS,EAAE;IAEhB,MAAME,mBAAmB,GAAGA,CAAA,KAAM;MAChC,MAAMC,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC3CF,KAAK,CAACG,SAAS,GAAG,gBAAgB;;MAElC;MACA,MAAMC,YAAY,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;;MAExC;MACA,MAAMC,IAAI,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MACtC,MAAME,OAAO,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;;MAE3C;MACA,MAAMG,QAAQ,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;MAExCN,KAAK,CAACU,KAAK,CAACC,OAAO,GAAG;AAC5B;AACA;AACA,gBAAgBP,YAAY;AAC5B,iBAAiBG,IAAI;AACrB,kBAAkBA,IAAI;AACtB,iEAAiEC,OAAO,4BAA4BA,OAAO,GAAG,GAAG;AACjH;AACA;AACA,+BAA+BC,QAAQ;AACvC;AACA,OAAO;MAEDZ,SAAS,CAACe,WAAW,CAACZ,KAAK,CAAC;;MAE5B;MACAa,UAAU,CAAC,MAAM;QACf,IAAIb,KAAK,CAACc,UAAU,EAAE;UACpBd,KAAK,CAACc,UAAU,CAACC,WAAW,CAACf,KAAK,CAAC;QACrC;MACF,CAAC,EAAES,QAAQ,GAAG,IAAI,CAAC;IACrB,CAAC;;IAED;IACA,MAAMO,mBAAmB,GAAGA,CAAA,KAAM;MAChCpB,WAAW,CAACE,OAAO,GAAGmB,WAAW,CAAC,MAAM;QACtClB,mBAAmB,CAAC,CAAC;MACvB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC;IAEDiB,mBAAmB,CAAC,CAAC;;IAErB;IACA,OAAO,MAAM;MACX,IAAIpB,WAAW,CAACE,OAAO,EAAE;QACvBoB,aAAa,CAACtB,WAAW,CAACE,OAAO,CAAC;MACpC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACER,OAAA,CAAAE,SAAA;IAAA2B,QAAA,gBACE7B,OAAA;MACE8B,GAAG,EAAEzB,YAAa;MAClBe,KAAK,EAAE;QACLW,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,CAAC;QACTC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,aAAa,EAAE,MAAM;QACrBC,MAAM,EAAE,CAAC;QACTC,QAAQ,EAAE;MACZ;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEF1C,OAAA;MAAO2C,GAAG;MAAAd,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAACtC,EAAA,CA1GID,cAAc;AAAAyC,EAAA,GAAdzC,cAAc;AA4GpB,eAAeA,cAAc;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}