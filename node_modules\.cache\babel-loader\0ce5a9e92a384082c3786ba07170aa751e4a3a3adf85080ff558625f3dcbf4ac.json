{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\n// Enhanced zodiac data with additional information\nconst zodiacData = {\n  aries: {\n    dates: 'මාර්තු 21 - අප්‍රේල් 19',\n    element: 'ගිනි',\n    planet: 'අඟහරු',\n    color: 'රතු',\n    gemstone: 'දියමන්ති',\n    description: 'නායකත්ව ගුණාංග සහිත',\n    gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'\n  },\n  taurus: {\n    dates: 'අප්‍රේල් 20 - මැයි 20',\n    element: 'පෘථිවි',\n    planet: 'සිකුරු',\n    color: 'කොළ',\n    gemstone: 'මරකත',\n    description: 'ස්ථිර සහ විශ්වාසදායක',\n    gradient: 'linear-gradient(135deg, #26de81 0%, #20bf6b 100%)'\n  },\n  gemini: {\n    dates: 'මැයි 21 - ජූනි 20',\n    element: 'වායු',\n    planet: 'බුධ',\n    color: 'කහ',\n    gemstone: 'පීත මණි',\n    description: 'බුද්ධිමත් සහ කතාබහට ප්‍රිය',\n    gradient: 'linear-gradient(135deg, #fed330 0%, #f7b731 100%)'\n  },\n  cancer: {\n    dates: 'ජූනි 21 - ජූලි 22',\n    element: 'ජල',\n    planet: 'චන්ද්‍රයා',\n    color: 'රිදී',\n    gemstone: 'මුතු',\n    description: 'සංවේදී සහ රැකවරණ ගුණ',\n    gradient: 'linear-gradient(135deg, #a4b0be 0%, #747d8c 100%)'\n  },\n  leo: {\n    dates: 'ජූලි 23 - අගෝස්තු 22',\n    element: 'ගිනි',\n    planet: 'සූර්යයා',\n    color: 'රන්වන්',\n    gemstone: 'රුබි',\n    description: 'ආත්මවිශ්වාසී සහ උදාර',\n    gradient: 'linear-gradient(135deg, #f39c12 0%, #d35400 100%)'\n  },\n  virgo: {\n    dates: 'අගෝස්තු 23 - සැප්තැම්බර් 22',\n    element: 'පෘථිවි',\n    planet: 'බුධ',\n    color: 'නිල්',\n    gemstone: 'නිල්මණි',\n    description: 'විශ්ලේෂණාත්මක සහ ක්‍රමවත්',\n    gradient: 'linear-gradient(135deg, #3742fa 0%, #2f3542 100%)'\n  },\n  libra: {\n    dates: 'සැප්තැම්බර් 23 - ඔක්තෝබර් 22',\n    element: 'වායු',\n    planet: 'සිකුරු',\n    color: 'රෝස',\n    gemstone: 'ඔපල්',\n    description: 'සමබර සහ සාධාරණ',\n    gradient: 'linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%)'\n  },\n  scorpio: {\n    dates: 'ඔක්තෝබර් 23 - නොවැම්බර් 21',\n    element: 'ජල',\n    planet: 'අඟහරු',\n    color: 'තද රතු',\n    gemstone: 'ටොපාස්',\n    description: 'තීව්‍ර සහ අභිරහස්',\n    gradient: 'linear-gradient(135deg, #8b0000 0%, #dc143c 100%)'\n  },\n  sagittarius: {\n    dates: 'නොවැම්බර් 22 - දෙසැම්බර් 21',\n    element: 'ගිනි',\n    planet: 'බ්‍රහස්පති',\n    color: 'දම්',\n    gemstone: 'ටර්කොයිස්',\n    description: 'ස්වාධීන සහ ප්‍රීතිමත්',\n    gradient: 'linear-gradient(135deg, #9c88ff 0%, #8c7ae6 100%)'\n  },\n  capricorn: {\n    dates: 'දෙසැම්බර් 22 - ජනවාරි 19',\n    element: 'පෘථිවි',\n    planet: 'සෙනසුරු',\n    color: 'කළු',\n    gemstone: 'ගාර්නට්',\n    description: 'අධිෂ්ඨානශීලී සහ ප්‍රායෝගික',\n    gradient: 'linear-gradient(135deg, #2c2c54 0%, #40407a 100%)'\n  },\n  aquarius: {\n    dates: 'ජනවාරි 20 - පෙබරවාරි 18',\n    element: 'වායු',\n    planet: 'යුරේනස්',\n    color: 'ටර්කොයිස්',\n    gemstone: 'ඇමතිස්ට්',\n    description: 'නව්‍ය සහ මානවීය',\n    gradient: 'linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%)'\n  },\n  pisces: {\n    dates: 'පෙබරවාරි 19 - මාර්තු 20',\n    element: 'ජල',\n    planet: 'නෙප්චූන්',\n    color: 'මුහුදු නිල්',\n    gemstone: 'ඇක්වාමරීන්',\n    description: 'සංවේදී සහ කලාත්මක',\n    gradient: 'linear-gradient(135deg, #0abde3 0%, #006ba6 100%)'\n  }\n};\nconst LandingPage = ({\n  zodiacSigns\n}) => {\n  _s();\n  useEffect(() => {\n    // Add floating animation to zodiac cards with staggered delay\n    const cards = document.querySelectorAll('.premium-zodiac-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.1}s`;\n      card.classList.add('floating');\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page\",\n    children: [/*#__PURE__*/_jsxDEV(ParticleBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(KuberaAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"landing-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"main-title\",\n        children: \"\\u0DC3\\u0DD2\\u0D82\\u0DC4\\u0DBD \\u0DA2\\u0DCA\\u200D\\u0DBA\\u0DDC\\u0DAD\\u0DD2\\u0DC2 \\u0DC0\\u0DD9\\u0DB6\\u0DCA \\u0D85\\u0DA9\\u0DC0\\u0DD2\\u0DBA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"subtitle\",\n        children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\u0DAF\\u0DDB\\u0DB1\\u0DD2\\u0D9A \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"\\u0DB4\\u0DD4\\u0DBB\\u0DCF\\u0DAB \\u0DA2\\u0DCA\\u200D\\u0DBA\\u0DDC\\u0DAD\\u0DD2\\u0DC2 \\u0DC1\\u0DCF\\u0DC3\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA\\u0DDA \\u0D9C\\u0DD0\\u0DB9\\u0DD4\\u0DBB\\u0DD4 \\u0DA5\\u0DCF\\u0DB1\\u0DBA \\u0DC3\\u0DC4 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\u0D94\\u0DB6\\u0DDA \\u0DAF\\u0DDB\\u0DB1\\u0DD2\\u0D9A \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DC3\\u0DC4 \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD \\u0DB8\\u0D9C \\u0DB4\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DD3\\u0DB8 \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DB1\\u0DCA\\u0DB1.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divine-blessing\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"blessing-text\",\n          children: \"\\uD83D\\uDE4F \\u0DAF\\u0DD2\\u0DC0\\u0DCA\\u200D\\u0DBA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\uD83D\\uDE4F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 148,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"premium-zodiac-grid\",\n      children: zodiacSigns.map((sign, index) => {\n        const signData = zodiacData[sign.id];\n        return /*#__PURE__*/_jsxDEV(Link, {\n          to: `/${sign.id}`,\n          className: \"premium-zodiac-card\",\n          style: {\n            animationDelay: `${index * 0.1}s`,\n            background: (signData === null || signData === void 0 ? void 0 : signData.gradient) || 'linear-gradient(135deg, rgba(244, 208, 63, 0.1) 0%, rgba(244, 208, 63, 0.05) 100%)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"zodiac-header-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"zodiac-icon-large\",\n              children: zodiacIcons[sign.id]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"zodiac-names-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sinhala-name-large\",\n                children: sign.sinhala\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"english-name-small\",\n                children: sign.english\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"zodiac-details\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"\\u0D9A\\u0DCF\\u0DBD \\u0DC3\\u0DD3\\u0DB8\\u0DCF\\u0DC0:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: signData === null || signData === void 0 ? void 0 : signData.dates\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"\\u0DB8\\u0DD6\\u0DBD\\u0DAF\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DCA\\u200D\\u0DBA\\u0DBA:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: signData === null || signData === void 0 ? void 0 : signData.element\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"\\u0D9C\\u0DCA\\u200D\\u0DBB\\u0DC4\\u0DBA\\u0DCF:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: signData === null || signData === void 0 ? void 0 : signData.planet\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"detail-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-label\",\n                children: \"\\u0DB8\\u0DD0\\u0DAB\\u0DD2\\u0D9A:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"detail-value\",\n                children: signData === null || signData === void 0 ? void 0 : signData.gemstone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"zodiac-description\",\n            children: signData === null || signData === void 0 ? void 0 : signData.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-action\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-text\",\n              children: \"\\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DB6\\u0DBD\\u0DB1\\u0DCA\\u0DB1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"action-arrow\",\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this)]\n        }, sign.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 144,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "Link", "ParticleBackground", "KuberaAnimation", "jsxDEV", "_jsxDEV", "zodiacIcons", "aries", "taurus", "gemini", "cancer", "leo", "virgo", "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces", "zodiacData", "dates", "element", "planet", "color", "gemstone", "description", "gradient", "LandingPage", "zodiacSigns", "_s", "cards", "document", "querySelectorAll", "for<PERSON>ach", "card", "index", "style", "animationDelay", "classList", "add", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "sign", "signData", "id", "to", "background", "sinhala", "english", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\n\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\n// Enhanced zodiac data with additional information\nconst zodiacData = {\n  aries: {\n    dates: 'මාර්තු 21 - අප්‍රේල් 19',\n    element: 'ගිනි',\n    planet: 'අඟහරු',\n    color: 'රතු',\n    gemstone: 'දියමන්ති',\n    description: 'නායකත්ව ගුණාංග සහිත',\n    gradient: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'\n  },\n  taurus: {\n    dates: 'අප්‍රේල් 20 - මැයි 20',\n    element: 'පෘථිවි',\n    planet: 'සිකුරු',\n    color: 'කොළ',\n    gemstone: 'මරකත',\n    description: 'ස්ථිර සහ විශ්වාසදායක',\n    gradient: 'linear-gradient(135deg, #26de81 0%, #20bf6b 100%)'\n  },\n  gemini: {\n    dates: 'මැයි 21 - ජූනි 20',\n    element: 'වායු',\n    planet: 'බුධ',\n    color: 'කහ',\n    gemstone: 'පීත මණි',\n    description: 'බුද්ධිමත් සහ කතාබහට ප්‍රිය',\n    gradient: 'linear-gradient(135deg, #fed330 0%, #f7b731 100%)'\n  },\n  cancer: {\n    dates: 'ජූනි 21 - ජූලි 22',\n    element: 'ජල',\n    planet: 'චන්ද්‍රයා',\n    color: 'රිදී',\n    gemstone: 'මුතු',\n    description: 'සංවේදී සහ රැකවරණ ගුණ',\n    gradient: 'linear-gradient(135deg, #a4b0be 0%, #747d8c 100%)'\n  },\n  leo: {\n    dates: 'ජූලි 23 - අගෝස්තු 22',\n    element: 'ගිනි',\n    planet: 'සූර්යයා',\n    color: 'රන්වන්',\n    gemstone: 'රුබි',\n    description: 'ආත්මවිශ්වාසී සහ උදාර',\n    gradient: 'linear-gradient(135deg, #f39c12 0%, #d35400 100%)'\n  },\n  virgo: {\n    dates: 'අගෝස්තු 23 - සැප්තැම්බර් 22',\n    element: 'පෘථිවි',\n    planet: 'බුධ',\n    color: 'නිල්',\n    gemstone: 'නිල්මණි',\n    description: 'විශ්ලේෂණාත්මක සහ ක්‍රමවත්',\n    gradient: 'linear-gradient(135deg, #3742fa 0%, #2f3542 100%)'\n  },\n  libra: {\n    dates: 'සැප්තැම්බර් 23 - ඔක්තෝබර් 22',\n    element: 'වායු',\n    planet: 'සිකුරු',\n    color: 'රෝස',\n    gemstone: 'ඔපල්',\n    description: 'සමබර සහ සාධාරණ',\n    gradient: 'linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%)'\n  },\n  scorpio: {\n    dates: 'ඔක්තෝබර් 23 - නොවැම්බර් 21',\n    element: 'ජල',\n    planet: 'අඟහරු',\n    color: 'තද රතු',\n    gemstone: 'ටොපාස්',\n    description: 'තීව්‍ර සහ අභිරහස්',\n    gradient: 'linear-gradient(135deg, #8b0000 0%, #dc143c 100%)'\n  },\n  sagittarius: {\n    dates: 'නොවැම්බර් 22 - දෙසැම්බර් 21',\n    element: 'ගිනි',\n    planet: 'බ්‍රහස්පති',\n    color: 'දම්',\n    gemstone: 'ටර්කොයිස්',\n    description: 'ස්වාධීන සහ ප්‍රීතිමත්',\n    gradient: 'linear-gradient(135deg, #9c88ff 0%, #8c7ae6 100%)'\n  },\n  capricorn: {\n    dates: 'දෙසැම්බර් 22 - ජනවාරි 19',\n    element: 'පෘථිවි',\n    planet: 'සෙනසුරු',\n    color: 'කළු',\n    gemstone: 'ගාර්නට්',\n    description: 'අධිෂ්ඨානශීලී සහ ප්‍රායෝගික',\n    gradient: 'linear-gradient(135deg, #2c2c54 0%, #40407a 100%)'\n  },\n  aquarius: {\n    dates: 'ජනවාරි 20 - පෙබරවාරි 18',\n    element: 'වායු',\n    planet: 'යුරේනස්',\n    color: 'ටර්කොයිස්',\n    gemstone: 'ඇමතිස්ට්',\n    description: 'නව්‍ය සහ මානවීය',\n    gradient: 'linear-gradient(135deg, #00d2d3 0%, #54a0ff 100%)'\n  },\n  pisces: {\n    dates: 'පෙබරවාරි 19 - මාර්තු 20',\n    element: 'ජල',\n    planet: 'නෙප්චූන්',\n    color: 'මුහුදු නිල්',\n    gemstone: 'ඇක්වාමරීන්',\n    description: 'සංවේදී සහ කලාත්මක',\n    gradient: 'linear-gradient(135deg, #0abde3 0%, #006ba6 100%)'\n  }\n};\n\nconst LandingPage = ({ zodiacSigns }) => {\n  useEffect(() => {\n    // Add floating animation to zodiac cards with staggered delay\n    const cards = document.querySelectorAll('.premium-zodiac-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.1}s`;\n      card.classList.add('floating');\n    });\n  }, []);\n\n  return (\n    <div className=\"landing-page\">\n      <ParticleBackground />\n      <KuberaAnimation />\n\n      <div className=\"landing-header\">\n        <h1 className=\"main-title\">සිංහල ජ්‍යොතිෂ වෙබ් අඩවිය</h1>\n        <h2 className=\"subtitle\">කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ දෛනික රාශිඵල</h2>\n        <p className=\"description\">\n          පුරාණ ජ්‍යොතිෂ ශාස්ත්‍රයේ ගැඹුරු ඥානය සහ කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ\n          ඔබේ දෛනික රාශිඵල සහ ජීවිත මග පෙන්වීම ලබා ගන්න.\n        </p>\n        <div className=\"divine-blessing\">\n          <span className=\"blessing-text\">🙏 දිව්‍ය ආශීර්වාදය සමඟ 🙏</span>\n        </div>\n      </div>\n\n      <div className=\"premium-zodiac-grid\">\n        {zodiacSigns.map((sign, index) => {\n          const signData = zodiacData[sign.id];\n          return (\n            <Link\n              key={sign.id}\n              to={`/${sign.id}`}\n              className=\"premium-zodiac-card\"\n              style={{\n                animationDelay: `${index * 0.1}s`,\n                background: signData?.gradient || 'linear-gradient(135deg, rgba(244, 208, 63, 0.1) 0%, rgba(244, 208, 63, 0.05) 100%)'\n              }}\n            >\n              <div className=\"card-glow\"></div>\n              <div className=\"zodiac-header-section\">\n                <div className=\"zodiac-icon-large\">{zodiacIcons[sign.id]}</div>\n                <div className=\"zodiac-names-section\">\n                  <div className=\"sinhala-name-large\">{sign.sinhala}</div>\n                  <div className=\"english-name-small\">{sign.english}</div>\n                </div>\n              </div>\n\n              <div className=\"zodiac-details\">\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">කාල සීමාව:</span>\n                  <span className=\"detail-value\">{signData?.dates}</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">මූලද්‍රව්‍යය:</span>\n                  <span className=\"detail-value\">{signData?.element}</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">ග්‍රහයා:</span>\n                  <span className=\"detail-value\">{signData?.planet}</span>\n                </div>\n                <div className=\"detail-row\">\n                  <span className=\"detail-label\">මැණික:</span>\n                  <span className=\"detail-value\">{signData?.gemstone}</span>\n                </div>\n              </div>\n\n              <div className=\"zodiac-description\">\n                {signData?.description}\n              </div>\n\n              <div className=\"card-action\">\n                <span className=\"action-text\">රාශිඵල බලන්න</span>\n                <span className=\"action-arrow\">→</span>\n              </div>\n            </Link>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default LandingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,eAAe,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,GAAG,EAAE,GAAG;EACRC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,GAAG;EACZC,WAAW,EAAE,GAAG;EAChBC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,MAAMC,UAAU,GAAG;EACjBZ,KAAK,EAAE;IACLa,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,QAAQ,EAAE;EACZ,CAAC;EACDlB,MAAM,EAAE;IACNY,KAAK,EAAE,uBAAuB;IAC9BC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDjB,MAAM,EAAE;IACNW,KAAK,EAAE,mBAAmB;IAC1BC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDhB,MAAM,EAAE;IACNU,KAAK,EAAE,mBAAmB;IAC1BC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,WAAW;IACnBC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDf,GAAG,EAAE;IACHS,KAAK,EAAE,sBAAsB;IAC7BC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,sBAAsB;IACnCC,QAAQ,EAAE;EACZ,CAAC;EACDd,KAAK,EAAE;IACLQ,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,KAAK;IACbC,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,2BAA2B;IACxCC,QAAQ,EAAE;EACZ,CAAC;EACDb,KAAK,EAAE;IACLO,KAAK,EAAE,8BAA8B;IACrCC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,gBAAgB;IAC7BC,QAAQ,EAAE;EACZ,CAAC;EACDZ,OAAO,EAAE;IACPM,KAAK,EAAE,4BAA4B;IACnCC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,OAAO;IACfC,KAAK,EAAE,QAAQ;IACfC,QAAQ,EAAE,QAAQ;IAClBC,WAAW,EAAE,mBAAmB;IAChCC,QAAQ,EAAE;EACZ,CAAC;EACDX,WAAW,EAAE;IACXK,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,YAAY;IACpBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,WAAW;IACrBC,WAAW,EAAE,uBAAuB;IACpCC,QAAQ,EAAE;EACZ,CAAC;EACDV,SAAS,EAAE;IACTI,KAAK,EAAE,0BAA0B;IACjCC,OAAO,EAAE,QAAQ;IACjBC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,KAAK;IACZC,QAAQ,EAAE,SAAS;IACnBC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE;EACZ,CAAC;EACDT,QAAQ,EAAE;IACRG,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,SAAS;IACjBC,KAAK,EAAE,WAAW;IAClBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,iBAAiB;IAC9BC,QAAQ,EAAE;EACZ,CAAC;EACDR,MAAM,EAAE;IACNE,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,UAAU;IAClBC,KAAK,EAAE,aAAa;IACpBC,QAAQ,EAAE,YAAY;IACtBC,WAAW,EAAE,mBAAmB;IAChCC,QAAQ,EAAE;EACZ;AACF,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACvC7B,SAAS,CAAC,MAAM;IACd;IACA,MAAM8B,KAAK,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,sBAAsB,CAAC;IAC/DF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAC7BD,IAAI,CAACE,KAAK,CAACC,cAAc,GAAG,GAAGF,KAAK,GAAG,GAAG,GAAG;MAC7CD,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACElC,OAAA;IAAKmC,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BpC,OAAA,CAACH,kBAAkB;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBxC,OAAA,CAACF,eAAe;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEnBxC,OAAA;MAAKmC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BpC,OAAA;QAAImC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDxC,OAAA;QAAImC,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAA0C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxExC,OAAA;QAAGmC,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAG3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJxC,OAAA;QAAKmC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BpC,OAAA;UAAMmC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA0B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENxC,OAAA;MAAKmC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,EACjCb,WAAW,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEZ,KAAK,KAAK;QAChC,MAAMa,QAAQ,GAAG7B,UAAU,CAAC4B,IAAI,CAACE,EAAE,CAAC;QACpC,oBACE5C,OAAA,CAACJ,IAAI;UAEHiD,EAAE,EAAE,IAAIH,IAAI,CAACE,EAAE,EAAG;UAClBT,SAAS,EAAC,qBAAqB;UAC/BJ,KAAK,EAAE;YACLC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG,GAAG;YACjCgB,UAAU,EAAE,CAAAH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEtB,QAAQ,KAAI;UACpC,CAAE;UAAAe,QAAA,gBAEFpC,OAAA;YAAKmC,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCxC,OAAA;YAAKmC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBACpCpC,OAAA;cAAKmC,SAAS,EAAC,mBAAmB;cAAAC,QAAA,EAAEnC,WAAW,CAACyC,IAAI,CAACE,EAAE;YAAC;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/DxC,OAAA;cAAKmC,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCpC,OAAA;gBAAKmC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEM,IAAI,CAACK;cAAO;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxDxC,OAAA;gBAAKmC,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEM,IAAI,CAACM;cAAO;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BpC,OAAA;cAAKmC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDxC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE5B;cAAK;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNxC,OAAA;cAAKmC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACnDxC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE3B;cAAO;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNxC,OAAA;cAAKmC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9CxC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE1B;cAAM;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACNxC,OAAA;cAAKmC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzBpC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5CxC,OAAA;gBAAMmC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAExB;cAAQ;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAChCO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvB;UAAW;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAENxC,OAAA;YAAKmC,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BpC,OAAA;cAAMmC,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDxC,OAAA;cAAMmC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA,GA3CDE,IAAI,CAACE,EAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4CR,CAAC;MAEX,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAlFIF,WAAW;AAAA2B,EAAA,GAAX3B,WAAW;AAoFjB,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}