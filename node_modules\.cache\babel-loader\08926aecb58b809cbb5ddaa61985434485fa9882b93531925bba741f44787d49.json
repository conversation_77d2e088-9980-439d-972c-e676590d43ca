{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\n\"production\" !== process.env.NODE_ENV && function () {\n  function is(x, y) {\n    return x === y && (0 !== x || 1 / x === 1 / y) || x !== x && y !== y;\n  }\n  function useSyncExternalStore$2(subscribe, getSnapshot) {\n    didWarnOld18Alpha || void 0 === React.startTransition || (didWarnOld18Alpha = !0, console.error(\"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"));\n    var value = getSnapshot();\n    if (!didWarnUncachedGetSnapshot) {\n      var cachedValue = getSnapshot();\n      objectIs(value, cachedValue) || (console.error(\"The result of getSnapshot should be cached to avoid an infinite loop\"), didWarnUncachedGetSnapshot = !0);\n    }\n    cachedValue = useState({\n      inst: {\n        value: value,\n        getSnapshot: getSnapshot\n      }\n    });\n    var inst = cachedValue[0].inst,\n      forceUpdate = cachedValue[1];\n    useLayoutEffect(function () {\n      inst.value = value;\n      inst.getSnapshot = getSnapshot;\n      checkIfSnapshotChanged(inst) && forceUpdate({\n        inst: inst\n      });\n    }, [subscribe, value, getSnapshot]);\n    useEffect(function () {\n      checkIfSnapshotChanged(inst) && forceUpdate({\n        inst: inst\n      });\n      return subscribe(function () {\n        checkIfSnapshotChanged(inst) && forceUpdate({\n          inst: inst\n        });\n      });\n    }, [subscribe]);\n    useDebugValue(value);\n    return value;\n  }\n  function checkIfSnapshotChanged(inst) {\n    var latestGetSnapshot = inst.getSnapshot;\n    inst = inst.value;\n    try {\n      var nextValue = latestGetSnapshot();\n      return !objectIs(inst, nextValue);\n    } catch (error) {\n      return !0;\n    }\n  }\n  function useSyncExternalStore$1(subscribe, getSnapshot) {\n    return getSnapshot();\n  }\n  \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n  var React = require(\"react\"),\n    objectIs = \"function\" === typeof Object.is ? Object.is : is,\n    useState = React.useState,\n    useEffect = React.useEffect,\n    useLayoutEffect = React.useLayoutEffect,\n    useDebugValue = React.useDebugValue,\n    didWarnOld18Alpha = !1,\n    didWarnUncachedGetSnapshot = !1,\n    shim = \"undefined\" === typeof window || \"undefined\" === typeof window.document || \"undefined\" === typeof window.document.createElement ? useSyncExternalStore$1 : useSyncExternalStore$2;\n  exports.useSyncExternalStore = void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n  \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ && \"function\" === typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop && __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n}();", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "is", "x", "y", "useSyncExternalStore$2", "subscribe", "getSnapshot", "didWarnOld18Alpha", "React", "startTransition", "console", "error", "value", "didWarnUncachedGetSnapshot", "cachedValue", "objectIs", "useState", "inst", "forceUpdate", "useLayoutEffect", "checkIfSnapshotChanged", "useEffect", "useDebugValue", "latestGetSnapshot", "nextValue", "useSyncExternalStore$1", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "registerInternalModuleStart", "Error", "require", "Object", "shim", "window", "document", "createElement", "exports", "useSyncExternalStore", "registerInternalModuleStop"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AACZ,YAAY,KAAKA,OAAO,CAACC,GAAG,CAACC,QAAQ,IAClC,YAAY;EACX,SAASC,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAChB,OAAQD,CAAC,KAAKC,CAAC,KAAK,CAAC,KAAKD,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC,CAAC,IAAMD,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAE;EAC1E;EACA,SAASC,sBAAsBA,CAACC,SAAS,EAAEC,WAAW,EAAE;IACtDC,iBAAiB,IACf,KAAK,CAAC,KAAKC,KAAK,CAACC,eAAe,KAC9BF,iBAAiB,GAAG,CAAC,CAAC,EACxBG,OAAO,CAACC,KAAK,CACX,gMACF,CAAC,CAAC;IACJ,IAAIC,KAAK,GAAGN,WAAW,CAAC,CAAC;IACzB,IAAI,CAACO,0BAA0B,EAAE;MAC/B,IAAIC,WAAW,GAAGR,WAAW,CAAC,CAAC;MAC/BS,QAAQ,CAACH,KAAK,EAAEE,WAAW,CAAC,KACzBJ,OAAO,CAACC,KAAK,CACZ,sEACF,CAAC,EACAE,0BAA0B,GAAG,CAAC,CAAE,CAAC;IACtC;IACAC,WAAW,GAAGE,QAAQ,CAAC;MACrBC,IAAI,EAAE;QAAEL,KAAK,EAAEA,KAAK;QAAEN,WAAW,EAAEA;MAAY;IACjD,CAAC,CAAC;IACF,IAAIW,IAAI,GAAGH,WAAW,CAAC,CAAC,CAAC,CAACG,IAAI;MAC5BC,WAAW,GAAGJ,WAAW,CAAC,CAAC,CAAC;IAC9BK,eAAe,CACb,YAAY;MACVF,IAAI,CAACL,KAAK,GAAGA,KAAK;MAClBK,IAAI,CAACX,WAAW,GAAGA,WAAW;MAC9Bc,sBAAsB,CAACH,IAAI,CAAC,IAAIC,WAAW,CAAC;QAAED,IAAI,EAAEA;MAAK,CAAC,CAAC;IAC7D,CAAC,EACD,CAACZ,SAAS,EAAEO,KAAK,EAAEN,WAAW,CAChC,CAAC;IACDe,SAAS,CACP,YAAY;MACVD,sBAAsB,CAACH,IAAI,CAAC,IAAIC,WAAW,CAAC;QAAED,IAAI,EAAEA;MAAK,CAAC,CAAC;MAC3D,OAAOZ,SAAS,CAAC,YAAY;QAC3Be,sBAAsB,CAACH,IAAI,CAAC,IAAIC,WAAW,CAAC;UAAED,IAAI,EAAEA;QAAK,CAAC,CAAC;MAC7D,CAAC,CAAC;IACJ,CAAC,EACD,CAACZ,SAAS,CACZ,CAAC;IACDiB,aAAa,CAACV,KAAK,CAAC;IACpB,OAAOA,KAAK;EACd;EACA,SAASQ,sBAAsBA,CAACH,IAAI,EAAE;IACpC,IAAIM,iBAAiB,GAAGN,IAAI,CAACX,WAAW;IACxCW,IAAI,GAAGA,IAAI,CAACL,KAAK;IACjB,IAAI;MACF,IAAIY,SAAS,GAAGD,iBAAiB,CAAC,CAAC;MACnC,OAAO,CAACR,QAAQ,CAACE,IAAI,EAAEO,SAAS,CAAC;IACnC,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd,OAAO,CAAC,CAAC;IACX;EACF;EACA,SAASc,sBAAsBA,CAACpB,SAAS,EAAEC,WAAW,EAAE;IACtD,OAAOA,WAAW,CAAC,CAAC;EACtB;EACA,WAAW,KAAK,OAAOoB,8BAA8B,IACnD,UAAU,KACR,OAAOA,8BAA8B,CAACC,2BAA2B,IACnED,8BAA8B,CAACC,2BAA2B,CAACC,KAAK,CAAC,CAAC,CAAC;EACrE,IAAIpB,KAAK,GAAGqB,OAAO,CAAC,OAAO,CAAC;IAC1Bd,QAAQ,GAAG,UAAU,KAAK,OAAOe,MAAM,CAAC7B,EAAE,GAAG6B,MAAM,CAAC7B,EAAE,GAAGA,EAAE;IAC3De,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBK,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BF,eAAe,GAAGX,KAAK,CAACW,eAAe;IACvCG,aAAa,GAAGd,KAAK,CAACc,aAAa;IACnCf,iBAAiB,GAAG,CAAC,CAAC;IACtBM,0BAA0B,GAAG,CAAC,CAAC;IAC/BkB,IAAI,GACF,WAAW,KAAK,OAAOC,MAAM,IAC7B,WAAW,KAAK,OAAOA,MAAM,CAACC,QAAQ,IACtC,WAAW,KAAK,OAAOD,MAAM,CAACC,QAAQ,CAACC,aAAa,GAChDT,sBAAsB,GACtBrB,sBAAsB;EAC9B+B,OAAO,CAACC,oBAAoB,GAC1B,KAAK,CAAC,KAAK5B,KAAK,CAAC4B,oBAAoB,GAAG5B,KAAK,CAAC4B,oBAAoB,GAAGL,IAAI;EAC3E,WAAW,KAAK,OAAOL,8BAA8B,IACnD,UAAU,KACR,OAAOA,8BAA8B,CAACW,0BAA0B,IAClEX,8BAA8B,CAACW,0BAA0B,CAACT,KAAK,CAAC,CAAC,CAAC;AACtE,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}