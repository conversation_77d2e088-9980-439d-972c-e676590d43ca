{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\SmokeAnimation.js\",\n  _s = $RefreshSig$();\nimport React, { useMemo, Suspense } from 'react';\nimport { Canvas } from '@react-three/fiber';\nimport { Smoke } from 'react-smoke';\nimport * as THREE from 'three';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SmokeAnimation = () => {\n  _s();\n  // Create memoized colors for better performance\n  const bgColor = useMemo(() => new THREE.Color('transparent'), []);\n  const smokeColor = useMemo(() => new THREE.Color('#f4d03f'), []); // Golden color matching the theme\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      bottom: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none',\n      zIndex: 1,\n      overflow: 'hidden'\n    },\n    children: /*#__PURE__*/_jsxDEV(Canvas, {\n      camera: {\n        fov: 60,\n        position: [0, -200, 300],\n        far: 6000\n      },\n      scene: {\n        background: bgColor\n      },\n      style: {\n        background: 'transparent'\n      },\n      children: /*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: null,\n        children: [/*#__PURE__*/_jsxDEV(\"ambientLight\", {\n          intensity: 0.3\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"directionalLight\", {\n          intensity: 0.5,\n          position: [-1, 0, 1],\n          color: \"#f4d03f\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Smoke, {\n          color: smokeColor,\n          density: 30,\n          enableRotation: true,\n          rotation: [0, 0, 0.1],\n          position: [0, -100, 0],\n          scale: [2, 1.5, 2]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Smoke, {\n          color: smokeColor,\n          density: 20,\n          enableRotation: true,\n          rotation: [0, 0, -0.05],\n          position: [-50, -80, -20],\n          scale: [1.5, 1.2, 1.5]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Smoke, {\n          color: smokeColor,\n          density: 25,\n          enableRotation: true,\n          rotation: [0, 0, 0.08],\n          position: [50, -90, -10],\n          scale: [1.8, 1.3, 1.8]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_s(SmokeAnimation, \"YVXO8v64/G6rbhc7kgB+JxfOOjI=\");\n_c = SmokeAnimation;\nexport default SmokeAnimation;\nvar _c;\n$RefreshReg$(_c, \"SmokeAnimation\");", "map": {"version": 3, "names": ["React", "useMemo", "Suspense", "<PERSON><PERSON>", "Smoke", "THREE", "jsxDEV", "_jsxDEV", "SmokeAnimation", "_s", "bgColor", "Color", "smokeColor", "style", "position", "bottom", "left", "width", "height", "pointerEvents", "zIndex", "overflow", "children", "camera", "fov", "far", "scene", "background", "fallback", "intensity", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "density", "enableRotation", "rotation", "scale", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/SmokeAnimation.js"], "sourcesContent": ["import React, { useMemo, Suspense } from 'react';\nimport { Canvas } from '@react-three/fiber';\nimport { Smoke } from 'react-smoke';\nimport * as THREE from 'three';\n\nconst SmokeAnimation = () => {\n  // Create memoized colors for better performance\n  const bgColor = useMemo(() => new THREE.Color('transparent'), []);\n  const smokeColor = useMemo(() => new THREE.Color('#f4d03f'), []); // Golden color matching the theme\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        bottom: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: 1,\n        overflow: 'hidden'\n      }}\n    >\n      <Canvas\n        camera={{ \n          fov: 60, \n          position: [0, -200, 300], \n          far: 6000 \n        }}\n        scene={{\n          background: bgColor,\n        }}\n        style={{\n          background: 'transparent'\n        }}\n      >\n        <Suspense fallback={null}>\n          {/* Ambient lighting for better smoke visibility */}\n          <ambientLight intensity={0.3} />\n          <directionalLight \n            intensity={0.5} \n            position={[-1, 0, 1]} \n            color=\"#f4d03f\" \n          />\n          \n          {/* Main smoke effect */}\n          <Smoke\n            color={smokeColor}\n            density={30}\n            enableRotation={true}\n            rotation={[0, 0, 0.1]}\n            position={[0, -100, 0]}\n            scale={[2, 1.5, 2]}\n          />\n          \n          {/* Additional smoke layers for more realistic effect */}\n          <Smoke\n            color={smokeColor}\n            density={20}\n            enableRotation={true}\n            rotation={[0, 0, -0.05]}\n            position={[-50, -80, -20]}\n            scale={[1.5, 1.2, 1.5]}\n          />\n          \n          <Smoke\n            color={smokeColor}\n            density={25}\n            enableRotation={true}\n            rotation={[0, 0, 0.08]}\n            position={[50, -90, -10]}\n            scale={[1.8, 1.3, 1.8]}\n          />\n        </Suspense>\n      </Canvas>\n    </div>\n  );\n};\n\nexport default SmokeAnimation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AAChD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,KAAK,QAAQ,aAAa;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B;EACA,MAAMC,OAAO,GAAGT,OAAO,CAAC,MAAM,IAAII,KAAK,CAACM,KAAK,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;EACjE,MAAMC,UAAU,GAAGX,OAAO,CAAC,MAAM,IAAII,KAAK,CAACM,KAAK,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAElE,oBACEJ,OAAA;IACEM,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,aAAa,EAAE,MAAM;MACrBC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,eAEFf,OAAA,CAACJ,MAAM;MACLoB,MAAM,EAAE;QACNC,GAAG,EAAE,EAAE;QACPV,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;QACxBW,GAAG,EAAE;MACP,CAAE;MACFC,KAAK,EAAE;QACLC,UAAU,EAAEjB;MACd,CAAE;MACFG,KAAK,EAAE;QACLc,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,eAEFf,OAAA,CAACL,QAAQ;QAAC0B,QAAQ,EAAE,IAAK;QAAAN,QAAA,gBAEvBf,OAAA;UAAcsB,SAAS,EAAE;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChC1B,OAAA;UACEsB,SAAS,EAAE,GAAI;UACff,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE;UACrBoB,KAAK,EAAC;QAAS;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAGF1B,OAAA,CAACH,KAAK;UACJ8B,KAAK,EAAEtB,UAAW;UAClBuB,OAAO,EAAE,EAAG;UACZC,cAAc,EAAE,IAAK;UACrBC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAE;UACtBvB,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAE;UACvBwB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAGF1B,OAAA,CAACH,KAAK;UACJ8B,KAAK,EAAEtB,UAAW;UAClBuB,OAAO,EAAE,EAAG;UACZC,cAAc,EAAE,IAAK;UACrBC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAE;UACxBvB,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAE;UAC1BwB,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eAEF1B,OAAA,CAACH,KAAK;UACJ8B,KAAK,EAAEtB,UAAW;UAClBuB,OAAO,EAAE,EAAG;UACZC,cAAc,EAAE,IAAK;UACrBC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAE;UACvBvB,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAE;UACzBwB,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;QAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACxB,EAAA,CAxEID,cAAc;AAAA+B,EAAA,GAAd/B,cAAc;AA0EpB,eAAeA,cAAc;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}