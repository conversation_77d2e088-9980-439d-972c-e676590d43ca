{"ast": null, "code": "'use strict';\n\nvar utils = require('./utils');\nvar bind = require('./helpers/bind');\nvar Axios = require('./core/Axios');\nvar mergeConfig = require('./core/mergeConfig');\nvar defaults = require('./defaults');\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n * @return {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context);\n\n  // Copy context to instance\n  utils.extend(instance, context);\n\n  // Factory for creating new instances\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n  return instance;\n}\n\n// Create the default instance to be exported\nvar axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Expose Cancel & CancelToken\naxios.CanceledError = require('./cancel/CanceledError');\naxios.CancelToken = require('./cancel/CancelToken');\naxios.isCancel = require('./cancel/isCancel');\naxios.VERSION = require('./env/data').version;\naxios.toFormData = require('./helpers/toFormData');\n\n// Expose AxiosError class\naxios.AxiosError = require('../lib/core/AxiosError');\n\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = require('./helpers/spread');\n\n// Expose isAxiosError\naxios.isAxiosError = require('./helpers/isAxiosError');\nmodule.exports = axios;\n\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = axios;", "map": {"version": 3, "names": ["utils", "require", "bind", "A<PERSON>os", "mergeConfig", "defaults", "createInstance", "defaultConfig", "context", "instance", "prototype", "request", "extend", "create", "instanceConfig", "axios", "CanceledError", "CancelToken", "isCancel", "VERSION", "version", "toFormData", "AxiosError", "Cancel", "all", "promises", "Promise", "spread", "isAxiosError", "module", "exports", "default"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/axios.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./utils');\nvar bind = require('./helpers/bind');\nvar Axios = require('./core/Axios');\nvar mergeConfig = require('./core/mergeConfig');\nvar defaults = require('./defaults');\n\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n * @return {Axios} A new instance of Axios\n */\nfunction createInstance(defaultConfig) {\n  var context = new Axios(defaultConfig);\n  var instance = bind(Axios.prototype.request, context);\n\n  // Copy axios.prototype to instance\n  utils.extend(instance, Axios.prototype, context);\n\n  // Copy context to instance\n  utils.extend(instance, context);\n\n  // Factory for creating new instances\n  instance.create = function create(instanceConfig) {\n    return createInstance(mergeConfig(defaultConfig, instanceConfig));\n  };\n\n  return instance;\n}\n\n// Create the default instance to be exported\nvar axios = createInstance(defaults);\n\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios;\n\n// Expose Cancel & CancelToken\naxios.CanceledError = require('./cancel/CanceledError');\naxios.CancelToken = require('./cancel/CancelToken');\naxios.isCancel = require('./cancel/isCancel');\naxios.VERSION = require('./env/data').version;\naxios.toFormData = require('./helpers/toFormData');\n\n// Expose AxiosError class\naxios.AxiosError = require('../lib/core/AxiosError');\n\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n\n// Expose all/spread\naxios.all = function all(promises) {\n  return Promise.all(promises);\n};\naxios.spread = require('./helpers/spread');\n\n// Expose isAxiosError\naxios.isAxiosError = require('./helpers/isAxiosError');\n\nmodule.exports = axios;\n\n// Allow use of default import syntax in TypeScript\nmodule.exports.default = axios;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIC,IAAI,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACpC,IAAIE,KAAK,GAAGF,OAAO,CAAC,cAAc,CAAC;AACnC,IAAIG,WAAW,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AAC/C,IAAII,QAAQ,GAAGJ,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,cAAcA,CAACC,aAAa,EAAE;EACrC,IAAIC,OAAO,GAAG,IAAIL,KAAK,CAACI,aAAa,CAAC;EACtC,IAAIE,QAAQ,GAAGP,IAAI,CAACC,KAAK,CAACO,SAAS,CAACC,OAAO,EAAEH,OAAO,CAAC;;EAErD;EACAR,KAAK,CAACY,MAAM,CAACH,QAAQ,EAAEN,KAAK,CAACO,SAAS,EAAEF,OAAO,CAAC;;EAEhD;EACAR,KAAK,CAACY,MAAM,CAACH,QAAQ,EAAED,OAAO,CAAC;;EAE/B;EACAC,QAAQ,CAACI,MAAM,GAAG,SAASA,MAAMA,CAACC,cAAc,EAAE;IAChD,OAAOR,cAAc,CAACF,WAAW,CAACG,aAAa,EAAEO,cAAc,CAAC,CAAC;EACnE,CAAC;EAED,OAAOL,QAAQ;AACjB;;AAEA;AACA,IAAIM,KAAK,GAAGT,cAAc,CAACD,QAAQ,CAAC;;AAEpC;AACAU,KAAK,CAACZ,KAAK,GAAGA,KAAK;;AAEnB;AACAY,KAAK,CAACC,aAAa,GAAGf,OAAO,CAAC,wBAAwB,CAAC;AACvDc,KAAK,CAACE,WAAW,GAAGhB,OAAO,CAAC,sBAAsB,CAAC;AACnDc,KAAK,CAACG,QAAQ,GAAGjB,OAAO,CAAC,mBAAmB,CAAC;AAC7Cc,KAAK,CAACI,OAAO,GAAGlB,OAAO,CAAC,YAAY,CAAC,CAACmB,OAAO;AAC7CL,KAAK,CAACM,UAAU,GAAGpB,OAAO,CAAC,sBAAsB,CAAC;;AAElD;AACAc,KAAK,CAACO,UAAU,GAAGrB,OAAO,CAAC,wBAAwB,CAAC;;AAEpD;AACAc,KAAK,CAACQ,MAAM,GAAGR,KAAK,CAACC,aAAa;;AAElC;AACAD,KAAK,CAACS,GAAG,GAAG,SAASA,GAAGA,CAACC,QAAQ,EAAE;EACjC,OAAOC,OAAO,CAACF,GAAG,CAACC,QAAQ,CAAC;AAC9B,CAAC;AACDV,KAAK,CAACY,MAAM,GAAG1B,OAAO,CAAC,kBAAkB,CAAC;;AAE1C;AACAc,KAAK,CAACa,YAAY,GAAG3B,OAAO,CAAC,wBAAwB,CAAC;AAEtD4B,MAAM,CAACC,OAAO,GAAGf,KAAK;;AAEtB;AACAc,MAAM,CAACC,OAAO,CAACC,OAAO,GAAGhB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}