{"ast": null, "code": "'use strict';\n\nmodule.exports = function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n};", "map": {"version": 3, "names": ["module", "exports", "isCancel", "value", "__CANCEL__"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/cancel/isCancel.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function isCancel(value) {\n  return !!(value && value.__CANCEL__);\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,KAAK,EAAE;EACxC,OAAO,CAAC,EAAEA,KAAK,IAAIA,KAAK,CAACC,UAAU,CAAC;AACtC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}