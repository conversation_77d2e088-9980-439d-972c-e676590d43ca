{"ast": null, "code": "import axios from 'axios';\nclass HoroscopeService {\n  constructor() {\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Try Gemini API first with enhanced real-time functionality\n  async getHoroscopeFromGemini(signEnglish, signSinhala) {\n    try {\n      var _response$data, _response$data$candid, _response$data$candid2, _response$data$candid3, _response$data$candid4, _response$data$candid5;\n      // Note: Replace with your actual Gemini API key\n      const GEMINI_API_KEY = process.env.REACT_APP_GEMINI_API_KEY;\n      if (!GEMINI_API_KEY) {\n        throw new Error('Gemini API key not found');\n      }\n      const today = new Date();\n      const dateString = today.toLocaleDateString('si-LK', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long'\n      });\n      const prompt = `Generate a comprehensive and detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language for today ${dateString}. \n\nYou MUST provide complete and detailed content for ALL FIVE categories below. Each category should have at least 2-3 sentences of meaningful predictions:\n\n1. ආදරය සහ සම්බන්ධතා (Love and Relationships)\n[Provide detailed predictions about romantic relationships, family bonds, friendships, and emotional connections for today]\n\n2. වෘත්තීය ජීවිතය (Career and Work)\n[Provide specific guidance about work, career opportunities, professional relationships, and business matters for today]\n\n3. සෞඛ්‍ය සහ යහපැවැත්ම (Health and Wellness)\n[Provide advice about physical health, mental wellbeing, energy levels, and self-care for today]\n\n4. මූල්‍ය කටයුතු (Financial matters)\n[Provide guidance about money, investments, expenses, financial decisions, and economic opportunities for today]\n\n5. සාමාන්‍ය උපදෙස් (General advice)\n[Provide overall guidance, spiritual advice, lucky colors/numbers, and general recommendations for today]\n\nIMPORTANT: Make sure to write complete, meaningful content for each category. Do not leave any category empty or incomplete. Write in beautiful, flowing Sinhala script with spiritual and positive tone. Make it culturally relevant to Sri Lankan context and include specific guidance for today's date.`;\n      const response = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${GEMINI_API_KEY}`, {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.8,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 2048\n        }\n      }, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 15000 // 15 second timeout\n      });\n      if ((_response$data = response.data) !== null && _response$data !== void 0 && (_response$data$candid = _response$data.candidates) !== null && _response$data$candid !== void 0 && (_response$data$candid2 = _response$data$candid[0]) !== null && _response$data$candid2 !== void 0 && (_response$data$candid3 = _response$data$candid2.content) !== null && _response$data$candid3 !== void 0 && (_response$data$candid4 = _response$data$candid3.parts) !== null && _response$data$candid4 !== void 0 && (_response$data$candid5 = _response$data$candid4[0]) !== null && _response$data$candid5 !== void 0 && _response$data$candid5.text) {\n        return response.data.candidates[0].content.parts[0].text;\n      } else {\n        throw new Error('Invalid response from Gemini API');\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$config, _error$config2;\n      console.error('Gemini API error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        config: {\n          url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n          method: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.method\n        }\n      });\n      throw error;\n    }\n  }\n\n  // OpenAI API as fallback\n  async getHoroscopeFromOpenAI(signEnglish, signSinhala) {\n    try {\n      var _response$data2, _response$data2$choic, _response$data2$choic2, _response$data2$choic3;\n      // Note: Replace with your actual OpenAI API key\n      const OPENAI_API_KEY = process.env.REACT_APP_OPENAI_API_KEY;\n      if (!OPENAI_API_KEY) {\n        throw new Error('OpenAI API key not found');\n      }\n      const prompt = `Generate a detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language. Include predictions about love, career, health, and general advice. Make it spiritual and positive. Write in beautiful Sinhala script. Today's date is ${new Date().toLocaleDateString()}.`;\n      const response = await axios.post('https://api.openai.com/v1/chat/completions', {\n        model: 'gpt-3.5-turbo',\n        messages: [{\n          role: 'system',\n          content: 'You are a wise Sinhala astrologer who provides spiritual guidance and daily horoscopes in beautiful Sinhala language.'\n        }, {\n          role: 'user',\n          content: prompt\n        }],\n        max_tokens: 500,\n        temperature: 0.7\n      }, {\n        headers: {\n          'Authorization': `Bearer ${OPENAI_API_KEY}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if ((_response$data2 = response.data) !== null && _response$data2 !== void 0 && (_response$data2$choic = _response$data2.choices) !== null && _response$data2$choic !== void 0 && (_response$data2$choic2 = _response$data2$choic[0]) !== null && _response$data2$choic2 !== void 0 && (_response$data2$choic3 = _response$data2$choic2.message) !== null && _response$data2$choic3 !== void 0 && _response$data2$choic3.content) {\n        return response.data.choices[0].message.content;\n      } else {\n        throw new Error('Invalid response from OpenAI API');\n      }\n    } catch (error) {\n      console.error('OpenAI API error:', error);\n      throw error;\n    }\n  }\n\n  // Generate fallback horoscope if APIs fail\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': 'අද දිනය ඔබට ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ආදරය සහ කාර්යක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n      'වෘෂභ': 'ස්ථාවරත්වය සහ ඉවසීම අදට ඔබේ ප්‍රධාන ගුණාංග වනු ඇත. මූල්‍ය කටයුතුවල සැලකිලිමත් වන්න. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න.',\n      'මිථුන': 'සන්නිවේදනය සහ නව අදහස් අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සමාජ සම්බන්ධතා ශක්තිමත් කරන්න. ගමන් සහ ඉගෙනීම සඳහා හොඳ දිනයකි.',\n      'කටක': 'පවුල සහ නිවස අද ඔබේ අවධානයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වන්න. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n      'සිංහ': 'නායකත්වය සහ විශ්වාසය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. කලාත්මක ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. ආදරයේ ක්ෂේත්‍රයේ ධනාත්මක ප්‍රගතියක් අපේක්ෂා කරන්න.',\n      'කන්‍යා': 'විස්තර සහ සංවිධානය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සෞඛ්‍ය සහ සේවා කටයුතුවලට අවධානය යොමු කරන්න. කුඩා ප්‍රගතියන් ප්‍රශංසා කරන්න.',\n      'තුලා': 'සමතුලිතතාවය සහ සාධාරණත්වය අද ඔබේ මග පෙන්වනු ඇත. සම්බන්ධතා සහ හවුල්කාරිත්වයන් ශක්තිමත් කරන්න. සෞන්දර්යය සහ කලාව අගය කරන්න.',\n      'වෘශ්චික': 'ගැඹුරු අවබෝධය සහ පරිවර්තනය අද ඔබේ ප්‍රධාන තේමාවන් වනු ඇත. අභ්‍යන්තර ශක්තිය සොයා ගන්න. රහස්‍ය සහ ගවේෂණ සඳහා සුදුසු කාලයකි.',\n      'ධනු': 'ඉගෙනීම සහ ගවේෂණය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. දර්ශනය සහ ආධ්‍යාත්මිකත්වය කෙරෙහි අවධානය යොමු කරන්න. දුර ගමන් සඳහා හොඳ දිනයකි.',\n      'මකර': 'වගකීම සහ අභිලාෂය අද ඔබේ මග පෙන්වනු ඇත. දිගුකාලීන ඉලක්ක සඳහා වැඩ කරන්න. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',\n      'කුම්භ': 'නවෝත්පාදනය සහ මිත්‍රත්වය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. අනාගතය සඳහා සැලසුම් කරන්න.',\n      'මීන': 'අන්තර්ज්ඤානය සහ කරුණාව අද ඔබේ මග පෙන්වනු ඇත. ආධ්‍යාත්මික ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. කලාත්මක ප්‍රකාශනය සඳහා සුදුසු කාලයකි.'\n    };\n    return fallbackHoroscopes[signSinhala] || 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.';\n  }\n\n  // Clear cache for a specific sign (useful for refresh)\n  clearCache(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.delete(cacheKey);\n  }\n\n  // Clear all cache\n  clearAllCache() {\n    this.cache.clear();\n  }\n\n  // Main method to get horoscope\n  async getHoroscope(signEnglish, signSinhala, forceRefresh = false) {\n    try {\n      // Clear cache if force refresh is requested\n      if (forceRefresh) {\n        this.clearCache(signEnglish.toLowerCase());\n      }\n\n      // Try Gemini API first\n      return await this.getHoroscopeFromGemini(signEnglish, signSinhala);\n    } catch (geminiError) {\n      console.log('Gemini API failed, trying OpenAI...');\n      try {\n        // Try OpenAI API as fallback\n        return await this.getHoroscopeFromOpenAI(signEnglish, signSinhala);\n      } catch (openaiError) {\n        console.log('Both APIs failed, using fallback horoscope...');\n\n        // Use fallback horoscope if both APIs fail\n        return this.generateFallbackHoroscope(signSinhala);\n      }\n    }\n  }\n}\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;", "map": {"version": 3, "names": ["axios", "HoroscopeService", "constructor", "cache", "Map", "cacheExpiry", "getCachedHoroscope", "signId", "today", "Date", "toDateString", "cache<PERSON>ey", "has", "cached", "get", "now", "timestamp", "data", "delete", "cacheHoroscope", "horoscope", "set", "getHoroscopeFromGemini", "signEnglish", "signSinhala", "_response$data", "_response$data$candid", "_response$data$candid2", "_response$data$candid3", "_response$data$candid4", "_response$data$candid5", "GEMINI_API_KEY", "process", "env", "REACT_APP_GEMINI_API_KEY", "Error", "dateString", "toLocaleDateString", "year", "month", "day", "weekday", "prompt", "response", "post", "contents", "parts", "text", "generationConfig", "temperature", "topK", "topP", "maxOutputTokens", "headers", "timeout", "candidates", "content", "error", "_error$response", "_error$response2", "_error$response3", "_error$config", "_error$config2", "console", "message", "status", "statusText", "config", "url", "method", "getHoroscopeFromOpenAI", "_response$data2", "_response$data2$choic", "_response$data2$choic2", "_response$data2$choic3", "OPENAI_API_KEY", "REACT_APP_OPENAI_API_KEY", "model", "messages", "role", "max_tokens", "choices", "generateFallbackHoroscope", "fallbackHoroscopes", "clearCache", "clearAllCache", "clear", "getHoroscope", "forceRefresh", "toLowerCase", "geminiError", "log", "openaiError", "horoscopeService"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass HoroscopeService {\n  constructor() {\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    \n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Try Gemini API first with enhanced real-time functionality\n  async getHoroscopeFromGemini(signEnglish, signSinhala) {\n    try {\n      // Note: Replace with your actual Gemini API key\n      const GEMINI_API_KEY = process.env.REACT_APP_GEMINI_API_KEY;\n      \n      if (!GEMINI_API_KEY) {\n        throw new Error('Gemini API key not found');\n      }\n\n      const today = new Date();\n      const dateString = today.toLocaleDateString('si-LK', { \n        year: 'numeric', \n        month: 'long', \n        day: 'numeric',\n        weekday: 'long'\n      });\n\n      const prompt = `Generate a comprehensive and detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language for today ${dateString}. \n\nYou MUST provide complete and detailed content for ALL FIVE categories below. Each category should have at least 2-3 sentences of meaningful predictions:\n\n1. ආදරය සහ සම්බන්ධතා (Love and Relationships)\n[Provide detailed predictions about romantic relationships, family bonds, friendships, and emotional connections for today]\n\n2. වෘත්තීය ජීවිතය (Career and Work)\n[Provide specific guidance about work, career opportunities, professional relationships, and business matters for today]\n\n3. සෞඛ්‍ය සහ යහපැවැත්ම (Health and Wellness)\n[Provide advice about physical health, mental wellbeing, energy levels, and self-care for today]\n\n4. මූල්‍ය කටයුතු (Financial matters)\n[Provide guidance about money, investments, expenses, financial decisions, and economic opportunities for today]\n\n5. සාමාන්‍ය උපදෙස් (General advice)\n[Provide overall guidance, spiritual advice, lucky colors/numbers, and general recommendations for today]\n\nIMPORTANT: Make sure to write complete, meaningful content for each category. Do not leave any category empty or incomplete. Write in beautiful, flowing Sinhala script with spiritual and positive tone. Make it culturally relevant to Sri Lankan context and include specific guidance for today's date.`;\n\n      const response = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${GEMINI_API_KEY}`,\n        {\n          contents: [{\n            parts: [{\n              text: prompt\n            }]\n          }],\n          generationConfig: {\n            temperature: 0.8,\n            topK: 40,\n            topP: 0.95,\n            maxOutputTokens: 2048\n          }\n        },\n        {\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          timeout: 15000 // 15 second timeout\n        }\n      );\n\n      if (response.data?.candidates?.[0]?.content?.parts?.[0]?.text) {\n        return response.data.candidates[0].content.parts[0].text;\n      } else {\n        throw new Error('Invalid response from Gemini API');\n      }\n    } catch (error) {\n      console.error('Gemini API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        config: {\n          url: error.config?.url,\n          method: error.config?.method\n        }\n      });\n      throw error;\n    }\n  }\n\n  // OpenAI API as fallback\n  async getHoroscopeFromOpenAI(signEnglish, signSinhala) {\n    try {\n      // Note: Replace with your actual OpenAI API key\n      const OPENAI_API_KEY = process.env.REACT_APP_OPENAI_API_KEY;\n      \n      if (!OPENAI_API_KEY) {\n        throw new Error('OpenAI API key not found');\n      }\n\n      const prompt = `Generate a detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language. Include predictions about love, career, health, and general advice. Make it spiritual and positive. Write in beautiful Sinhala script. Today's date is ${new Date().toLocaleDateString()}.`;\n\n      const response = await axios.post(\n        'https://api.openai.com/v1/chat/completions',\n        {\n          model: 'gpt-3.5-turbo',\n          messages: [\n            {\n              role: 'system',\n              content: 'You are a wise Sinhala astrologer who provides spiritual guidance and daily horoscopes in beautiful Sinhala language.'\n            },\n            {\n              role: 'user',\n              content: prompt\n            }\n          ],\n          max_tokens: 500,\n          temperature: 0.7\n        },\n        {\n          headers: {\n            'Authorization': `Bearer ${OPENAI_API_KEY}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data?.choices?.[0]?.message?.content) {\n        return response.data.choices[0].message.content;\n      } else {\n        throw new Error('Invalid response from OpenAI API');\n      }\n    } catch (error) {\n      console.error('OpenAI API error:', error);\n      throw error;\n    }\n  }\n\n  // Generate fallback horoscope if APIs fail\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': 'අද දිනය ඔබට ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ආදරය සහ කාර්යක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n      'වෘෂභ': 'ස්ථාවරත්වය සහ ඉවසීම අදට ඔබේ ප්‍රධාන ගුණාංග වනු ඇත. මූල්‍ය කටයුතුවල සැලකිලිමත් වන්න. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න.',\n      'මිථුන': 'සන්නිවේදනය සහ නව අදහස් අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සමාජ සම්බන්ධතා ශක්තිමත් කරන්න. ගමන් සහ ඉගෙනීම සඳහා හොඳ දිනයකි.',\n      'කටක': 'පවුල සහ නිවස අද ඔබේ අවධානයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වන්න. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n      'සිංහ': 'නායකත්වය සහ විශ්වාසය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. කලාත්මක ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. ආදරයේ ක්ෂේත්‍රයේ ධනාත්මක ප්‍රගතියක් අපේක්ෂා කරන්න.',\n      'කන්‍යා': 'විස්තර සහ සංවිධානය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සෞඛ්‍ය සහ සේවා කටයුතුවලට අවධානය යොමු කරන්න. කුඩා ප්‍රගතියන් ප්‍රශංසා කරන්න.',\n      'තුලා': 'සමතුලිතතාවය සහ සාධාරණත්වය අද ඔබේ මග පෙන්වනු ඇත. සම්බන්ධතා සහ හවුල්කාරිත්වයන් ශක්තිමත් කරන්න. සෞන්දර්යය සහ කලාව අගය කරන්න.',\n      'වෘශ්චික': 'ගැඹුරු අවබෝධය සහ පරිවර්තනය අද ඔබේ ප්‍රධාන තේමාවන් වනු ඇත. අභ්‍යන්තර ශක්තිය සොයා ගන්න. රහස්‍ය සහ ගවේෂණ සඳහා සුදුසු කාලයකි.',\n      'ධනු': 'ඉගෙනීම සහ ගවේෂණය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. දර්ශනය සහ ආධ්‍යාත්මිකත්වය කෙරෙහි අවධානය යොමු කරන්න. දුර ගමන් සඳහා හොඳ දිනයකි.',\n      'මකර': 'වගකීම සහ අභිලාෂය අද ඔබේ මග පෙන්වනු ඇත. දිගුකාලීන ඉලක්ක සඳහා වැඩ කරන්න. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',\n      'කුම්භ': 'නවෝත්පාදනය සහ මිත්‍රත්වය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. අනාගතය සඳහා සැලසුම් කරන්න.',\n      'මීන': 'අන්තර්ज්ඤානය සහ කරුණාව අද ඔබේ මග පෙන්වනු ඇත. ආධ්‍යාත්මික ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. කලාත්මක ප්‍රකාශනය සඳහා සුදුසු කාලයකි.'\n    };\n\n    return fallbackHoroscopes[signSinhala] || 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.';\n  }\n\n  // Clear cache for a specific sign (useful for refresh)\n  clearCache(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.delete(cacheKey);\n  }\n\n  // Clear all cache\n  clearAllCache() {\n    this.cache.clear();\n  }\n\n  // Main method to get horoscope\n  async getHoroscope(signEnglish, signSinhala, forceRefresh = false) {\n    try {\n      // Clear cache if force refresh is requested\n      if (forceRefresh) {\n        this.clearCache(signEnglish.toLowerCase());\n      }\n      \n      // Try Gemini API first\n      return await this.getHoroscopeFromGemini(signEnglish, signSinhala);\n    } catch (geminiError) {\n      console.log('Gemini API failed, trying OpenAI...');\n      \n      try {\n        // Try OpenAI API as fallback\n        return await this.getHoroscopeFromOpenAI(signEnglish, signSinhala);\n      } catch (openaiError) {\n        console.log('Both APIs failed, using fallback horoscope...');\n        \n        // Use fallback horoscope if both APIs fail\n        return this.generateFallbackHoroscope(signSinhala);\n      }\n    }\n  }\n}\n\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EAC1C;;EAEA;EACAC,kBAAkBA,CAACC,MAAM,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,IAAI,CAACL,KAAK,CAACS,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC5B,MAAME,MAAM,GAAG,IAAI,CAACV,KAAK,CAACW,GAAG,CAACH,QAAQ,CAAC;MACvC,IAAIF,IAAI,CAACM,GAAG,CAAC,CAAC,GAAGF,MAAM,CAACG,SAAS,GAAG,IAAI,CAACX,WAAW,EAAE;QACpD,OAAOQ,MAAM,CAACI,IAAI;MACpB,CAAC,MAAM;QACL,IAAI,CAACd,KAAK,CAACe,MAAM,CAACP,QAAQ,CAAC;MAC7B;IACF;IAEA,OAAO,IAAI;EACb;;EAEA;EACAQ,cAAcA,CAACZ,MAAM,EAAEa,SAAS,EAAE;IAChC,MAAMZ,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,CAACL,KAAK,CAACkB,GAAG,CAACV,QAAQ,EAAE;MACvBM,IAAI,EAAEG,SAAS;MACfJ,SAAS,EAAEP,IAAI,CAACM,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,sBAAsBA,CAACC,WAAW,EAAEC,WAAW,EAAE;IACrD,IAAI;MAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,wBAAwB;MAE3D,IAAI,CAACH,cAAc,EAAE;QACnB,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAM3B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAM2B,UAAU,GAAG5B,KAAK,CAAC6B,kBAAkB,CAAC,OAAO,EAAE;QACnDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,SAAS;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,6DAA6DnB,WAAW,KAAKC,WAAW,+CAA+CY,UAAU;AACtK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4SAA4S;MAEtS,MAAMO,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,IAAI,CAC/B,gGAAgGb,cAAc,EAAE,EAChH;QACEc,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,CAAC;YACNC,IAAI,EAAEL;UACR,CAAC;QACH,CAAC,CAAC;QACFM,gBAAgB,EAAE;UAChBC,WAAW,EAAE,GAAG;UAChBC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,IAAI;UACVC,eAAe,EAAE;QACnB;MACF,CAAC,EACD;QACEC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,OAAO,EAAE,KAAK,CAAC;MACjB,CACF,CAAC;MAED,KAAA7B,cAAA,GAAIkB,QAAQ,CAAC1B,IAAI,cAAAQ,cAAA,gBAAAC,qBAAA,GAAbD,cAAA,CAAe8B,UAAU,cAAA7B,qBAAA,gBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,gBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC6B,OAAO,cAAA5B,sBAAA,gBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCkB,KAAK,cAAAjB,sBAAA,gBAAAC,sBAAA,GAA9CD,sBAAA,CAAiD,CAAC,CAAC,cAAAC,sBAAA,eAAnDA,sBAAA,CAAqDiB,IAAI,EAAE;QAC7D,OAAOJ,QAAQ,CAAC1B,IAAI,CAACsC,UAAU,CAAC,CAAC,CAAC,CAACC,OAAO,CAACV,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI;MAC1D,CAAC,MAAM;QACL,MAAM,IAAIZ,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,cAAA;MACdC,OAAO,CAACN,KAAK,CAAC,2BAA2B,EAAE;QACzCO,OAAO,EAAEP,KAAK,CAACO,OAAO;QACtBC,MAAM,GAAAP,eAAA,GAAED,KAAK,CAACd,QAAQ,cAAAe,eAAA,uBAAdA,eAAA,CAAgBO,MAAM;QAC9BC,UAAU,GAAAP,gBAAA,GAAEF,KAAK,CAACd,QAAQ,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBO,UAAU;QACtCjD,IAAI,GAAA2C,gBAAA,GAAEH,KAAK,CAACd,QAAQ,cAAAiB,gBAAA,uBAAdA,gBAAA,CAAgB3C,IAAI;QAC1BkD,MAAM,EAAE;UACNC,GAAG,GAAAP,aAAA,GAAEJ,KAAK,CAACU,MAAM,cAAAN,aAAA,uBAAZA,aAAA,CAAcO,GAAG;UACtBC,MAAM,GAAAP,cAAA,GAAEL,KAAK,CAACU,MAAM,cAAAL,cAAA,uBAAZA,cAAA,CAAcO;QACxB;MACF,CAAC,CAAC;MACF,MAAMZ,KAAK;IACb;EACF;;EAEA;EACA,MAAMa,sBAAsBA,CAAC/C,WAAW,EAAEC,WAAW,EAAE;IACrD,IAAI;MAAA,IAAA+C,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,cAAc,GAAG3C,OAAO,CAACC,GAAG,CAAC2C,wBAAwB;MAE3D,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAIxC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAMO,MAAM,GAAG,2CAA2CnB,WAAW,KAAKC,WAAW,6LAA6L,IAAIf,IAAI,CAAC,CAAC,CAAC4B,kBAAkB,CAAC,CAAC,GAAG;MAEpT,MAAMM,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,IAAI,CAC/B,4CAA4C,EAC5C;QACEiC,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE,CACR;UACEC,IAAI,EAAE,QAAQ;UACdvB,OAAO,EAAE;QACX,CAAC,EACD;UACEuB,IAAI,EAAE,MAAM;UACZvB,OAAO,EAAEd;QACX,CAAC,CACF;QACDsC,UAAU,EAAE,GAAG;QACf/B,WAAW,EAAE;MACf,CAAC,EACD;QACEI,OAAO,EAAE;UACP,eAAe,EAAE,UAAUsB,cAAc,EAAE;UAC3C,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,KAAAJ,eAAA,GAAI5B,QAAQ,CAAC1B,IAAI,cAAAsD,eAAA,gBAAAC,qBAAA,GAAbD,eAAA,CAAeU,OAAO,cAAAT,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAyB,CAAC,CAAC,cAAAC,sBAAA,gBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BT,OAAO,cAAAU,sBAAA,eAApCA,sBAAA,CAAsClB,OAAO,EAAE;QACjD,OAAOb,QAAQ,CAAC1B,IAAI,CAACgE,OAAO,CAAC,CAAC,CAAC,CAACjB,OAAO,CAACR,OAAO;MACjD,CAAC,MAAM;QACL,MAAM,IAAIrB,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK;IACb;EACF;;EAEA;EACAyB,yBAAyBA,CAAC1D,WAAW,EAAE;IACrC,MAAM2D,kBAAkB,GAAG;MACzB,KAAK,EAAE,6HAA6H;MACpI,MAAM,EAAE,kIAAkI;MAC1I,OAAO,EAAE,kHAAkH;MAC3H,KAAK,EAAE,6HAA6H;MACpI,MAAM,EAAE,8IAA8I;MACtJ,QAAQ,EAAE,2HAA2H;MACrI,MAAM,EAAE,2HAA2H;MACnI,SAAS,EAAE,2HAA2H;MACtI,KAAK,EAAE,2HAA2H;MAClI,KAAK,EAAE,uGAAuG;MAC9G,OAAO,EAAE,uHAAuH;MAChI,KAAK,EAAE;IACT,CAAC;IAED,OAAOA,kBAAkB,CAAC3D,WAAW,CAAC,IAAI,2DAA2D;EACvG;;EAEA;EACA4D,UAAUA,CAAC7E,MAAM,EAAE;IACjB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IACrC,IAAI,CAACL,KAAK,CAACe,MAAM,CAACP,QAAQ,CAAC;EAC7B;;EAEA;EACA0E,aAAaA,CAAA,EAAG;IACd,IAAI,CAAClF,KAAK,CAACmF,KAAK,CAAC,CAAC;EACpB;;EAEA;EACA,MAAMC,YAAYA,CAAChE,WAAW,EAAEC,WAAW,EAAEgE,YAAY,GAAG,KAAK,EAAE;IACjE,IAAI;MACF;MACA,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACJ,UAAU,CAAC7D,WAAW,CAACkE,WAAW,CAAC,CAAC,CAAC;MAC5C;;MAEA;MACA,OAAO,MAAM,IAAI,CAACnE,sBAAsB,CAACC,WAAW,EAAEC,WAAW,CAAC;IACpE,CAAC,CAAC,OAAOkE,WAAW,EAAE;MACpB3B,OAAO,CAAC4B,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAI;QACF;QACA,OAAO,MAAM,IAAI,CAACrB,sBAAsB,CAAC/C,WAAW,EAAEC,WAAW,CAAC;MACpE,CAAC,CAAC,OAAOoE,WAAW,EAAE;QACpB7B,OAAO,CAAC4B,GAAG,CAAC,+CAA+C,CAAC;;QAE5D;QACA,OAAO,IAAI,CAACT,yBAAyB,CAAC1D,WAAW,CAAC;MACpD;IACF;EACF;AACF;AAEA,MAAMqE,gBAAgB,GAAG,IAAI5F,gBAAgB,CAAC,CAAC;AAC/C,eAAe4F,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}