{"ast": null, "code": "import axios from 'axios';\nclass HoroscopeService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Get horoscope from backend API\n  async getHoroscopeFromAPI(signId) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}`, {\n        timeout: 10000 // 10 second timeout\n      });\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3;\n      console.error('API error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data\n      });\n      throw error;\n    }\n  }\n\n  // Get specific category from backend API\n  async getCategoryFromAPI(signId, category) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}/${category}`, {\n        timeout: 10000 // 10 second timeout\n      });\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      var _error$response4, _error$response5, _error$response6;\n      console.error('API error details:', {\n        message: error.message,\n        status: (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.status,\n        statusText: (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.statusText,\n        data: (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data\n      });\n      throw error;\n    }\n  }\n\n  // Fallback horoscope generation (enhanced with categories)\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': {\n        love: 'අද දිනය ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ප්‍රේම සම්බන්ධතාවල ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට හොඳ අවස්ථාවක්. නව ව්‍යාපෘති ආරම්භ කිරීම සහ ගැටළු විසඳීම සඳහා ශක්තිමත් දිනයකි.',\n        health: 'ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. ව්‍යායාම සහ ක්‍රීඩා ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n        finance: 'මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට හොඳ කාලයකි. වියදම් සැලසුම් කිරීමේදී ප්‍රවේශම්කාරී වන්න.',\n        general: 'සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය රතු සහ අංකය 9.'\n      },\n      'වෘෂභ': {\n        love: 'ස්ථාවරත්වය සහ ඉවසීම ආදරය ක්ෂේත්‍රයේ ප්‍රධාන ගුණාංග වනු ඇත. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කිරීමට හොඳ දිනයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රවේශයක් අවශ්‍ය වේ. දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීම සඳහා ඉවසීම අවශ්‍ය වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ සමතුලිත ආහාර වේලක් පිළිපැදීම වැදගත් වේ. ආතතිය අවම කර ගන්න.',\n        finance: 'මූල්‍ය ස්ථාවරත්වය සහ ඉතිරිකිරීම් කෙරෙහි අවධානය යොමු කරන්න. අනවශ්‍ය වියදම් වළකින්න.',\n        general: 'ස්ථාවර ප්‍රගතිය සහ ඉවසීම අදට ප්‍රධාන තේමාවන් වේ. වාසනාවන්ත වර්ණය කොළ සහ අංකය 6.'\n      },\n      'මිථුන': {\n        love: 'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ. නව මිත්‍රත්වයන් ගොඩනැගීමට සුදුසු කාලයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා සහ නව අදහස් ඉදිරිපත් කිරීමට හොඳ අවස්ථාවක්. ගමන් සහ රැස්වීම් සාර්ථක වේ.',\n        health: 'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. නමුත් ආතතිය කළමනාකරණය කිරීම වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විවිධාංගීකරණය සහ නව ආදායම් මාර්ග සොයා ගැනීමට හොඳ කාලයකි.',\n        general: 'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගැනීමට සුදුසු දිනයකි. වාසනාවන්ත වර්ණය කහ සහ අංකය 5.'\n      },\n      'කටක': {\n        love: 'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වීම වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව සහ කණ්ඩායම් වැඩ කටයුතු සාර්ථක වේ. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n        health: 'හැඟීම්බර සෞඛ්‍යය සහ ආහාර පාන කෙරෙහි විශේෂ අවධානය යොමු කරන්න. ජල පානය වැඩි කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා සහ ආරක්ෂාව ප්‍රමුඛතාවය දෙන්න. ගෘහ ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න. වාසනාවන්ත වර්ණය සුදු සහ අංකය 2.'\n      },\n      'සිංහ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න. රොමැන්ටික් අවස්ථා සහ ප්‍රීතිමත් සිදුවීම් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සහ නිර්මාණශීලී ව්‍යාපෘති සඳහා සුදුසු කාලයකි. පිළිගැනීම ලැබේ.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී. හෘද සෞඛ්‍යය සහ ව්‍යායාම කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල විශ්වාසය සහ ධනාත්මක ආකල්පයක් ප්‍රදර්ශනය කරන්න. ආයෝජන සාර්ථක වේ.',\n        general: 'කලාත්මක ක්‍රියාකාරකම් සහ විනෝදාස්වාදය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය රන්වන් සහ අංකය 1.'\n      },\n      'කන්‍යා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ. කුඩා ප්‍රීතිමත් සිදුවීම් ප්‍රශංසා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය සහ විස්තර කෙරෙහි අවධානය ප්‍රශංසනීය ප්‍රතිඵල ගෙන දේ. සේවා කටයුතු සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ සහ ආහාර පාන සැලසුම් වැදගත් වේ. ශරීර පිරිසිදුකම පවත්වන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් සහ ගිණුම් කැටයුතු නිවැරදිව කරන්න. ඉතිරිකිරීම් වැඩි කරන්න.',\n        general: 'කුඩා ප්‍රගතියන් සහ දෛනික කටයුතු සාර්ථකව සම්පූර්ණ කරන්න. වාසනාවන්ත වර්ණය නිල් සහ අංකය 6.'\n      },\n      'තුලා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය සහ සාධාරණත්වය වැදගත් වේ. සම්බන්ධතා ශක්තිමත් කිරීමට හොඳ කාලයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සහ සමූහ වැඩ කටයුතු සාර්ථක වේ. සෞන්දර්යය සහ කලාව සම්බන්ධ ක්ෂේත්‍ර වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න. වකුගඩු සහ සමේ සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම සහ හවුල්කාරිත්ව ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'සෞන්දර්යය සහ සමගිය අගය කරන්න. වාසනාවන්ත වර්ණය රෝස සහ අංකය 7.'\n      },\n      'වෘශ්චික': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය සහ පරිවර්තනය අපේක්ෂා කරන්න. අභ්‍යන්තර ශක්තිය සොයා ගන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ සහ විශ්ලේෂණ කටයුතු සාර්ථක වේ. රහස්‍ය සහ ගැඹුරු අධ්‍යයනය සඳහා සුදුසු කාලයකි.',\n        health: 'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර සහ පුනර්ජීවනය කෙරෙහි අවධානය යොමු කරන්න. ජීර්ණ ක්‍රියාවලිය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය සහ දිගුකාලීන ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය තද රතු සහ අංකය 8.'\n      },\n      'ධනු': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ. දුර සම්බන්ධතා සහ සංස්කෘතික හුවමාරුව සාර්ථක වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම, ගමන් සහ ගුරු කටයුතු සඳහා සුදුසු කාලයකි. දර්ශනය සහ ආධ්‍යාත්මිකත්වය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ. කලවා සහ ඉණ සෞඛ්‍යය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සහ ගමන් සම්බන්ධ වියදම් සඳහා සැලසුම් කරන්න.',\n        general: 'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ජම්බු සහ අංකය 3.'\n      },\n      'මකර': {\n        love: 'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ. දිගුකාලීන සම්බන්ධතා ශක්තිමත් කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීමට හොඳ කාලයකි. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල අස්ථි සහ සම් සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න. නිතිපතා ව්‍යායාම වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන සහ දිගුකාලීන ඉතිරිකිරීම් සඳහා හොඳ කාලයකි.',\n        general: 'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න. වාසනාවන්ත වර්ණය කළු සහ අංකය 10.'\n      },\n      'කුම්භ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය සඳහා සුදුසු කාලයකි. අනාගතය සඳහා සැලසුම් කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම සහ විකල්ප වෛද්‍ය ක්‍රම වාසිදායක වේ. රුධිර සංසරණය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සහ තාක්ෂණික ක්ෂේත්‍ර වාසිදායක වේ.',\n        general: 'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ආකාශ නිල් සහ අංකය 11.'\n      },\n      'මීන': {\n        love: 'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ. ආධ්‍යාත්මික සම්බන්ධතා ශක්තිමත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය සහ සේවා කටයුතු සඳහා සුදුසු කාලයකි. අන්තර්ජ්ඤානය අනුගමනය කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය සහ මානසික සාමය වැදගත් වේ. පාද සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය සහ කරුණාව අනුගමනය කරන්න. පුණ්‍ය කටයුතු වාසිදායක වේ.',\n        general: 'ආධ්‍යාත්මික ක්‍රියාකාරකම් සහ කලාත්මක ප්‍රකාශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය මුහුදු කොළ සහ අංකය 12.'\n      }\n    };\n    const fallback = fallbackHoroscopes[signSinhala];\n    if (fallback) {\n      return {\n        categories: {\n          love: {\n            id: 'love',\n            title: 'ආදරය සහ සම්බන්ධතා',\n            emoji: '💕',\n            content: fallback.love\n          },\n          career: {\n            id: 'career',\n            title: 'වෘත්තීය ජීවිතය',\n            emoji: '💼',\n            content: fallback.career\n          },\n          health: {\n            id: 'health',\n            title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n            emoji: '🌿',\n            content: fallback.health\n          },\n          finance: {\n            id: 'finance',\n            title: 'මූල්‍ය කටයුතු',\n            emoji: '💰',\n            content: fallback.finance\n          },\n          general: {\n            id: 'general',\n            title: 'සාමාන්‍ය උපදෙස්',\n            emoji: '✨',\n            content: fallback.general\n          }\n        },\n        rawContent: Object.values(fallback).join(' '),\n        dateCreated: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString()\n      };\n    }\n\n    // Ultimate fallback\n    return {\n      categories: {\n        love: {\n          id: 'love',\n          title: 'ආදරය සහ සම්බන්ධතා',\n          emoji: '💕',\n          content: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.'\n        },\n        career: {\n          id: 'career',\n          title: 'වෘත්තීය ජීවිතය',\n          emoji: '💼',\n          content: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.'\n        },\n        health: {\n          id: 'health',\n          title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n          emoji: '🌿',\n          content: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.'\n        },\n        finance: {\n          id: 'finance',\n          title: 'මූල්‍ය කටයුතු',\n          emoji: '💰',\n          content: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.'\n        },\n        general: {\n          id: 'general',\n          title: 'සාමාන්‍ය උපදෙස්',\n          emoji: '✨',\n          content: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n        }\n      },\n      rawContent: 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.',\n      dateCreated: new Date().toISOString().split('T')[0],\n      lastUpdated: new Date().toISOString()\n    };\n  }\n\n  // Clear cache for a specific sign\n  clearCache(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.delete(cacheKey);\n  }\n\n  // Clear all cache\n  clearAllCache() {\n    this.cache.clear();\n  }\n\n  // Check API health\n  async checkAPIHealth() {\n    try {\n      const response = await axios.get(`${this.baseURL}/health`, {\n        timeout: 5000\n      });\n      return response.data.success;\n    } catch (error) {\n      console.error('API health check failed:', error.message);\n      return false;\n    }\n  }\n\n  // Main method to get horoscope (with caching and fallback)\n  async getHoroscope(signId, forceRefresh = false) {\n    try {\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const cached = this.getCachedHoroscope(signId);\n        if (cached) {\n          console.log('Returning cached horoscope for', signId);\n          return cached;\n        }\n      }\n      console.log('Fetching fresh horoscope for', signId);\n\n      // Try to get from API\n      try {\n        const apiData = await this.getHoroscopeFromAPI(signId);\n\n        // Cache the result\n        this.setCachedHoroscope(signId, apiData);\n        return apiData;\n      } catch (apiError) {\n        console.warn('API failed, using fallback horoscope:', apiError.message);\n\n        // Generate fallback horoscope\n        const fallbackData = this.generateFallbackHoroscope(signId);\n\n        // Cache the fallback (with shorter expiry)\n        this.setCachedHoroscope(signId, fallbackData, 1); // 1 hour expiry for fallback\n\n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Complete horoscope fetch failure:', error);\n\n      // Last resort: return a basic fallback\n      return this.generateFallbackHoroscope(signId);\n    }\n  }\n\n  // Generate fallback for specific category\n  generateFallbackCategory(signId, category) {\n    const signSinhala = this.getSignSinhala(signId);\n    const fallbackHoroscopes = {\n      'මේෂ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ධනාත්මක ශක්තිය සහ නව ආරම්භයන් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කරන්න.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල ධනාත්මක ප්‍රවණතා දක්නට ලැබේ.',\n        general: 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත.'\n      },\n      'වෘෂභ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ස්ථාවරත්වය සහ විශ්වාසය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රගතිය අපේක්ෂා කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ ස්ථාවරත්වය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආදායම් සහ ඉතිරිකිරීම් වැදගත් වේ.',\n        general: 'ස්ථාවරත්වය සහ ක්‍රමානුකූලත්වය අගය කරන්න.'\n      },\n      'මිථුන': {\n        love: 'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා වාසිදායක වේ.',\n        health: 'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල විවිධාංගීකරණය වැදගත් වේ.',\n        general: 'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගන්න.'\n      },\n      'කටක': {\n        love: 'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව වැදගත් වේ.',\n        health: 'හැඟීම්බර සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා ප්‍රමුඛතාවය දෙන්න.',\n        general: 'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න.'\n      },\n      'සිංහ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සාර්ථක වේ.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල විශ්වාසය ප්‍රදර්ශනය කරන්න.',\n        general: 'කලාත්මක ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'\n      },\n      'කන්‍යා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් කරන්න.',\n        general: 'කුඩා ප්‍රගතියන් සාර්ථකව සම්පූර්ණ කරන්න.'\n      },\n      'තුලා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම වැදගත් වේ.',\n        general: 'සෞන්දර්යය සහ සමගිය අගය කරන්න.'\n      },\n      'වෘශ්චික': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ කටයුතු සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය කරන්න.',\n        general: 'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න.'\n      },\n      'ධනු': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගමන් වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සලකා බලන්න.',\n        general: 'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි.'\n      },\n      'මකර': {\n        love: 'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක වැදගත් වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල අස්ථි සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන වැදගත් වේ.',\n        general: 'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න.'\n      },\n      'කුම්භ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම වාසිදායක වේ.',\n        finance: 'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සලකා බලන්න.',\n        general: 'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි.'\n      },\n      'මීන': {\n        love: 'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය අනුගමනය කරන්න.',\n        general: 'ආධ්‍යාත්මික ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'\n      }\n    };\n    const categoryTitles = {\n      love: 'ආදරය සහ සම්බන්ධතා',\n      career: 'වෘත්තීය ජීවිතය',\n      health: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n      finance: 'මූල්‍ය කටයුතු',\n      general: 'සාමාන්‍ය උපදෙස්'\n    };\n    const categoryEmojis = {\n      love: '💕',\n      career: '💼',\n      health: '🌿',\n      finance: '💰',\n      general: '✨'\n    };\n    const fallback = fallbackHoroscopes[signSinhala];\n    if (fallback && fallback[category]) {\n      return {\n        success: true,\n        data: {\n          id: category,\n          title: categoryTitles[category] || category,\n          emoji: categoryEmojis[category] || '⭐',\n          content: fallback[category]\n        },\n        dateCreated: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString()\n      };\n    }\n\n    // Ultimate fallback\n    return {\n      success: true,\n      data: {\n        id: category,\n        title: categoryTitles[category] || category,\n        emoji: categoryEmojis[category] || '⭐',\n        content: `${categoryTitles[category] || category} ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.`\n      },\n      dateCreated: new Date().toISOString().split('T')[0],\n      lastUpdated: new Date().toISOString()\n    };\n  }\n\n  // Get specific category for a sign\n  async getCategory(signId, category, forceRefresh = false) {\n    try {\n      // Check cache first (unless force refresh)\n      const cacheKey = `${signId}_${category}`;\n      if (!forceRefresh) {\n        const cached = this.getCachedHoroscope(cacheKey);\n        if (cached) {\n          console.log('Returning cached category for', signId, category);\n          return cached;\n        }\n      }\n      console.log('Fetching fresh category for', signId, category);\n\n      // Try to get from API\n      try {\n        const apiData = await this.getCategoryFromAPI(signId, category);\n\n        // Cache the result\n        this.setCachedHoroscope(cacheKey, apiData);\n        return apiData;\n      } catch (apiError) {\n        console.warn('Category API failed, using fallback:', apiError.message);\n\n        // Generate fallback for this category\n        const fallbackData = this.generateFallbackCategory(signId, category);\n\n        // Cache the fallback (with shorter expiry)\n        this.setCachedHoroscope(cacheKey, fallbackData, 1); // 1 hour expiry for fallback\n\n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Complete category fetch failure:', error);\n\n      // Last resort: return a basic fallback\n      return this.generateFallbackCategory(signId, category);\n    }\n  }\n\n  // Get structured horoscope data (for backward compatibility)\n  async getStructuredHoroscope(signEnglish, signSinhala, forceRefresh = false) {\n    const horoscopeData = await this.getHoroscope(signEnglish, signSinhala, forceRefresh);\n    return horoscopeData.categories ? Object.values(horoscopeData.categories) : [];\n  }\n\n  // Manual refresh all horoscopes (admin function)\n  async refreshAllHoroscopes() {\n    try {\n      const response = await axios.post(`${this.baseURL}/refresh-horoscopes`, {}, {\n        timeout: 60000 // 1 minute timeout for bulk operation\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Manual refresh failed:', error.message);\n      throw error;\n    }\n  }\n}\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;", "map": {"version": 3, "names": ["axios", "HoroscopeService", "constructor", "baseURL", "process", "env", "REACT_APP_API_URL", "cache", "Map", "cacheExpiry", "getCachedHoroscope", "signId", "today", "Date", "toDateString", "cache<PERSON>ey", "has", "cached", "get", "now", "timestamp", "data", "delete", "cacheHoroscope", "horoscope", "set", "getHoroscopeFromAPI", "response", "timeout", "success", "Error", "error", "_error$response", "_error$response2", "_error$response3", "console", "message", "status", "statusText", "getCategoryFromAPI", "category", "_error$response4", "_error$response5", "_error$response6", "generateFallbackHoroscope", "signSinhala", "fallbackHoroscopes", "love", "career", "health", "finance", "general", "fallback", "categories", "id", "title", "emoji", "content", "rawContent", "Object", "values", "join", "dateCreated", "toISOString", "split", "lastUpdated", "clearCache", "clearAllCache", "clear", "checkAPIHealth", "getHoroscope", "forceRefresh", "log", "apiData", "setCachedHoroscope", "apiError", "warn", "fallbackD<PERSON>", "generateFallbackCategory", "getSignSinhala", "categoryTitles", "categoryEmojis", "getCategory", "getStructuredHoroscope", "signEnglish", "horoscopeData", "refreshAllHoroscopes", "post", "horoscopeService"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass HoroscopeService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    \n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Get horoscope from backend API\n  async getHoroscopeFromAPI(signId) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}`, {\n        timeout: 10000 // 10 second timeout\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Get specific category from backend API\n  async getCategoryFromAPI(signId, category) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}/${category}`, {\n        timeout: 10000 // 10 second timeout\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Fallback horoscope generation (enhanced with categories)\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': {\n        love: 'අද දිනය ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ප්‍රේම සම්බන්ධතාවල ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට හොඳ අවස්ථාවක්. නව ව්‍යාපෘති ආරම්භ කිරීම සහ ගැටළු විසඳීම සඳහා ශක්තිමත් දිනයකි.',\n        health: 'ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. ව්‍යායාම සහ ක්‍රීඩා ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n        finance: 'මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට හොඳ කාලයකි. වියදම් සැලසුම් කිරීමේදී ප්‍රවේශම්කාරී වන්න.',\n        general: 'සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය රතු සහ අංකය 9.'\n      },\n      'වෘෂභ': {\n        love: 'ස්ථාවරත්වය සහ ඉවසීම ආදරය ක්ෂේත්‍රයේ ප්‍රධාන ගුණාංග වනු ඇත. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කිරීමට හොඳ දිනයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රවේශයක් අවශ්‍ය වේ. දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීම සඳහා ඉවසීම අවශ්‍ය වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ සමතුලිත ආහාර වේලක් පිළිපැදීම වැදගත් වේ. ආතතිය අවම කර ගන්න.',\n        finance: 'මූල්‍ය ස්ථාවරත්වය සහ ඉතිරිකිරීම් කෙරෙහි අවධානය යොමු කරන්න. අනවශ්‍ය වියදම් වළකින්න.',\n        general: 'ස්ථාවර ප්‍රගතිය සහ ඉවසීම අදට ප්‍රධාන තේමාවන් වේ. වාසනාවන්ත වර්ණය කොළ සහ අංකය 6.'\n      },\n      'මිථුන': {\n        love: 'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ. නව මිත්‍රත්වයන් ගොඩනැගීමට සුදුසු කාලයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා සහ නව අදහස් ඉදිරිපත් කිරීමට හොඳ අවස්ථාවක්. ගමන් සහ රැස්වීම් සාර්ථක වේ.',\n        health: 'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. නමුත් ආතතිය කළමනාකරණය කිරීම වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විවිධාංගීකරණය සහ නව ආදායම් මාර්ග සොයා ගැනීමට හොඳ කාලයකි.',\n        general: 'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගැනීමට සුදුසු දිනයකි. වාසනාවන්ත වර්ණය කහ සහ අංකය 5.'\n      },\n      'කටක': {\n        love: 'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වීම වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව සහ කණ්ඩායම් වැඩ කටයුතු සාර්ථක වේ. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n        health: 'හැඟීම්බර සෞඛ්‍යය සහ ආහාර පාන කෙරෙහි විශේෂ අවධානය යොමු කරන්න. ජල පානය වැඩි කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා සහ ආරක්ෂාව ප්‍රමුඛතාවය දෙන්න. ගෘහ ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න. වාසනාවන්ත වර්ණය සුදු සහ අංකය 2.'\n      },\n      'සිංහ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න. රොමැන්ටික් අවස්ථා සහ ප්‍රීතිමත් සිදුවීම් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සහ නිර්මාණශීලී ව්‍යාපෘති සඳහා සුදුසු කාලයකි. පිළිගැනීම ලැබේ.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී. හෘද සෞඛ්‍යය සහ ව්‍යායාම කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල විශ්වාසය සහ ධනාත්මක ආකල්පයක් ප්‍රදර්ශනය කරන්න. ආයෝජන සාර්ථක වේ.',\n        general: 'කලාත්මක ක්‍රියාකාරකම් සහ විනෝදාස්වාදය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය රන්වන් සහ අංකය 1.'\n      },\n      'කන්‍යා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ. කුඩා ප්‍රීතිමත් සිදුවීම් ප්‍රශංසා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය සහ විස්තර කෙරෙහි අවධානය ප්‍රශංසනීය ප්‍රතිඵල ගෙන දේ. සේවා කටයුතු සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ සහ ආහාර පාන සැලසුම් වැදගත් වේ. ශරීර පිරිසිදුකම පවත්වන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් සහ ගිණුම් කැටයුතු නිවැරදිව කරන්න. ඉතිරිකිරීම් වැඩි කරන්න.',\n        general: 'කුඩා ප්‍රගතියන් සහ දෛනික කටයුතු සාර්ථකව සම්පූර්ණ කරන්න. වාසනාවන්ත වර්ණය නිල් සහ අංකය 6.'\n      },\n      'තුලා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය සහ සාධාරණත්වය වැදගත් වේ. සම්බන්ධතා ශක්තිමත් කිරීමට හොඳ කාලයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සහ සමූහ වැඩ කටයුතු සාර්ථක වේ. සෞන්දර්යය සහ කලාව සම්බන්ධ ක්ෂේත්‍ර වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න. වකුගඩු සහ සමේ සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම සහ හවුල්කාරිත්ව ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'සෞන්දර්යය සහ සමගිය අගය කරන්න. වාසනාවන්ත වර්ණය රෝස සහ අංකය 7.'\n      },\n      'වෘශ්චික': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය සහ පරිවර්තනය අපේක්ෂා කරන්න. අභ්‍යන්තර ශක්තිය සොයා ගන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ සහ විශ්ලේෂණ කටයුතු සාර්ථක වේ. රහස්‍ය සහ ගැඹුරු අධ්‍යයනය සඳහා සුදුසු කාලයකි.',\n        health: 'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර සහ පුනර්ජීවනය කෙරෙහි අවධානය යොමු කරන්න. ජීර්ණ ක්‍රියාවලිය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය සහ දිගුකාලීන ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය තද රතු සහ අංකය 8.'\n      },\n      'ධනු': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ. දුර සම්බන්ධතා සහ සංස්කෘතික හුවමාරුව සාර්ථක වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම, ගමන් සහ ගුරු කටයුතු සඳහා සුදුසු කාලයකි. දර්ශනය සහ ආධ්‍යාත්මිකත්වය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ. කලවා සහ ඉණ සෞඛ්‍යය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සහ ගමන් සම්බන්ධ වියදම් සඳහා සැලසුම් කරන්න.',\n        general: 'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ජම්බු සහ අංකය 3.'\n      },\n      'මකර': {\n        love: 'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ. දිගුකාලීන සම්බන්ධතා ශක්තිමත් කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීමට හොඳ කාලයකි. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල අස්ථි සහ සම් සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න. නිතිපතා ව්‍යායාම වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන සහ දිගුකාලීන ඉතිරිකිරීම් සඳහා හොඳ කාලයකි.',\n        general: 'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න. වාසනාවන්ත වර්ණය කළු සහ අංකය 10.'\n      },\n      'කුම්භ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය සඳහා සුදුසු කාලයකි. අනාගතය සඳහා සැලසුම් කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම සහ විකල්ප වෛද්‍ය ක්‍රම වාසිදායක වේ. රුධිර සංසරණය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සහ තාක්ෂණික ක්ෂේත්‍ර වාසිදායක වේ.',\n        general: 'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ආකාශ නිල් සහ අංකය 11.'\n      },\n      'මීන': {\n        love: 'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ. ආධ්‍යාත්මික සම්බන්ධතා ශක්තිමත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය සහ සේවා කටයුතු සඳහා සුදුසු කාලයකි. අන්තර්ජ්ඤානය අනුගමනය කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය සහ මානසික සාමය වැදගත් වේ. පාද සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය සහ කරුණාව අනුගමනය කරන්න. පුණ්‍ය කටයුතු වාසිදායක වේ.',\n        general: 'ආධ්‍යාත්මික ක්‍රියාකාරකම් සහ කලාත්මක ප්‍රකාශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය මුහුදු කොළ සහ අංකය 12.'\n      }\n    };\n\n    const fallback = fallbackHoroscopes[signSinhala];\n    if (fallback) {\n      return {\n        categories: {\n          love: {\n            id: 'love',\n            title: 'ආදරය සහ සම්බන්ධතා',\n            emoji: '💕',\n            content: fallback.love\n          },\n          career: {\n            id: 'career',\n            title: 'වෘත්තීය ජීවිතය',\n            emoji: '💼',\n            content: fallback.career\n          },\n          health: {\n            id: 'health',\n            title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n            emoji: '🌿',\n            content: fallback.health\n          },\n          finance: {\n            id: 'finance',\n            title: 'මූල්‍ය කටයුතු',\n            emoji: '💰',\n            content: fallback.finance\n          },\n          general: {\n            id: 'general',\n            title: 'සාමාන්‍ය උපදෙස්',\n            emoji: '✨',\n            content: fallback.general\n          }\n        },\n        rawContent: Object.values(fallback).join(' '),\n        dateCreated: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString()\n      };\n    }\n\n    // Ultimate fallback\n    return {\n      categories: {\n        love: {\n          id: 'love',\n          title: 'ආදරය සහ සම්බන්ධතා',\n          emoji: '💕',\n          content: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.'\n        },\n        career: {\n          id: 'career',\n          title: 'වෘත්තීය ජීවිතය',\n          emoji: '💼',\n          content: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.'\n        },\n        health: {\n          id: 'health',\n          title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n          emoji: '🌿',\n          content: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.'\n        },\n        finance: {\n          id: 'finance',\n          title: 'මූල්‍ය කටයුතු',\n          emoji: '💰',\n          content: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.'\n        },\n        general: {\n          id: 'general',\n          title: 'සාමාන්‍ය උපදෙස්',\n          emoji: '✨',\n          content: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n        }\n      },\n      rawContent: 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.',\n      dateCreated: new Date().toISOString().split('T')[0],\n      lastUpdated: new Date().toISOString()\n    };\n  }\n\n  // Clear cache for a specific sign\n  clearCache(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.delete(cacheKey);\n  }\n\n  // Clear all cache\n  clearAllCache() {\n    this.cache.clear();\n  }\n\n  // Check API health\n  async checkAPIHealth() {\n    try {\n      const response = await axios.get(`${this.baseURL}/health`, {\n        timeout: 5000\n      });\n      return response.data.success;\n    } catch (error) {\n      console.error('API health check failed:', error.message);\n      return false;\n    }\n  }\n\n  // Main method to get horoscope (with caching and fallback)\n  async getHoroscope(signId, forceRefresh = false) {\n    try {\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const cached = this.getCachedHoroscope(signId);\n        if (cached) {\n          console.log('Returning cached horoscope for', signId);\n          return cached;\n        }\n      }\n\n      console.log('Fetching fresh horoscope for', signId);\n      \n      // Try to get from API\n      try {\n        const apiData = await this.getHoroscopeFromAPI(signId);\n        \n        // Cache the result\n        this.setCachedHoroscope(signId, apiData);\n        \n        return apiData;\n      } catch (apiError) {\n        console.warn('API failed, using fallback horoscope:', apiError.message);\n        \n        // Generate fallback horoscope\n        const fallbackData = this.generateFallbackHoroscope(signId);\n        \n        // Cache the fallback (with shorter expiry)\n        this.setCachedHoroscope(signId, fallbackData, 1); // 1 hour expiry for fallback\n        \n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Complete horoscope fetch failure:', error);\n      \n      // Last resort: return a basic fallback\n      return this.generateFallbackHoroscope(signId);\n    }\n  }\n\n  // Generate fallback for specific category\n  generateFallbackCategory(signId, category) {\n    const signSinhala = this.getSignSinhala(signId);\n    const fallbackHoroscopes = {\n      'මේෂ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ධනාත්මක ශක්තිය සහ නව ආරම්භයන් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කරන්න.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල ධනාත්මක ප්‍රවණතා දක්නට ලැබේ.',\n        general: 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත.'\n      },\n      'වෘෂභ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ස්ථාවරත්වය සහ විශ්වාසය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රගතිය අපේක්ෂා කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ ස්ථාවරත්වය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආදායම් සහ ඉතිරිකිරීම් වැදගත් වේ.',\n        general: 'ස්ථාවරත්වය සහ ක්‍රමානුකූලත්වය අගය කරන්න.'\n      },\n      'මිථුන': {\n        love: 'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා වාසිදායක වේ.',\n        health: 'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල විවිධාංගීකරණය වැදගත් වේ.',\n        general: 'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගන්න.'\n      },\n      'කටක': {\n        love: 'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව වැදගත් වේ.',\n        health: 'හැඟීම්බර සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා ප්‍රමුඛතාවය දෙන්න.',\n        general: 'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න.'\n      },\n      'සිංහ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සාර්ථක වේ.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල විශ්වාසය ප්‍රදර්ශනය කරන්න.',\n        general: 'කලාත්මක ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'\n      },\n      'කන්‍යා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් කරන්න.',\n        general: 'කුඩා ප්‍රගතියන් සාර්ථකව සම්පූර්ණ කරන්න.'\n      },\n      'තුලා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම වැදගත් වේ.',\n        general: 'සෞන්දර්යය සහ සමගිය අගය කරන්න.'\n      },\n      'වෘශ්චික': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ කටයුතු සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය කරන්න.',\n        general: 'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න.'\n      },\n      'ධනු': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගමන් වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සලකා බලන්න.',\n        general: 'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි.'\n      },\n      'මකර': {\n        love: 'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක වැදගත් වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල අස්ථි සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන වැදගත් වේ.',\n        general: 'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න.'\n      },\n      'කුම්භ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම වාසිදායක වේ.',\n        finance: 'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සලකා බලන්න.',\n        general: 'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි.'\n      },\n      'මීන': {\n        love: 'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය අනුගමනය කරන්න.',\n        general: 'ආධ්‍යාත්මික ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'\n      }\n    };\n\n    const categoryTitles = {\n      love: 'ආදරය සහ සම්බන්ධතා',\n      career: 'වෘත්තීය ජීවිතය',\n      health: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n      finance: 'මූල්‍ය කටයුතු',\n      general: 'සාමාන්‍ය උපදෙස්'\n    };\n\n    const categoryEmojis = {\n      love: '💕',\n      career: '💼',\n      health: '🌿',\n      finance: '💰',\n      general: '✨'\n    };\n\n    const fallback = fallbackHoroscopes[signSinhala];\n    if (fallback && fallback[category]) {\n      return {\n        success: true,\n        data: {\n          id: category,\n          title: categoryTitles[category] || category,\n          emoji: categoryEmojis[category] || '⭐',\n          content: fallback[category]\n        },\n        dateCreated: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString()\n      };\n    }\n\n    // Ultimate fallback\n    return {\n      success: true,\n      data: {\n        id: category,\n        title: categoryTitles[category] || category,\n        emoji: categoryEmojis[category] || '⭐',\n        content: `${categoryTitles[category] || category} ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.`\n      },\n      dateCreated: new Date().toISOString().split('T')[0],\n      lastUpdated: new Date().toISOString()\n    };\n  }\n\n  // Get specific category for a sign\n  async getCategory(signId, category, forceRefresh = false) {\n    try {\n      // Check cache first (unless force refresh)\n      const cacheKey = `${signId}_${category}`;\n      if (!forceRefresh) {\n        const cached = this.getCachedHoroscope(cacheKey);\n        if (cached) {\n          console.log('Returning cached category for', signId, category);\n          return cached;\n        }\n      }\n\n      console.log('Fetching fresh category for', signId, category);\n      \n      // Try to get from API\n      try {\n        const apiData = await this.getCategoryFromAPI(signId, category);\n        \n        // Cache the result\n        this.setCachedHoroscope(cacheKey, apiData);\n        \n        return apiData;\n      } catch (apiError) {\n        console.warn('Category API failed, using fallback:', apiError.message);\n        \n        // Generate fallback for this category\n        const fallbackData = this.generateFallbackCategory(signId, category);\n        \n        // Cache the fallback (with shorter expiry)\n        this.setCachedHoroscope(cacheKey, fallbackData, 1); // 1 hour expiry for fallback\n        \n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Complete category fetch failure:', error);\n      \n      // Last resort: return a basic fallback\n      return this.generateFallbackCategory(signId, category);\n    }\n  }\n\n  // Get structured horoscope data (for backward compatibility)\n  async getStructuredHoroscope(signEnglish, signSinhala, forceRefresh = false) {\n    const horoscopeData = await this.getHoroscope(signEnglish, signSinhala, forceRefresh);\n    return horoscopeData.categories ? Object.values(horoscopeData.categories) : [];\n  }\n\n  // Manual refresh all horoscopes (admin function)\n  async refreshAllHoroscopes() {\n    try {\n      const response = await axios.post(`${this.baseURL}/refresh-horoscopes`, {}, {\n        timeout: 60000 // 1 minute timeout for bulk operation\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Manual refresh failed:', error.message);\n      throw error;\n    }\n  }\n}\n\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;IAC3E,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EAC1C;;EAEA;EACAC,kBAAkBA,CAACC,MAAM,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,IAAI,CAACL,KAAK,CAACS,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC5B,MAAME,MAAM,GAAG,IAAI,CAACV,KAAK,CAACW,GAAG,CAACH,QAAQ,CAAC;MACvC,IAAIF,IAAI,CAACM,GAAG,CAAC,CAAC,GAAGF,MAAM,CAACG,SAAS,GAAG,IAAI,CAACX,WAAW,EAAE;QACpD,OAAOQ,MAAM,CAACI,IAAI;MACpB,CAAC,MAAM;QACL,IAAI,CAACd,KAAK,CAACe,MAAM,CAACP,QAAQ,CAAC;MAC7B;IACF;IAEA,OAAO,IAAI;EACb;;EAEA;EACAQ,cAAcA,CAACZ,MAAM,EAAEa,SAAS,EAAE;IAChC,MAAMZ,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,CAACL,KAAK,CAACkB,GAAG,CAACV,QAAQ,EAAE;MACvBM,IAAI,EAAEG,SAAS;MACfJ,SAAS,EAAEP,IAAI,CAACM,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,mBAAmBA,CAACf,MAAM,EAAE;IAChC,IAAI;MACF,MAAMgB,QAAQ,GAAG,MAAM3B,KAAK,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACf,OAAO,cAAcQ,MAAM,EAAE,EAAE;QACtEiB,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MAEF,IAAID,QAAQ,CAACN,IAAI,IAAIM,QAAQ,CAACN,IAAI,CAACQ,OAAO,EAAE;QAC1C,OAAOF,QAAQ,CAACN,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIS,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdC,OAAO,CAACJ,KAAK,CAAC,oBAAoB,EAAE;QAClCK,OAAO,EAAEL,KAAK,CAACK,OAAO;QACtBC,MAAM,GAAAL,eAAA,GAAED,KAAK,CAACJ,QAAQ,cAAAK,eAAA,uBAAdA,eAAA,CAAgBK,MAAM;QAC9BC,UAAU,GAAAL,gBAAA,GAAEF,KAAK,CAACJ,QAAQ,cAAAM,gBAAA,uBAAdA,gBAAA,CAAgBK,UAAU;QACtCjB,IAAI,GAAAa,gBAAA,GAAEH,KAAK,CAACJ,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBb;MACxB,CAAC,CAAC;MACF,MAAMU,KAAK;IACb;EACF;;EAEA;EACA,MAAMQ,kBAAkBA,CAAC5B,MAAM,EAAE6B,QAAQ,EAAE;IACzC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAM3B,KAAK,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACf,OAAO,cAAcQ,MAAM,IAAI6B,QAAQ,EAAE,EAAE;QAClFZ,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MAEF,IAAID,QAAQ,CAACN,IAAI,IAAIM,QAAQ,CAACN,IAAI,CAACQ,OAAO,EAAE;QAC1C,OAAOF,QAAQ,CAACN,IAAI,CAACA,IAAI;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIS,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAU,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdR,OAAO,CAACJ,KAAK,CAAC,oBAAoB,EAAE;QAClCK,OAAO,EAAEL,KAAK,CAACK,OAAO;QACtBC,MAAM,GAAAI,gBAAA,GAAEV,KAAK,CAACJ,QAAQ,cAAAc,gBAAA,uBAAdA,gBAAA,CAAgBJ,MAAM;QAC9BC,UAAU,GAAAI,gBAAA,GAAEX,KAAK,CAACJ,QAAQ,cAAAe,gBAAA,uBAAdA,gBAAA,CAAgBJ,UAAU;QACtCjB,IAAI,GAAAsB,gBAAA,GAAEZ,KAAK,CAACJ,QAAQ,cAAAgB,gBAAA,uBAAdA,gBAAA,CAAgBtB;MACxB,CAAC,CAAC;MACF,MAAMU,KAAK;IACb;EACF;;EAEA;EACAa,yBAAyBA,CAACC,WAAW,EAAE;IACrC,MAAMC,kBAAkB,GAAG;MACzB,KAAK,EAAE;QACLC,IAAI,EAAE,iJAAiJ;QACvJC,MAAM,EAAE,mIAAmI;QAC3IC,MAAM,EAAE,0GAA0G;QAClHC,OAAO,EAAE,qGAAqG;QAC9GC,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNJ,IAAI,EAAE,qHAAqH;QAC3HC,MAAM,EAAE,8GAA8G;QACtHC,MAAM,EAAE,uFAAuF;QAC/FC,OAAO,EAAE,oFAAoF;QAC7FC,OAAO,EAAE;MACX,CAAC;MACD,OAAO,EAAE;QACPJ,IAAI,EAAE,0FAA0F;QAChGC,MAAM,EAAE,4GAA4G;QACpHC,MAAM,EAAE,gFAAgF;QACxFC,OAAO,EAAE,0EAA0E;QACnFC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,yFAAyF;QAC/FC,MAAM,EAAE,8GAA8G;QACtHC,MAAM,EAAE,kFAAkF;QAC1FC,OAAO,EAAE,yFAAyF;QAClGC,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNJ,IAAI,EAAE,gHAAgH;QACtHC,MAAM,EAAE,mGAAmG;QAC3GC,MAAM,EAAE,mFAAmF;QAC3FC,OAAO,EAAE,iFAAiF;QAC1FC,OAAO,EAAE;MACX,CAAC;MACD,QAAQ,EAAE;QACRJ,IAAI,EAAE,4FAA4F;QAClGC,MAAM,EAAE,wGAAwG;QAChHC,MAAM,EAAE,0FAA0F;QAClGC,OAAO,EAAE,2FAA2F;QACpGC,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNJ,IAAI,EAAE,4FAA4F;QAClGC,MAAM,EAAE,kHAAkH;QAC1HC,MAAM,EAAE,uFAAuF;QAC/FC,OAAO,EAAE,yEAAyE;QAClFC,OAAO,EAAE;MACX,CAAC;MACD,SAAS,EAAE;QACTJ,IAAI,EAAE,uFAAuF;QAC7FC,MAAM,EAAE,sGAAsG;QAC9GC,MAAM,EAAE,gGAAgG;QACxGC,OAAO,EAAE,sEAAsE;QAC/EC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,4FAA4F;QAClGC,MAAM,EAAE,2GAA2G;QACnHC,MAAM,EAAE,6FAA6F;QACrGC,OAAO,EAAE,0EAA0E;QACnFC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,oFAAoF;QAC1FC,MAAM,EAAE,6GAA6G;QACrHC,MAAM,EAAE,4FAA4F;QACpGC,OAAO,EAAE,wEAAwE;QACjFC,OAAO,EAAE;MACX,CAAC;MACD,OAAO,EAAE;QACPJ,IAAI,EAAE,wFAAwF;QAC9FC,MAAM,EAAE,yFAAyF;QACjGC,MAAM,EAAE,kGAAkG;QAC1GC,OAAO,EAAE,oEAAoE;QAC7EC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,sFAAsF;QAC5FC,MAAM,EAAE,qGAAqG;QAC7GC,MAAM,EAAE,kGAAkG;QAC1GC,OAAO,EAAE,kFAAkF;QAC3FC,OAAO,EAAE;MACX;IACF,CAAC;IAED,MAAMC,QAAQ,GAAGN,kBAAkB,CAACD,WAAW,CAAC;IAChD,IAAIO,QAAQ,EAAE;MACZ,OAAO;QACLC,UAAU,EAAE;UACVN,IAAI,EAAE;YACJO,EAAE,EAAE,MAAM;YACVC,KAAK,EAAE,mBAAmB;YAC1BC,KAAK,EAAE,IAAI;YACXC,OAAO,EAAEL,QAAQ,CAACL;UACpB,CAAC;UACDC,MAAM,EAAE;YACNM,EAAE,EAAE,QAAQ;YACZC,KAAK,EAAE,gBAAgB;YACvBC,KAAK,EAAE,IAAI;YACXC,OAAO,EAAEL,QAAQ,CAACJ;UACpB,CAAC;UACDC,MAAM,EAAE;YACNK,EAAE,EAAE,QAAQ;YACZC,KAAK,EAAE,qBAAqB;YAC5BC,KAAK,EAAE,IAAI;YACXC,OAAO,EAAEL,QAAQ,CAACH;UACpB,CAAC;UACDC,OAAO,EAAE;YACPI,EAAE,EAAE,SAAS;YACbC,KAAK,EAAE,eAAe;YACtBC,KAAK,EAAE,IAAI;YACXC,OAAO,EAAEL,QAAQ,CAACF;UACpB,CAAC;UACDC,OAAO,EAAE;YACPG,EAAE,EAAE,SAAS;YACbC,KAAK,EAAE,iBAAiB;YACxBC,KAAK,EAAE,GAAG;YACVC,OAAO,EAAEL,QAAQ,CAACD;UACpB;QACF,CAAC;QACDO,UAAU,EAAEC,MAAM,CAACC,MAAM,CAACR,QAAQ,CAAC,CAACS,IAAI,CAAC,GAAG,CAAC;QAC7CC,WAAW,EAAE,IAAIjD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnDC,WAAW,EAAE,IAAIpD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC;MACtC,CAAC;IACH;;IAEA;IACA,OAAO;MACLV,UAAU,EAAE;QACVN,IAAI,EAAE;UACJO,EAAE,EAAE,MAAM;UACVC,KAAK,EAAE,mBAAmB;UAC1BC,KAAK,EAAE,IAAI;UACXC,OAAO,EAAE;QACX,CAAC;QACDT,MAAM,EAAE;UACNM,EAAE,EAAE,QAAQ;UACZC,KAAK,EAAE,gBAAgB;UACvBC,KAAK,EAAE,IAAI;UACXC,OAAO,EAAE;QACX,CAAC;QACDR,MAAM,EAAE;UACNK,EAAE,EAAE,QAAQ;UACZC,KAAK,EAAE,qBAAqB;UAC5BC,KAAK,EAAE,IAAI;UACXC,OAAO,EAAE;QACX,CAAC;QACDP,OAAO,EAAE;UACPI,EAAE,EAAE,SAAS;UACbC,KAAK,EAAE,eAAe;UACtBC,KAAK,EAAE,IAAI;UACXC,OAAO,EAAE;QACX,CAAC;QACDN,OAAO,EAAE;UACPG,EAAE,EAAE,SAAS;UACbC,KAAK,EAAE,iBAAiB;UACxBC,KAAK,EAAE,GAAG;UACVC,OAAO,EAAE;QACX;MACF,CAAC;MACDC,UAAU,EAAE,2DAA2D;MACvEI,WAAW,EAAE,IAAIjD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnDC,WAAW,EAAE,IAAIpD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC;IACtC,CAAC;EACH;;EAEA;EACAG,UAAUA,CAACvD,MAAM,EAAE;IACjB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IACrC,IAAI,CAACL,KAAK,CAACe,MAAM,CAACP,QAAQ,CAAC;EAC7B;;EAEA;EACAoD,aAAaA,CAAA,EAAG;IACd,IAAI,CAAC5D,KAAK,CAAC6D,KAAK,CAAC,CAAC;EACpB;;EAEA;EACA,MAAMC,cAAcA,CAAA,EAAG;IACrB,IAAI;MACF,MAAM1C,QAAQ,GAAG,MAAM3B,KAAK,CAACkB,GAAG,CAAC,GAAG,IAAI,CAACf,OAAO,SAAS,EAAE;QACzDyB,OAAO,EAAE;MACX,CAAC,CAAC;MACF,OAAOD,QAAQ,CAACN,IAAI,CAACQ,OAAO;IAC9B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAACK,OAAO,CAAC;MACxD,OAAO,KAAK;IACd;EACF;;EAEA;EACA,MAAMkC,YAAYA,CAAC3D,MAAM,EAAE4D,YAAY,GAAG,KAAK,EAAE;IAC/C,IAAI;MACF;MACA,IAAI,CAACA,YAAY,EAAE;QACjB,MAAMtD,MAAM,GAAG,IAAI,CAACP,kBAAkB,CAACC,MAAM,CAAC;QAC9C,IAAIM,MAAM,EAAE;UACVkB,OAAO,CAACqC,GAAG,CAAC,gCAAgC,EAAE7D,MAAM,CAAC;UACrD,OAAOM,MAAM;QACf;MACF;MAEAkB,OAAO,CAACqC,GAAG,CAAC,8BAA8B,EAAE7D,MAAM,CAAC;;MAEnD;MACA,IAAI;QACF,MAAM8D,OAAO,GAAG,MAAM,IAAI,CAAC/C,mBAAmB,CAACf,MAAM,CAAC;;QAEtD;QACA,IAAI,CAAC+D,kBAAkB,CAAC/D,MAAM,EAAE8D,OAAO,CAAC;QAExC,OAAOA,OAAO;MAChB,CAAC,CAAC,OAAOE,QAAQ,EAAE;QACjBxC,OAAO,CAACyC,IAAI,CAAC,uCAAuC,EAAED,QAAQ,CAACvC,OAAO,CAAC;;QAEvE;QACA,MAAMyC,YAAY,GAAG,IAAI,CAACjC,yBAAyB,CAACjC,MAAM,CAAC;;QAE3D;QACA,IAAI,CAAC+D,kBAAkB,CAAC/D,MAAM,EAAEkE,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;;QAElD,OAAOA,YAAY;MACrB;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;MAEzD;MACA,OAAO,IAAI,CAACa,yBAAyB,CAACjC,MAAM,CAAC;IAC/C;EACF;;EAEA;EACAmE,wBAAwBA,CAACnE,MAAM,EAAE6B,QAAQ,EAAE;IACzC,MAAMK,WAAW,GAAG,IAAI,CAACkC,cAAc,CAACpE,MAAM,CAAC;IAC/C,MAAMmC,kBAAkB,GAAG;MACzB,KAAK,EAAE;QACLC,IAAI,EAAE,8DAA8D;QACpEC,MAAM,EAAE,qDAAqD;QAC7DC,MAAM,EAAE,iCAAiC;QACzCC,OAAO,EAAE,8CAA8C;QACvDC,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNJ,IAAI,EAAE,mDAAmD;QACzDC,MAAM,EAAE,wDAAwD;QAChEC,MAAM,EAAE,kDAAkD;QAC1DC,OAAO,EAAE,yDAAyD;QAClEC,OAAO,EAAE;MACX,CAAC;MACD,OAAO,EAAE;QACPJ,IAAI,EAAE,iDAAiD;QACvDC,MAAM,EAAE,kDAAkD;QAC1DC,MAAM,EAAE,yCAAyC;QACjDC,OAAO,EAAE,0CAA0C;QACnDC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,iDAAiD;QACvDC,MAAM,EAAE,yCAAyC;QACjDC,MAAM,EAAE,4CAA4C;QACpDC,OAAO,EAAE,mDAAmD;QAC5DC,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNJ,IAAI,EAAE,wDAAwD;QAC9DC,MAAM,EAAE,iDAAiD;QACzDC,MAAM,EAAE,iCAAiC;QACzCC,OAAO,EAAE,4CAA4C;QACrDC,OAAO,EAAE;MACX,CAAC;MACD,QAAQ,EAAE;QACRJ,IAAI,EAAE,mDAAmD;QACzDC,MAAM,EAAE,0CAA0C;QAClDC,MAAM,EAAE,4CAA4C;QACpDC,OAAO,EAAE,wCAAwC;QACjDC,OAAO,EAAE;MACX,CAAC;MACD,MAAM,EAAE;QACNJ,IAAI,EAAE,wCAAwC;QAC9CC,MAAM,EAAE,+CAA+C;QACvDC,MAAM,EAAE,uCAAuC;QAC/CC,OAAO,EAAE,6CAA6C;QACtDC,OAAO,EAAE;MACX,CAAC;MACD,SAAS,EAAE;QACTJ,IAAI,EAAE,8CAA8C;QACpDC,MAAM,EAAE,4CAA4C;QACpDC,MAAM,EAAE,qDAAqD;QAC7DC,OAAO,EAAE,yCAAyC;QAClDC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,6CAA6C;QACnDC,MAAM,EAAE,gDAAgD;QACxDC,MAAM,EAAE,+DAA+D;QACvEC,OAAO,EAAE,2CAA2C;QACpDC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,gDAAgD;QACtDC,MAAM,EAAE,0DAA0D;QAClEC,MAAM,EAAE,yDAAyD;QACjEC,OAAO,EAAE,yCAAyC;QAClDC,OAAO,EAAE;MACX,CAAC;MACD,OAAO,EAAE;QACPJ,IAAI,EAAE,qDAAqD;QAC3DC,MAAM,EAAE,uDAAuD;QAC/DC,MAAM,EAAE,mDAAmD;QAC3DC,OAAO,EAAE,8CAA8C;QACvDC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,EAAE;QACLJ,IAAI,EAAE,mDAAmD;QACzDC,MAAM,EAAE,mDAAmD;QAC3DC,MAAM,EAAE,6CAA6C;QACrDC,OAAO,EAAE,6CAA6C;QACtDC,OAAO,EAAE;MACX;IACF,CAAC;IAED,MAAM6B,cAAc,GAAG;MACrBjC,IAAI,EAAE,mBAAmB;MACzBC,MAAM,EAAE,gBAAgB;MACxBC,MAAM,EAAE,qBAAqB;MAC7BC,OAAO,EAAE,eAAe;MACxBC,OAAO,EAAE;IACX,CAAC;IAED,MAAM8B,cAAc,GAAG;MACrBlC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE;IACX,CAAC;IAED,MAAMC,QAAQ,GAAGN,kBAAkB,CAACD,WAAW,CAAC;IAChD,IAAIO,QAAQ,IAAIA,QAAQ,CAACZ,QAAQ,CAAC,EAAE;MAClC,OAAO;QACLX,OAAO,EAAE,IAAI;QACbR,IAAI,EAAE;UACJiC,EAAE,EAAEd,QAAQ;UACZe,KAAK,EAAEyB,cAAc,CAACxC,QAAQ,CAAC,IAAIA,QAAQ;UAC3CgB,KAAK,EAAEyB,cAAc,CAACzC,QAAQ,CAAC,IAAI,GAAG;UACtCiB,OAAO,EAAEL,QAAQ,CAACZ,QAAQ;QAC5B,CAAC;QACDsB,WAAW,EAAE,IAAIjD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACnDC,WAAW,EAAE,IAAIpD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC;MACtC,CAAC;IACH;;IAEA;IACA,OAAO;MACLlC,OAAO,EAAE,IAAI;MACbR,IAAI,EAAE;QACJiC,EAAE,EAAEd,QAAQ;QACZe,KAAK,EAAEyB,cAAc,CAACxC,QAAQ,CAAC,IAAIA,QAAQ;QAC3CgB,KAAK,EAAEyB,cAAc,CAACzC,QAAQ,CAAC,IAAI,GAAG;QACtCiB,OAAO,EAAE,GAAGuB,cAAc,CAACxC,QAAQ,CAAC,IAAIA,QAAQ;MAClD,CAAC;MACDsB,WAAW,EAAE,IAAIjD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACnDC,WAAW,EAAE,IAAIpD,IAAI,CAAC,CAAC,CAACkD,WAAW,CAAC;IACtC,CAAC;EACH;;EAEA;EACA,MAAMmB,WAAWA,CAACvE,MAAM,EAAE6B,QAAQ,EAAE+B,YAAY,GAAG,KAAK,EAAE;IACxD,IAAI;MACF;MACA,MAAMxD,QAAQ,GAAG,GAAGJ,MAAM,IAAI6B,QAAQ,EAAE;MACxC,IAAI,CAAC+B,YAAY,EAAE;QACjB,MAAMtD,MAAM,GAAG,IAAI,CAACP,kBAAkB,CAACK,QAAQ,CAAC;QAChD,IAAIE,MAAM,EAAE;UACVkB,OAAO,CAACqC,GAAG,CAAC,+BAA+B,EAAE7D,MAAM,EAAE6B,QAAQ,CAAC;UAC9D,OAAOvB,MAAM;QACf;MACF;MAEAkB,OAAO,CAACqC,GAAG,CAAC,6BAA6B,EAAE7D,MAAM,EAAE6B,QAAQ,CAAC;;MAE5D;MACA,IAAI;QACF,MAAMiC,OAAO,GAAG,MAAM,IAAI,CAAClC,kBAAkB,CAAC5B,MAAM,EAAE6B,QAAQ,CAAC;;QAE/D;QACA,IAAI,CAACkC,kBAAkB,CAAC3D,QAAQ,EAAE0D,OAAO,CAAC;QAE1C,OAAOA,OAAO;MAChB,CAAC,CAAC,OAAOE,QAAQ,EAAE;QACjBxC,OAAO,CAACyC,IAAI,CAAC,sCAAsC,EAAED,QAAQ,CAACvC,OAAO,CAAC;;QAEtE;QACA,MAAMyC,YAAY,GAAG,IAAI,CAACC,wBAAwB,CAACnE,MAAM,EAAE6B,QAAQ,CAAC;;QAEpE;QACA,IAAI,CAACkC,kBAAkB,CAAC3D,QAAQ,EAAE8D,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEpD,OAAOA,YAAY;MACrB;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;;MAExD;MACA,OAAO,IAAI,CAAC+C,wBAAwB,CAACnE,MAAM,EAAE6B,QAAQ,CAAC;IACxD;EACF;;EAEA;EACA,MAAM2C,sBAAsBA,CAACC,WAAW,EAAEvC,WAAW,EAAE0B,YAAY,GAAG,KAAK,EAAE;IAC3E,MAAMc,aAAa,GAAG,MAAM,IAAI,CAACf,YAAY,CAACc,WAAW,EAAEvC,WAAW,EAAE0B,YAAY,CAAC;IACrF,OAAOc,aAAa,CAAChC,UAAU,GAAGM,MAAM,CAACC,MAAM,CAACyB,aAAa,CAAChC,UAAU,CAAC,GAAG,EAAE;EAChF;;EAEA;EACA,MAAMiC,oBAAoBA,CAAA,EAAG;IAC3B,IAAI;MACF,MAAM3D,QAAQ,GAAG,MAAM3B,KAAK,CAACuF,IAAI,CAAC,GAAG,IAAI,CAACpF,OAAO,qBAAqB,EAAE,CAAC,CAAC,EAAE;QAC1EyB,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MACF,OAAOD,QAAQ,CAACN,IAAI;IACtB,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdI,OAAO,CAACJ,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAACK,OAAO,CAAC;MACtD,MAAML,KAAK;IACb;EACF;AACF;AAEA,MAAMyD,gBAAgB,GAAG,IAAIvF,gBAAgB,CAAC,CAAC;AAC/C,eAAeuF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}