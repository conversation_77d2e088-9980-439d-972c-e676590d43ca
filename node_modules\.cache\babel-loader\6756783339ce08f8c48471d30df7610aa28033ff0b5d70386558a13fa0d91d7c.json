{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\n\n// Headers whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nvar ignoreDuplicateOf = ['age', 'authorization', 'content-length', 'content-type', 'etag', 'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since', 'last-modified', 'location', 'max-forwards', 'proxy-authorization', 'referer', 'retry-after', 'user-agent'];\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} headers Headers needing to be parsed\n * @returns {Object} Headers parsed into an object\n */\nmodule.exports = function parseHeaders(headers) {\n  var parsed = {};\n  var key;\n  var val;\n  var i;\n  if (!headers) {\n    return parsed;\n  }\n  utils.forEach(headers.split('\\n'), function parser(line) {\n    i = line.indexOf(':');\n    key = utils.trim(line.substr(0, i)).toLowerCase();\n    val = utils.trim(line.substr(i + 1));\n    if (key) {\n      if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {\n        return;\n      }\n      if (key === 'set-cookie') {\n        parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);\n      } else {\n        parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n      }\n    }\n  });\n  return parsed;\n};", "map": {"version": 3, "names": ["utils", "require", "ignoreDuplicateOf", "module", "exports", "parseHeaders", "headers", "parsed", "key", "val", "i", "for<PERSON>ach", "split", "parser", "line", "indexOf", "trim", "substr", "toLowerCase", "concat"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/helpers/parseHeaders.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\n\n// Headers whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nvar ignoreDuplicateOf = [\n  'age', 'authorization', 'content-length', 'content-type', 'etag',\n  'expires', 'from', 'host', 'if-modified-since', 'if-unmodified-since',\n  'last-modified', 'location', 'max-forwards', 'proxy-authorization',\n  'referer', 'retry-after', 'user-agent'\n];\n\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} headers Headers needing to be parsed\n * @returns {Object} Headers parsed into an object\n */\nmodule.exports = function parseHeaders(headers) {\n  var parsed = {};\n  var key;\n  var val;\n  var i;\n\n  if (!headers) { return parsed; }\n\n  utils.forEach(headers.split('\\n'), function parser(line) {\n    i = line.indexOf(':');\n    key = utils.trim(line.substr(0, i)).toLowerCase();\n    val = utils.trim(line.substr(i + 1));\n\n    if (key) {\n      if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {\n        return;\n      }\n      if (key === 'set-cookie') {\n        parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);\n      } else {\n        parsed[key] = parsed[key] ? parsed[key] + ', ' + val : val;\n      }\n    }\n  });\n\n  return parsed;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEjC;AACA;AACA,IAAIC,iBAAiB,GAAG,CACtB,KAAK,EAAE,eAAe,EAAE,gBAAgB,EAAE,cAAc,EAAE,MAAM,EAChE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,mBAAmB,EAAE,qBAAqB,EACrE,eAAe,EAAE,UAAU,EAAE,cAAc,EAAE,qBAAqB,EAClE,SAAS,EAAE,aAAa,EAAE,YAAY,CACvC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC9C,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,GAAG;EACP,IAAIC,GAAG;EACP,IAAIC,CAAC;EAEL,IAAI,CAACJ,OAAO,EAAE;IAAE,OAAOC,MAAM;EAAE;EAE/BP,KAAK,CAACW,OAAO,CAACL,OAAO,CAACM,KAAK,CAAC,IAAI,CAAC,EAAE,SAASC,MAAMA,CAACC,IAAI,EAAE;IACvDJ,CAAC,GAAGI,IAAI,CAACC,OAAO,CAAC,GAAG,CAAC;IACrBP,GAAG,GAAGR,KAAK,CAACgB,IAAI,CAACF,IAAI,CAACG,MAAM,CAAC,CAAC,EAAEP,CAAC,CAAC,CAAC,CAACQ,WAAW,CAAC,CAAC;IACjDT,GAAG,GAAGT,KAAK,CAACgB,IAAI,CAACF,IAAI,CAACG,MAAM,CAACP,CAAC,GAAG,CAAC,CAAC,CAAC;IAEpC,IAAIF,GAAG,EAAE;MACP,IAAID,MAAM,CAACC,GAAG,CAAC,IAAIN,iBAAiB,CAACa,OAAO,CAACP,GAAG,CAAC,IAAI,CAAC,EAAE;QACtD;MACF;MACA,IAAIA,GAAG,KAAK,YAAY,EAAE;QACxBD,MAAM,CAACC,GAAG,CAAC,GAAG,CAACD,MAAM,CAACC,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAG,EAAE,EAAEW,MAAM,CAAC,CAACV,GAAG,CAAC,CAAC;MAC9D,CAAC,MAAM;QACLF,MAAM,CAACC,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC,GAAG,IAAI,GAAGC,GAAG,GAAGA,GAAG;MAC5D;IACF;EACF,CAAC,CAAC;EAEF,OAAOF,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}