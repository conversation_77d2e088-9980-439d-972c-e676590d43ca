{"ast": null, "code": "/**\n * @license React\n * react-reconciler-constants.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function () {\n    'use strict';\n\n    var NoLane = /*                          */\n    0;\n    var SyncLane = /*                        */\n    2;\n    var InputContinuousLane = /*             */\n    8;\n    var DefaultLane = /*                     */\n    32;\n    var IdleLane = /*                        */\n    268435456;\n    var NoEventPriority = NoLane;\n    var DiscreteEventPriority = SyncLane;\n    var ContinuousEventPriority = InputContinuousLane;\n    var DefaultEventPriority = DefaultLane;\n    var IdleEventPriority = IdleLane;\n    var LegacyRoot = 0;\n    var ConcurrentRoot = 1;\n    exports.ConcurrentRoot = ConcurrentRoot;\n    exports.ContinuousEventPriority = ContinuousEventPriority;\n    exports.DefaultEventPriority = DefaultEventPriority;\n    exports.DiscreteEventPriority = DiscreteEventPriority;\n    exports.IdleEventPriority = IdleEventPriority;\n    exports.LegacyRoot = LegacyRoot;\n    exports.NoEventPriority = NoEventPriority;\n  })();\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "NoLane", "SyncLane", "InputContinuousLane", "DefaultLane", "IdleLane", "NoEventPriority", "DiscreteEventPriority", "ContinuousEventPriority", "DefaultEventPriority", "IdleEventPriority", "LegacyRoot", "ConcurrentRoot", "exports"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/@react-three/fiber/node_modules/react-reconciler/cjs/react-reconciler-constants.development.js"], "sourcesContent": ["/**\n * @license React\n * react-reconciler-constants.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\nvar NoLane =\n/*                          */\n0;\nvar SyncLane =\n/*                        */\n2;\nvar InputContinuousLane =\n/*             */\n8;\nvar DefaultLane =\n/*                     */\n32;\nvar IdleLane =\n/*                        */\n268435456;\n\nvar NoEventPriority = NoLane;\nvar DiscreteEventPriority = SyncLane;\nvar ContinuousEventPriority = InputContinuousLane;\nvar DefaultEventPriority = DefaultLane;\nvar IdleEventPriority = IdleLane;\n\nvar LegacyRoot = 0;\nvar ConcurrentRoot = 1;\n\nexports.ConcurrentRoot = ConcurrentRoot;\nexports.ContinuousEventPriority = ContinuousEventPriority;\nexports.DefaultEventPriority = DefaultEventPriority;\nexports.DiscreteEventPriority = DiscreteEventPriority;\nexports.IdleEventPriority = IdleEventPriority;\nexports.LegacyRoot = LegacyRoot;\nexports.NoEventPriority = NoEventPriority;\n  })();\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC,CAAC,YAAW;IACd,YAAY;;IAEZ,IAAIC,MAAM,GACV;IACA,CAAC;IACD,IAAIC,QAAQ,GACZ;IACA,CAAC;IACD,IAAIC,mBAAmB,GACvB;IACA,CAAC;IACD,IAAIC,WAAW,GACf;IACA,EAAE;IACF,IAAIC,QAAQ,GACZ;IACA,SAAS;IAET,IAAIC,eAAe,GAAGL,MAAM;IAC5B,IAAIM,qBAAqB,GAAGL,QAAQ;IACpC,IAAIM,uBAAuB,GAAGL,mBAAmB;IACjD,IAAIM,oBAAoB,GAAGL,WAAW;IACtC,IAAIM,iBAAiB,GAAGL,QAAQ;IAEhC,IAAIM,UAAU,GAAG,CAAC;IAClB,IAAIC,cAAc,GAAG,CAAC;IAEtBC,OAAO,CAACD,cAAc,GAAGA,cAAc;IACvCC,OAAO,CAACL,uBAAuB,GAAGA,uBAAuB;IACzDK,OAAO,CAACJ,oBAAoB,GAAGA,oBAAoB;IACnDI,OAAO,CAACN,qBAAqB,GAAGA,qBAAqB;IACrDM,OAAO,CAACH,iBAAiB,GAAGA,iBAAiB;IAC7CG,OAAO,CAACF,UAAU,GAAGA,UAAU;IAC/BE,OAAO,CAACP,eAAe,GAAGA,eAAe;EACvC,CAAC,EAAE,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}