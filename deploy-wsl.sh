#!/bin/bash

# WSL Deployment Script for Premium Landing Page Updates
# Deploy to kubera.help (************)

set -e  # Exit on any error

echo "🚀 Deploying Premium Landing Page Updates via WSL"
echo "=================================================="

# Configuration
SERVER_HOST="kubera.help"  # Use domain name instead of IP
SERVER_USER="ubuntu"
DOMAIN="kubera.help"
APP_DIR="/var/www/kubera.help"
LOCAL_BUILD_DIR="./build"
LOCAL_SERVER_DIR="./server"

# SSH Key paths (try multiple locations)
SSH_KEY_PATHS=(
    "/tmp/kubera_deploy.pem"
    "/mnt/c/Users/<USER>/Desktop/Horoscope/kubera_wsl.pem"
    "/mnt/c/Users/<USER>/Desktop/Horoscope/kubera.pem"
    "./kubera_wsl.pem"
    "./kubera.pem"
)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo_step() {
    echo -e "${YELLOW}🔄 $1${NC}"
}

# Find and setup SSH key
setup_ssh_key() {
    echo_step "Setting up SSH key..."
    
    SSH_KEY=""
    for key_path in "${SSH_KEY_PATHS[@]}"; do
        if [ -f "$key_path" ]; then
            echo_info "Found SSH key at: $key_path"
            SSH_KEY="$key_path"
            break
        fi
    done
    
    if [ -z "$SSH_KEY" ]; then
        echo_error "No SSH key found. Trying to copy from Windows..."
        
        # Try to copy from Windows to WSL
        if [ -f "/mnt/c/Users/<USER>/Desktop/Horoscope/kubera_wsl.pem" ]; then
            cp "/mnt/c/Users/<USER>/Desktop/Horoscope/kubera_wsl.pem" "/tmp/kubera_deploy.pem"
            SSH_KEY="/tmp/kubera_deploy.pem"
        elif [ -f "/mnt/c/Users/<USER>/Desktop/Horoscope/kubera.pem" ]; then
            cp "/mnt/c/Users/<USER>/Desktop/Horoscope/kubera.pem" "/tmp/kubera_deploy.pem"
            SSH_KEY="/tmp/kubera_deploy.pem"
        else
            echo_error "No SSH key found in expected locations"
            exit 1
        fi
    fi
    
    # Set proper permissions
    chmod 600 "$SSH_KEY"
    echo_success "SSH key configured: $SSH_KEY"
}

# Test server connection
test_connection() {
    echo_step "Testing server connection..."

    # Try different SSH options
    SSH_OPTIONS="-o ConnectTimeout=30 -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"

    echo_info "Trying to connect to $SERVER_USER@$SERVER_HOST..."

    if ssh -i "$SSH_KEY" $SSH_OPTIONS "$SERVER_USER@$SERVER_HOST" "echo 'Connection successful'" 2>/dev/null; then
        echo_success "Server connection established"
        return 0
    else
        echo_error "Cannot connect to server $SERVER_HOST"
        echo_info "Trying verbose connection to diagnose..."
        ssh -i "$SSH_KEY" $SSH_OPTIONS -v "$SERVER_USER@$SERVER_HOST" "echo 'Connection test'" 2>&1 | head -10 || true
        echo_info "Please check:"
        echo_info "  1. Server is running (website is accessible)"
        echo_info "  2. SSH key is correct"
        echo_info "  3. SSH port 22 is open"
        echo_info "  4. Network connectivity"
        return 1
    fi
}

# Verify build exists
verify_build() {
    echo_step "Verifying build files..."
    
    if [ ! -d "$LOCAL_BUILD_DIR" ]; then
        echo_error "Build directory not found. Building application..."
        npm run build
        
        if [ ! -d "$LOCAL_BUILD_DIR" ]; then
            echo_error "Build failed. Cannot proceed."
            exit 1
        fi
    fi
    
    echo_success "Build files verified"
}

# Create backup on server
create_backup() {
    echo_step "Creating backup on server..."
    
    BACKUP_NAME="build_backup_$(date +%Y%m%d_%H%M%S)"
    
    ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "
        cd $APP_DIR
        if [ -d build ]; then
            sudo cp -r build $BACKUP_NAME
            echo 'Backup created: $BACKUP_NAME'
        else
            echo 'No existing build to backup'
        fi
    "
    
    echo_success "Backup completed"
}

# Upload build files
upload_files() {
    echo_step "Uploading build files to production..."
    
    # Upload build files
    echo_info "Uploading React build files..."
    rsync -avz --delete -e "ssh -i $SSH_KEY -o StrictHostKeyChecking=no" "$LOCAL_BUILD_DIR/" "$SERVER_USER@$SERVER_HOST:$APP_DIR/build/"

    # Upload server files (if they exist and are newer)
    if [ -d "$LOCAL_SERVER_DIR" ]; then
        echo_info "Uploading server files..."
        rsync -avz --exclude node_modules -e "ssh -i $SSH_KEY -o StrictHostKeyChecking=no" "$LOCAL_SERVER_DIR/" "$SERVER_USER@$SERVER_HOST:$APP_DIR/server/"
    fi
    
    echo_success "Files uploaded successfully"
}

# Set proper permissions
set_permissions() {
    echo_step "Setting proper file permissions..."
    
    ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "
        sudo chown -R www-data:www-data $APP_DIR/build
        sudo chmod -R 755 $APP_DIR/build

        if [ -d $APP_DIR/server ]; then
            sudo chown -R ubuntu:ubuntu $APP_DIR/server
            sudo chmod -R 755 $APP_DIR/server
        fi
    "
    
    echo_success "Permissions set correctly"
}

# Install server dependencies
install_dependencies() {
    echo_step "Installing server dependencies..."
    
    ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "
        if [ -d $APP_DIR/server ]; then
            cd $APP_DIR/server
            npm install --production
        fi
    "
    
    echo_success "Dependencies installed"
}

# Restart services
restart_services() {
    echo_step "Restarting services..."
    
    ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_HOST" "
        # Restart PM2 backend
        pm2 restart kubera-backend 2>/dev/null || pm2 start $APP_DIR/ecosystem.config.js

        # Reload Nginx
        sudo systemctl reload nginx

        # Wait for services to start
        sleep 3

        # Check status
        echo '=== PM2 Status ==='
        pm2 status

        echo '=== Nginx Status ==='
        sudo systemctl status nginx --no-pager -l
    "
    
    echo_success "Services restarted"
}

# Verify deployment
verify_deployment() {
    echo_step "Verifying deployment..."
    
    # Test website response
    if curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN" | grep -q "200"; then
        echo_success "Website is responding correctly"
    else
        echo_error "Website is not responding correctly"
        return 1
    fi
    
    # Test API endpoint
    if curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/api/horoscope/aries" | grep -q "200"; then
        echo_success "API is responding correctly"
    else
        echo_error "API is not responding correctly"
        return 1
    fi
    
    echo_success "Deployment verification completed"
}

# Show deployment summary
show_summary() {
    echo ""
    echo_success "🎉 Premium Landing Page Deployment Completed!"
    echo ""
    echo -e "${BLUE}🌐 Website: https://$DOMAIN${NC}"
    echo ""
    echo -e "${YELLOW}📊 What's New:${NC}"
    echo "  ✓ Dark glass card design for all zodiac signs"
    echo "  ✓ Consistent golden glow effects"
    echo "  ✓ Enhanced zodiac information (dates, elements, planets, gemstones)"
    echo "  ✓ Improved mobile responsiveness"
    echo "  ✓ Better hover animations and transitions"
    echo ""
    echo -e "${YELLOW}🧪 Testing Steps:${NC}"
    echo "  1. Visit https://$DOMAIN"
    echo "  2. Hard refresh (Ctrl+F5)"
    echo "  3. Check dark glass cards"
    echo "  4. Test hover effects"
    echo "  5. Verify mobile responsiveness"
    echo "  6. Test all zodiac sign links"
    echo ""
    echo -e "${YELLOW}📊 Monitoring Commands:${NC}"
    echo "  Check logs: ssh -i $SSH_KEY $SERVER_USER@$SERVER_HOST 'pm2 logs kubera-backend'"
    echo "  Check status: ssh -i $SSH_KEY $SERVER_USER@$SERVER_HOST 'pm2 status'"
    echo "  Monitor: ssh -i $SSH_KEY $SERVER_USER@$SERVER_HOST 'pm2 monit'"
    echo ""
}

# Main deployment function
main() {
    echo_info "Starting deployment process..."
    
    # Setup and verification
    setup_ssh_key
    test_connection || exit 1
    verify_build
    
    # Deployment steps
    create_backup
    upload_files
    set_permissions
    install_dependencies
    restart_services
    
    # Verification
    sleep 5  # Wait for services to fully start
    verify_deployment
    
    # Summary
    show_summary
}

# Error handling
trap 'echo_error "Deployment failed! Check the error messages above."; exit 1' ERR

# Run main deployment
main
