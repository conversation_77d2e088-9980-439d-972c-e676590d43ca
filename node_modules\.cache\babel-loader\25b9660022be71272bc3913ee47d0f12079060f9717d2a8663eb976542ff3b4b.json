{"ast": null, "code": "import axios from'axios';class HoroscopeService{constructor(){this.baseURL=process.env.REACT_APP_API_URL||'http://localhost:5000/api';this.cache=new Map();this.cacheExpiry=24*60*60*1000;// 24 hours in milliseconds\n}// Get cached horoscope if it exists and is not expired\ngetCachedHoroscope(signId){const today=new Date().toDateString();const cacheKey=\"\".concat(signId,\"_\").concat(today);if(this.cache.has(cacheKey)){const cached=this.cache.get(cacheKey);if(Date.now()-cached.timestamp<this.cacheExpiry){return cached.data;}else{this.cache.delete(cacheKey);}}return null;}// Cache horoscope data\ncacheHoroscope(signId,horoscope){const today=new Date().toDateString();const cacheKey=\"\".concat(signId,\"_\").concat(today);this.cache.set(cacheKey,{data:horoscope,timestamp:Date.now()});}// Set cached horoscope data (alias for cacheHoroscope)\nsetCachedHoroscope(signId,data){let expiryHours=arguments.length>2&&arguments[2]!==undefined?arguments[2]:24;this.cacheHoroscope(signId,data,expiryHours);}// Helper method to get sign in Sinhala\ngetSignSinhala(signId){const signMap={'aries':'මේෂ','taurus':'වෘෂභ','gemini':'මිථුන','cancer':'කටක','leo':'සිංහ','virgo':'කන්‍යා','libra':'තුලා','scorpio':'වෘශ්චික','sagittarius':'ධනු','capricorn':'මකර','aquarius':'කුම්භ','pisces':'මීන'};return signMap[signId]||signId;}// Get horoscope from backend API\nasync getHoroscopeFromAPI(signId){try{const response=await axios.get(\"\".concat(this.baseURL,\"/horoscope/\").concat(signId),{timeout:10000// 10 second timeout\n});if(response.data&&response.data.success){return response.data.data;}else{throw new Error('Invalid response from API');}}catch(error){var _error$response,_error$response2,_error$response3;console.error('API error details:',{message:error.message,status:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status,statusText:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.statusText,data:(_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.data});throw error;}}// Get specific category from backend API\nasync getCategoryFromAPI(signId,category){try{const response=await axios.get(\"\".concat(this.baseURL,\"/horoscope/\").concat(signId,\"/\").concat(category),{timeout:10000// 10 second timeout\n});if(response.data&&response.data.success){return response.data.data;}else{throw new Error('Invalid response from API');}}catch(error){var _error$response4,_error$response5,_error$response6;console.error('API error details:',{message:error.message,status:(_error$response4=error.response)===null||_error$response4===void 0?void 0:_error$response4.status,statusText:(_error$response5=error.response)===null||_error$response5===void 0?void 0:_error$response5.statusText,data:(_error$response6=error.response)===null||_error$response6===void 0?void 0:_error$response6.data});throw error;}}// Fallback horoscope generation (enhanced with categories)\ngenerateFallbackHoroscope(signSinhala){const fallbackHoroscopes={'මේෂ':{love:'අද දිනය ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ප්‍රේම සම්බන්ධතාවල ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට හොඳ අවස්ථාවක්. නව ව්‍යාපෘති ආරම්භ කිරීම සහ ගැටළු විසඳීම සඳහා ශක්තිමත් දිනයකි.',health:'ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. ව්‍යායාම සහ ක්‍රීඩා ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',finance:'මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට හොඳ කාලයකි. වියදම් සැලසුම් කිරීමේදී ප්‍රවේශම්කාරී වන්න.',general:'සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය රතු සහ අංකය 9.'},'වෘෂභ':{love:'ස්ථාවරත්වය සහ ඉවසීම ආදරය ක්ෂේත්‍රයේ ප්‍රධාන ගුණාංග වනු ඇත. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කිරීමට හොඳ දිනයකි.',career:'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රවේශයක් අවශ්‍ය වේ. දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීම සඳහා ඉවසීම අවශ්‍ය වේ.',health:'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ සමතුලිත ආහාර වේලක් පිළිපැදීම වැදගත් වේ. ආතතිය අවම කර ගන්න.',finance:'මූල්‍ය ස්ථාවරත්වය සහ ඉතිරිකිරීම් කෙරෙහි අවධානය යොමු කරන්න. අනවශ්‍ය වියදම් වළකින්න.',general:'ස්ථාවර ප්‍රගතිය සහ ඉවසීම අදට ප්‍රධාන තේමාවන් වේ. වාසනාවන්ත වර්ණය කොළ සහ අංකය 6.'},'මිථුන':{love:'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ. නව මිත්‍රත්වයන් ගොඩනැගීමට සුදුසු කාලයකි.',career:'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා සහ නව අදහස් ඉදිරිපත් කිරීමට හොඳ අවස්ථාවක්. ගමන් සහ රැස්වීම් සාර්ථක වේ.',health:'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. නමුත් ආතතිය කළමනාකරණය කිරීම වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල විවිධාංගීකරණය සහ නව ආදායම් මාර්ග සොයා ගැනීමට හොඳ කාලයකි.',general:'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගැනීමට සුදුසු දිනයකි. වාසනාවන්ත වර්ණය කහ සහ අංකය 5.'},'කටක':{love:'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වීම වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව සහ කණ්ඩායම් වැඩ කටයුතු සාර්ථක වේ. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',health:'හැඟීම්බර සෞඛ්‍යය සහ ආහාර පාන කෙරෙහි විශේෂ අවධානය යොමු කරන්න. ජල පානය වැඩි කරන්න.',finance:'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා සහ ආරක්ෂාව ප්‍රමුඛතාවය දෙන්න. ගෘහ ආයෝජන සඳහා හොඳ කාලයකි.',general:'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න. වාසනාවන්ත වර්ණය සුදු සහ අංකය 2.'},'සිංහ':{love:'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න. රොමැන්ටික් අවස්ථා සහ ප්‍රීතිමත් සිදුවීම් අපේක්ෂා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සහ නිර්මාණශීලී ව්‍යාපෘති සඳහා සුදුසු කාලයකි. පිළිගැනීම ලැබේ.',health:'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී. හෘද සෞඛ්‍යය සහ ව්‍යායාම කෙරෙහි අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල විශ්වාසය සහ ධනාත්මක ආකල්පයක් ප්‍රදර්ශනය කරන්න. ආයෝජන සාර්ථක වේ.',general:'කලාත්මක ක්‍රියාකාරකම් සහ විනෝදාස්වාදය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය රන්වන් සහ අංකය 1.'},'කන්‍යා':{love:'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ. කුඩා ප්‍රීතිමත් සිදුවීම් ප්‍රශංසා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය සහ විස්තර කෙරෙහි අවධානය ප්‍රශංසනීය ප්‍රතිඵල ගෙන දේ. සේවා කටයුතු සාර්ථක වේ.',health:'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ සහ ආහාර පාන සැලසුම් වැදගත් වේ. ශරීර පිරිසිදුකම පවත්වන්න.',finance:'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් සහ ගිණුම් කැටයුතු නිවැරදිව කරන්න. ඉතිරිකිරීම් වැඩි කරන්න.',general:'කුඩා ප්‍රගතියන් සහ දෛනික කටයුතු සාර්ථකව සම්පූර්ණ කරන්න. වාසනාවන්ත වර්ණය නිල් සහ අංකය 6.'},'තුලා':{love:'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය සහ සාධාරණත්වය වැදගත් වේ. සම්බන්ධතා ශක්තිමත් කිරීමට හොඳ කාලයකි.',career:'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සහ සමූහ වැඩ කටයුතු සාර්ථක වේ. සෞන්දර්යය සහ කලාව සම්බන්ධ ක්ෂේත්‍ර වාසිදායක වේ.',health:'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න. වකුගඩු සහ සමේ සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම සහ හවුල්කාරිත්ව ආයෝජන සඳහා හොඳ කාලයකි.',general:'සෞන්දර්යය සහ සමගිය අගය කරන්න. වාසනාවන්ත වර්ණය රෝස සහ අංකය 7.'},'වෘශ්චික':{love:'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය සහ පරිවර්තනය අපේක්ෂා කරන්න. අභ්‍යන්තර ශක්තිය සොයා ගන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ සහ විශ්ලේෂණ කටයුතු සාර්ථක වේ. රහස්‍ය සහ ගැඹුරු අධ්‍යයනය සඳහා සුදුසු කාලයකි.',health:'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර සහ පුනර්ජීවනය කෙරෙහි අවධානය යොමු කරන්න. ජීර්ණ ක්‍රියාවලිය වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය සහ දිගුකාලීන ආයෝජන සඳහා හොඳ කාලයකි.',general:'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය තද රතු සහ අංකය 8.'},'ධනු':{love:'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ. දුර සම්බන්ධතා සහ සංස්කෘතික හුවමාරුව සාර්ථක වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම, ගමන් සහ ගුරු කටයුතු සඳහා සුදුසු කාලයකි. දර්ශනය සහ ආධ්‍යාත්මිකත්වය වාසිදායක වේ.',health:'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ. කලවා සහ ඉණ සෞඛ්‍යය වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සහ ගමන් සම්බන්ධ වියදම් සඳහා සැලසුම් කරන්න.',general:'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ජම්බු සහ අංකය 3.'},'මකර':{love:'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ. දිගුකාලීන සම්බන්ධතා ශක්තිමත් කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීමට හොඳ කාලයකි. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',health:'සෞඛ්‍ය කටයුතුවල අස්ථි සහ සම් සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න. නිතිපතා ව්‍යායාම වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන සහ දිගුකාලීන ඉතිරිකිරීම් සඳහා හොඳ කාලයකි.',general:'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න. වාසනාවන්ත වර්ණය කළු සහ අංකය 10.'},'කුම්භ':{love:'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය සඳහා සුදුසු කාලයකි. අනාගතය සඳහා සැලසුම් කරන්න.',health:'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම සහ විකල්ප වෛද්‍ය ක්‍රම වාසිදායක වේ. රුධිර සංසරණය වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සහ තාක්ෂණික ක්ෂේත්‍ර වාසිදායක වේ.',general:'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ආකාශ නිල් සහ අංකය 11.'},'මීන':{love:'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ. ආධ්‍යාත්මික සම්බන්ධතා ශක්තිමත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය සහ සේවා කටයුතු සඳහා සුදුසු කාලයකි. අන්තර්ජ්ඤානය අනුගමනය කරන්න.',health:'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය සහ මානසික සාමය වැදගත් වේ. පාද සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය සහ කරුණාව අනුගමනය කරන්න. පුණ්‍ය කටයුතු වාසිදායක වේ.',general:'ආධ්‍යාත්මික ක්‍රියාකාරකම් සහ කලාත්මක ප්‍රකාශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය මුහුදු කොළ සහ අංකය 12.'}};const fallback=fallbackHoroscopes[signSinhala];if(fallback){return{categories:{love:{id:'love',title:'ආදරය සහ සම්බන්ධතා',emoji:'💕',content:fallback.love},career:{id:'career',title:'වෘත්තීය ජීවිතය',emoji:'💼',content:fallback.career},health:{id:'health',title:'සෞඛ්‍ය සහ යහපැවැත්ම',emoji:'🌿',content:fallback.health},finance:{id:'finance',title:'මූල්‍ය කටයුතු',emoji:'💰',content:fallback.finance},general:{id:'general',title:'සාමාන්‍ය උපදෙස්',emoji:'✨',content:fallback.general}},rawContent:Object.values(fallback).join(' '),dateCreated:new Date().toISOString().split('T')[0],lastUpdated:new Date().toISOString()};}// Ultimate fallback\nreturn{categories:{love:{id:'love',title:'ආදරය සහ සම්බන්ධතා',emoji:'💕',content:'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.'},career:{id:'career',title:'වෘත්තීය ජීවිතය',emoji:'💼',content:'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.'},health:{id:'health',title:'සෞඛ්‍ය සහ යහපැවැත්ම',emoji:'🌿',content:'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.'},finance:{id:'finance',title:'මූල්‍ය කටයුතු',emoji:'💰',content:'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.'},general:{id:'general',title:'සාමාන්‍ය උපදෙස්',emoji:'✨',content:'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'}},rawContent:'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.',dateCreated:new Date().toISOString().split('T')[0],lastUpdated:new Date().toISOString()};}// Clear cache for a specific sign\nclearCache(signId){const today=new Date().toDateString();const cacheKey=\"\".concat(signId,\"_\").concat(today);this.cache.delete(cacheKey);}// Clear all cache\nclearAllCache(){this.cache.clear();}// Check API health\nasync checkAPIHealth(){try{const response=await axios.get(\"\".concat(this.baseURL,\"/health\"),{timeout:5000});return response.data.success;}catch(error){console.error('API health check failed:',error.message);return false;}}// Main method to get horoscope (with caching and fallback)\nasync getHoroscope(signId){let forceRefresh=arguments.length>1&&arguments[1]!==undefined?arguments[1]:false;try{// Check cache first (unless force refresh)\nif(!forceRefresh){const cached=this.getCachedHoroscope(signId);if(cached){console.log('Returning cached horoscope for',signId);return cached;}}console.log('Fetching fresh horoscope for',signId);// Try to get from API\ntry{const apiData=await this.getHoroscopeFromAPI(signId);// Cache the result\nthis.setCachedHoroscope(signId,apiData);return apiData;}catch(apiError){console.warn('API failed, using fallback horoscope:',apiError.message);// Generate fallback horoscope\nconst signSinhala=this.getSignSinhala(signId);const fallbackData=this.generateFallbackHoroscope(signSinhala);// Cache the fallback (with shorter expiry)\nthis.setCachedHoroscope(signId,fallbackData,1);// 1 hour expiry for fallback\nreturn fallbackData;}}catch(error){console.error('Complete horoscope fetch failure:',error);// Last resort: return a basic fallback\nconst signSinhala=this.getSignSinhala(signId);return this.generateFallbackHoroscope(signSinhala);}}// Generate fallback for specific category\ngenerateFallbackCategory(signId,category){const signSinhala=this.getSignSinhala(signId);const fallbackHoroscopes={'මේෂ':{love:'ආදරය ක්ෂේත්‍රයේ ධනාත්මක ශක්තිය සහ නව ආරම්භයන් අපේක්ෂා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කරන්න.',health:'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',finance:'මූල්‍ය කටයුතුවල ධනාත්මක ප්‍රවණතා දක්නට ලැබේ.',general:'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත.'},'වෘෂභ':{love:'ආදරය ක්ෂේත්‍රයේ ස්ථාවරත්වය සහ විශ්වාසය වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රගතිය අපේක්ෂා කරන්න.',health:'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ ස්ථාවරත්වය වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල ස්ථාවර ආදායම් සහ ඉතිරිකිරීම් වැදගත් වේ.',general:'ස්ථාවරත්වය සහ ක්‍රමානුකූලත්වය අගය කරන්න.'},'මිථුන':{love:'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා වාසිදායක වේ.',health:'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී.',finance:'මූල්‍ය කටයුතුවල විවිධාංගීකරණය වැදගත් වේ.',general:'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගන්න.'},'කටක':{love:'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව වැදගත් වේ.',health:'හැඟීම්බර සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා ප්‍රමුඛතාවය දෙන්න.',general:'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න.'},'සිංහ':{love:'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සාර්ථක වේ.',health:'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',finance:'මූල්‍ය කටයුතුවල විශ්වාසය ප්‍රදර්ශනය කරන්න.',general:'කලාත්මක ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'},'කන්‍යා':{love:'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය වාසිදායක වේ.',health:'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් කරන්න.',general:'කුඩා ප්‍රගතියන් සාර්ථකව සම්පූර්ණ කරන්න.'},'තුලා':{love:'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සාර්ථක වේ.',health:'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න.',finance:'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම වැදගත් වේ.',general:'සෞන්දර්යය සහ සමගිය අගය කරන්න.'},'වෘශ්චික':{love:'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය අපේක්ෂා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ කටයුතු සාර්ථක වේ.',health:'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර කෙරෙහි අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය කරන්න.',general:'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න.'},'ධනු':{love:'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගමන් වාසිදායක වේ.',health:'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ.',finance:'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සලකා බලන්න.',general:'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි.'},'මකර':{love:'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක වැදගත් වේ.',health:'සෞඛ්‍ය කටයුතුවල අස්ථි සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන වැදගත් වේ.',general:'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න.'},'කුම්භ':{love:'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය වාසිදායක වේ.',health:'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම වාසිදායක වේ.',finance:'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සලකා බලන්න.',general:'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි.'},'මීන':{love:'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ.',career:'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය වාසිදායක වේ.',health:'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය වැදගත් වේ.',finance:'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය අනුගමනය කරන්න.',general:'ආධ්‍යාත්මික ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'}};const categoryTitles={love:'ආදරය සහ සම්බන්ධතා',career:'වෘත්තීය ජීවිතය',health:'සෞඛ්‍ය සහ යහපැවැත්ම',finance:'මූල්‍ය කටයුතු',general:'සාමාන්‍ය උපදෙස්'};const categoryEmojis={love:'💕',career:'💼',health:'🌿',finance:'💰',general:'✨'};const fallback=fallbackHoroscopes[signSinhala];if(fallback&&fallback[category]){return{success:true,data:{id:category,title:categoryTitles[category]||category,emoji:categoryEmojis[category]||'⭐',content:fallback[category]},dateCreated:new Date().toISOString().split('T')[0],lastUpdated:new Date().toISOString()};}// Ultimate fallback\nreturn{success:true,data:{id:category,title:categoryTitles[category]||category,emoji:categoryEmojis[category]||'⭐',content:\"\".concat(categoryTitles[category]||category,\" \\u0D9A\\u0DCA\\u0DC2\\u0DDA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA\\u0DDA \\u0DB0\\u0DB1\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0D9A \\u0DC0\\u0DD9\\u0DB1\\u0DC3\\u0DCA\\u0D9A\\u0DB8\\u0DCA \\u0D85\\u0DB4\\u0DDA\\u0D9A\\u0DCA\\u0DC2\\u0DCF \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1.\")},dateCreated:new Date().toISOString().split('T')[0],lastUpdated:new Date().toISOString()};}// Get specific category for a sign\nasync getCategory(signId,category){let forceRefresh=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;try{// Check cache first (unless force refresh)\nconst cacheKey=\"\".concat(signId,\"_\").concat(category);if(!forceRefresh){const cached=this.getCachedHoroscope(cacheKey);if(cached){console.log('Returning cached category for',signId,category);return cached;}}console.log('Fetching fresh category for',signId,category);// Try to get from API\ntry{const apiData=await this.getCategoryFromAPI(signId,category);// Cache the result\nthis.setCachedHoroscope(cacheKey,apiData);return apiData;}catch(apiError){console.warn('Category API failed, using fallback:',apiError.message);// Generate fallback for this category\nconst fallbackData=this.generateFallbackCategory(signId,category);// Cache the fallback (with shorter expiry)\nthis.setCachedHoroscope(cacheKey,fallbackData,1);// 1 hour expiry for fallback\nreturn fallbackData;}}catch(error){console.error('Complete category fetch failure:',error);// Last resort: return a basic fallback\nreturn this.generateFallbackCategory(signId,category);}}// Get structured horoscope data (for backward compatibility)\nasync getStructuredHoroscope(signEnglish,signSinhala){let forceRefresh=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;const horoscopeData=await this.getHoroscope(signEnglish,signSinhala,forceRefresh);return horoscopeData.categories?Object.values(horoscopeData.categories):[];}// Manual refresh all horoscopes (admin function)\nasync refreshAllHoroscopes(){try{const response=await axios.post(\"\".concat(this.baseURL,\"/refresh-horoscopes\"),{},{timeout:60000// 1 minute timeout for bulk operation\n});return response.data;}catch(error){console.error('Manual refresh failed:',error.message);throw error;}}}const horoscopeService=new HoroscopeService();export default horoscopeService;", "map": {"version": 3, "names": ["axios", "HoroscopeService", "constructor", "baseURL", "process", "env", "REACT_APP_API_URL", "cache", "Map", "cacheExpiry", "getCachedHoroscope", "signId", "today", "Date", "toDateString", "cache<PERSON>ey", "concat", "has", "cached", "get", "now", "timestamp", "data", "delete", "cacheHoroscope", "horoscope", "set", "setCachedHoroscope", "expiryHours", "arguments", "length", "undefined", "getSignSinhala", "signMap", "getHoroscopeFromAPI", "response", "timeout", "success", "Error", "error", "_error$response", "_error$response2", "_error$response3", "console", "message", "status", "statusText", "getCategoryFromAPI", "category", "_error$response4", "_error$response5", "_error$response6", "generateFallbackHoroscope", "signSinhala", "fallbackHoroscopes", "love", "career", "health", "finance", "general", "fallback", "categories", "id", "title", "emoji", "content", "rawContent", "Object", "values", "join", "dateCreated", "toISOString", "split", "lastUpdated", "clearCache", "clearAllCache", "clear", "checkAPIHealth", "getHoroscope", "forceRefresh", "log", "apiData", "apiError", "warn", "fallbackD<PERSON>", "generateFallbackCategory", "categoryTitles", "categoryEmojis", "getCategory", "getStructuredHoroscope", "signEnglish", "horoscopeData", "refreshAllHoroscopes", "post", "horoscopeService"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass HoroscopeService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    \n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Set cached horoscope data (alias for cacheHoroscope)\n  setCachedHoroscope(signId, data, expiryHours = 24) {\n    this.cacheHoroscope(signId, data, expiryHours);\n  }\n\n  // Helper method to get sign in Sinhala\n  getSignSinhala(signId) {\n    const signMap = {\n      'aries': 'මේෂ',\n      'taurus': 'වෘෂභ',\n      'gemini': 'මිථුන',\n      'cancer': 'කටක',\n      'leo': 'සිංහ',\n      'virgo': 'කන්‍යා',\n      'libra': 'තුලා',\n      'scorpio': 'වෘශ්චික',\n      'sagittarius': 'ධනු',\n      'capricorn': 'මකර',\n      'aquarius': 'කුම්භ',\n      'pisces': 'මීන'\n    };\n    return signMap[signId] || signId;\n  }\n\n  // Get horoscope from backend API\n  async getHoroscopeFromAPI(signId) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}`, {\n        timeout: 10000 // 10 second timeout\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Get specific category from backend API\n  async getCategoryFromAPI(signId, category) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}/${category}`, {\n        timeout: 10000 // 10 second timeout\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Fallback horoscope generation (enhanced with categories)\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': {\n        love: 'අද දිනය ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ප්‍රේම සම්බන්ධතාවල ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට හොඳ අවස්ථාවක්. නව ව්‍යාපෘති ආරම්භ කිරීම සහ ගැටළු විසඳීම සඳහා ශක්තිමත් දිනයකි.',\n        health: 'ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. ව්‍යායාම සහ ක්‍රීඩා ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n        finance: 'මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට හොඳ කාලයකි. වියදම් සැලසුම් කිරීමේදී ප්‍රවේශම්කාරී වන්න.',\n        general: 'සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය රතු සහ අංකය 9.'\n      },\n      'වෘෂභ': {\n        love: 'ස්ථාවරත්වය සහ ඉවසීම ආදරය ක්ෂේත්‍රයේ ප්‍රධාන ගුණාංග වනු ඇත. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කිරීමට හොඳ දිනයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රවේශයක් අවශ්‍ය වේ. දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීම සඳහා ඉවසීම අවශ්‍ය වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ සමතුලිත ආහාර වේලක් පිළිපැදීම වැදගත් වේ. ආතතිය අවම කර ගන්න.',\n        finance: 'මූල්‍ය ස්ථාවරත්වය සහ ඉතිරිකිරීම් කෙරෙහි අවධානය යොමු කරන්න. අනවශ්‍ය වියදම් වළකින්න.',\n        general: 'ස්ථාවර ප්‍රගතිය සහ ඉවසීම අදට ප්‍රධාන තේමාවන් වේ. වාසනාවන්ත වර්ණය කොළ සහ අංකය 6.'\n      },\n      'මිථුන': {\n        love: 'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ. නව මිත්‍රත්වයන් ගොඩනැගීමට සුදුසු කාලයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා සහ නව අදහස් ඉදිරිපත් කිරීමට හොඳ අවස්ථාවක්. ගමන් සහ රැස්වීම් සාර්ථක වේ.',\n        health: 'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී. නමුත් ආතතිය කළමනාකරණය කිරීම වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විවිධාංගීකරණය සහ නව ආදායම් මාර්ග සොයා ගැනීමට හොඳ කාලයකි.',\n        general: 'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගැනීමට සුදුසු දිනයකි. වාසනාවන්ත වර්ණය කහ සහ අංකය 5.'\n      },\n      'කටක': {\n        love: 'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වීම වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව සහ කණ්ඩායම් වැඩ කටයුතු සාර්ථක වේ. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n        health: 'හැඟීම්බර සෞඛ්‍යය සහ ආහාර පාන කෙරෙහි විශේෂ අවධානය යොමු කරන්න. ජල පානය වැඩි කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා සහ ආරක්ෂාව ප්‍රමුඛතාවය දෙන්න. ගෘහ ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න. වාසනාවන්ත වර්ණය සුදු සහ අංකය 2.'\n      },\n      'සිංහ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න. රොමැන්ටික් අවස්ථා සහ ප්‍රීතිමත් සිදුවීම් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සහ නිර්මාණශීලී ව්‍යාපෘති සඳහා සුදුසු කාලයකි. පිළිගැනීම ලැබේ.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී. හෘද සෞඛ්‍යය සහ ව්‍යායාම කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල විශ්වාසය සහ ධනාත්මක ආකල්පයක් ප්‍රදර්ශනය කරන්න. ආයෝජන සාර්ථක වේ.',\n        general: 'කලාත්මක ක්‍රියාකාරකම් සහ විනෝදාස්වාදය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය රන්වන් සහ අංකය 1.'\n      },\n      'කන්‍යා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ. කුඩා ප්‍රීතිමත් සිදුවීම් ප්‍රශංසා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය සහ විස්තර කෙරෙහි අවධානය ප්‍රශංසනීය ප්‍රතිඵල ගෙන දේ. සේවා කටයුතු සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ සහ ආහාර පාන සැලසුම් වැදගත් වේ. ශරීර පිරිසිදුකම පවත්වන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් සහ ගිණුම් කැටයුතු නිවැරදිව කරන්න. ඉතිරිකිරීම් වැඩි කරන්න.',\n        general: 'කුඩා ප්‍රගතියන් සහ දෛනික කටයුතු සාර්ථකව සම්පූර්ණ කරන්න. වාසනාවන්ත වර්ණය නිල් සහ අංකය 6.'\n      },\n      'තුලා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය සහ සාධාරණත්වය වැදගත් වේ. සම්බන්ධතා ශක්තිමත් කිරීමට හොඳ කාලයකි.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සහ සමූහ වැඩ කටයුතු සාර්ථක වේ. සෞන්දර්යය සහ කලාව සම්බන්ධ ක්ෂේත්‍ර වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න. වකුගඩු සහ සමේ සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම සහ හවුල්කාරිත්ව ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'සෞන්දර්යය සහ සමගිය අගය කරන්න. වාසනාවන්ත වර්ණය රෝස සහ අංකය 7.'\n      },\n      'වෘශ්චික': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය සහ පරිවර්තනය අපේක්ෂා කරන්න. අභ්‍යන්තර ශක්තිය සොයා ගන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ සහ විශ්ලේෂණ කටයුතු සාර්ථක වේ. රහස්‍ය සහ ගැඹුරු අධ්‍යයනය සඳහා සුදුසු කාලයකි.',\n        health: 'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර සහ පුනර්ජීවනය කෙරෙහි අවධානය යොමු කරන්න. ජීර්ණ ක්‍රියාවලිය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය සහ දිගුකාලීන ආයෝජන සඳහා හොඳ කාලයකි.',\n        general: 'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න. වාසනාවන්ත වර්ණය තද රතු සහ අංකය 8.'\n      },\n      'ධනු': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ. දුර සම්බන්ධතා සහ සංස්කෘතික හුවමාරුව සාර්ථක වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම, ගමන් සහ ගුරු කටයුතු සඳහා සුදුසු කාලයකි. දර්ශනය සහ ආධ්‍යාත්මිකත්වය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ. කලවා සහ ඉණ සෞඛ්‍යය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සහ ගමන් සම්බන්ධ වියදම් සඳහා සැලසුම් කරන්න.',\n        general: 'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ජම්බු සහ අංකය 3.'\n      },\n      'මකර': {\n        love: 'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ. දිගුකාලීන සම්බන්ධතා ශක්තිමත් කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක සාක්ෂාත් කර ගැනීමට හොඳ කාලයකි. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල අස්ථි සහ සම් සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න. නිතිපතා ව්‍යායාම වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන සහ දිගුකාලීන ඉතිරිකිරීම් සඳහා හොඳ කාලයකි.',\n        general: 'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න. වාසනාවන්ත වර්ණය කළු සහ අංකය 10.'\n      },\n      'කුම්භ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය සඳහා සුදුසු කාලයකි. අනාගතය සඳහා සැලසුම් කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම සහ විකල්ප වෛද්‍ය ක්‍රම වාසිදායක වේ. රුධිර සංසරණය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සහ තාක්ෂණික ක්ෂේත්‍ර වාසිදායක වේ.',\n        general: 'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය ආකාශ නිල් සහ අංකය 11.'\n      },\n      'මීන': {\n        love: 'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ. ආධ්‍යාත්මික සම්බන්ධතා ශක්තිමත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය සහ සේවා කටයුතු සඳහා සුදුසු කාලයකි. අන්තර්ජ්ඤානය අනුගමනය කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය සහ මානසික සාමය වැදගත් වේ. පාද සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය සහ කරුණාව අනුගමනය කරන්න. පුණ්‍ය කටයුතු වාසිදායක වේ.',\n        general: 'ආධ්‍යාත්මික ක්‍රියාකාරකම් සහ කලාත්මක ප්‍රකාශනය සඳහා සුදුසු දිනයකි. වාසනාවන්ත වර්ණය මුහුදු කොළ සහ අංකය 12.'\n      }\n    };\n\n    const fallback = fallbackHoroscopes[signSinhala];\n    if (fallback) {\n      return {\n        categories: {\n          love: {\n            id: 'love',\n            title: 'ආදරය සහ සම්බන්ධතා',\n            emoji: '💕',\n            content: fallback.love\n          },\n          career: {\n            id: 'career',\n            title: 'වෘත්තීය ජීවිතය',\n            emoji: '💼',\n            content: fallback.career\n          },\n          health: {\n            id: 'health',\n            title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n            emoji: '🌿',\n            content: fallback.health\n          },\n          finance: {\n            id: 'finance',\n            title: 'මූල්‍ය කටයුතු',\n            emoji: '💰',\n            content: fallback.finance\n          },\n          general: {\n            id: 'general',\n            title: 'සාමාන්‍ය උපදෙස්',\n            emoji: '✨',\n            content: fallback.general\n          }\n        },\n        rawContent: Object.values(fallback).join(' '),\n        dateCreated: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString()\n      };\n    }\n\n    // Ultimate fallback\n    return {\n      categories: {\n        love: {\n          id: 'love',\n          title: 'ආදරය සහ සම්බන්ධතා',\n          emoji: '💕',\n          content: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.'\n        },\n        career: {\n          id: 'career',\n          title: 'වෘත්තීය ජීවිතය',\n          emoji: '💼',\n          content: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.'\n        },\n        health: {\n          id: 'health',\n          title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n          emoji: '🌿',\n          content: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.'\n        },\n        finance: {\n          id: 'finance',\n          title: 'මූල්‍ය කටයුතු',\n          emoji: '💰',\n          content: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.'\n        },\n        general: {\n          id: 'general',\n          title: 'සාමාන්‍ය උපදෙස්',\n          emoji: '✨',\n          content: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n        }\n      },\n      rawContent: 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.',\n      dateCreated: new Date().toISOString().split('T')[0],\n      lastUpdated: new Date().toISOString()\n    };\n  }\n\n  // Clear cache for a specific sign\n  clearCache(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.delete(cacheKey);\n  }\n\n  // Clear all cache\n  clearAllCache() {\n    this.cache.clear();\n  }\n\n  // Check API health\n  async checkAPIHealth() {\n    try {\n      const response = await axios.get(`${this.baseURL}/health`, {\n        timeout: 5000\n      });\n      return response.data.success;\n    } catch (error) {\n      console.error('API health check failed:', error.message);\n      return false;\n    }\n  }\n\n  // Main method to get horoscope (with caching and fallback)\n  async getHoroscope(signId, forceRefresh = false) {\n    try {\n      // Check cache first (unless force refresh)\n      if (!forceRefresh) {\n        const cached = this.getCachedHoroscope(signId);\n        if (cached) {\n          console.log('Returning cached horoscope for', signId);\n          return cached;\n        }\n      }\n\n      console.log('Fetching fresh horoscope for', signId);\n      \n      // Try to get from API\n      try {\n        const apiData = await this.getHoroscopeFromAPI(signId);\n        \n        // Cache the result\n        this.setCachedHoroscope(signId, apiData);\n        \n        return apiData;\n      } catch (apiError) {\n        console.warn('API failed, using fallback horoscope:', apiError.message);\n        \n        // Generate fallback horoscope\n        const signSinhala = this.getSignSinhala(signId);\n        const fallbackData = this.generateFallbackHoroscope(signSinhala);\n        \n        // Cache the fallback (with shorter expiry)\n        this.setCachedHoroscope(signId, fallbackData, 1); // 1 hour expiry for fallback\n        \n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Complete horoscope fetch failure:', error);\n      \n      // Last resort: return a basic fallback\n      const signSinhala = this.getSignSinhala(signId);\n      return this.generateFallbackHoroscope(signSinhala);\n    }\n  }\n\n  // Generate fallback for specific category\n  generateFallbackCategory(signId, category) {\n    const signSinhala = this.getSignSinhala(signId);\n    const fallbackHoroscopes = {\n      'මේෂ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ධනාත්මක ශක්තිය සහ නව ආරම්භයන් අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කරන්න.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල ධනාත්මක ප්‍රවණතා දක්නට ලැබේ.',\n        general: 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත.'\n      },\n      'වෘෂභ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ස්ථාවරත්වය සහ විශ්වාසය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ක්‍රමානුකූල ප්‍රගතිය අපේක්ෂා කරන්න.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා සහ ස්ථාවරත්වය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආදායම් සහ ඉතිරිකිරීම් වැදගත් වේ.',\n        general: 'ස්ථාවරත්වය සහ ක්‍රමානුකූලත්වය අගය කරන්න.'\n      },\n      'මිථුන': {\n        love: 'සන්නිවේදනය සහ අවබෝධය ආදරය ක්ෂේත්‍රයේ වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සන්නිවේදන කුසලතා වාසිදායක වේ.',\n        health: 'මානසික ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල විවිධාංගීකරණය වැදගත් වේ.',\n        general: 'ඉගෙනීම සහ නව අත්දැකීම් ලබා ගන්න.'\n      },\n      'කටක': {\n        love: 'පවුල සහ නිවස ආදරය ක්ෂේත්‍රයේ කේන්ද්‍රස්ථානය වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සහයෝගිතාව වැදගත් වේ.',\n        health: 'හැඟීම්බර සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල පවුලේ අවශ්‍යතා ප්‍රමුඛතාවය දෙන්න.',\n        general: 'අන්තර්ජ්ඤානය සහ හැඟීම් අනුගමනය කරන්න.'\n      },\n      'සිංහ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නායකත්වය සහ විශ්වාසය ප්‍රදර්ශනය කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව භූමිකාවන් සාර්ථක වේ.',\n        health: 'ශාරීරික ශක්තිය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල විශ්වාසය ප්‍රදර්ශනය කරන්න.',\n        general: 'කලාත්මක ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'\n      },\n      'කන්‍යා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ විස්තර සහ සැලකිලිමත්කම වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ සංවිධානය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නිතිපතා පරීක්ෂණ වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල සවිස්තර සැලසුම් කරන්න.',\n        general: 'කුඩා ප්‍රගතියන් සාර්ථකව සම්පූර්ණ කරන්න.'\n      },\n      'තුලා': {\n        love: 'ආදරය ක්ෂේත්‍රයේ සමතුලිතතාවය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ හවුල්කාරිත්වයන් සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල සමතුලිතතාවය පවත්වන්න.',\n        finance: 'මූල්‍ය කටයුතුවල සාධාරණ බෙදාගැනීම වැදගත් වේ.',\n        general: 'සෞන්දර්යය සහ සමගිය අගය කරන්න.'\n      },\n      'වෘශ්චික': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ගැඹුරු අවබෝධය අපේක්ෂා කරන්න.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ගවේෂණ කටයුතු සාර්ථක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ප්‍රතිකාර කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල ගැඹුරු විශ්ලේෂණය කරන්න.',\n        general: 'අභ්‍යන්තර ශක්තිය සහ පරිවර්තනය අපේක්ෂා කරන්න.'\n      },\n      'ධනු': {\n        love: 'ආදරය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගවේෂණය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ ඉගෙනීම සහ ගමන් වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ක්‍රීඩා සහ එළිමහන් ක්‍රියාකාරකම් වාසිදායක වේ.',\n        finance: 'මූල්‍ය කටයුතුවල විදේශීය ආයෝජන සලකා බලන්න.',\n        general: 'ඉගෙනීම සහ අනාගත දර්ශනය සඳහා සුදුසු දිනයකි.'\n      },\n      'මකර': {\n        love: 'ආදරය ක්ෂේත්‍රයේ වගකීම සහ ස්ථාවරත්වය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ අභිලාෂය සහ දිගුකාලීන ඉලක්ක වැදගත් වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල අස්ථි සෞඛ්‍යය කෙරෙහි අවධානය යොමු කරන්න.',\n        finance: 'මූල්‍ය කටයුතුවල ස්ථාවර ආයෝජන වැදගත් වේ.',\n        general: 'වගකීම සහ සාම්ප්‍රදායික වටිනාකම් අගය කරන්න.'\n      },\n      'කුම්භ': {\n        love: 'ආදරය ක්ෂේත්‍රයේ නවෝත්පාදනය සහ මිත්‍රත්වය වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ තාක්ෂණය සහ නවෝත්පාදනය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල නවීන ප්‍රතිකාර ක්‍රම වාසිදායක වේ.',\n        finance: 'මූල්‍ය කටයුතුවල නවීන ආයෝජන ක්‍රම සලකා බලන්න.',\n        general: 'නවෝත්පාදනය සහ සමාජ සේවය සඳහා සුදුසු දිනයකි.'\n      },\n      'මීන': {\n        love: 'ආදරය ක්ෂේත්‍රයේ අන්තර්ජ්ඤානය සහ කරුණාව වැදගත් වේ.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ කලාත්මක ප්‍රකාශනය වාසිදායක වේ.',\n        health: 'සෞඛ්‍ය කටයුතුවල ආධ්‍යාත්මික සුවය වැදගත් වේ.',\n        finance: 'මූල්‍ය කටයුතුවල අන්තර්ජ්ඤානය අනුගමනය කරන්න.',\n        general: 'ආධ්‍යාත්මික ක්‍රියාකාරකම් සඳහා සුදුසු දිනයකි.'\n      }\n    };\n\n    const categoryTitles = {\n      love: 'ආදරය සහ සම්බන්ධතා',\n      career: 'වෘත්තීය ජීවිතය',\n      health: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n      finance: 'මූල්‍ය කටයුතු',\n      general: 'සාමාන්‍ය උපදෙස්'\n    };\n\n    const categoryEmojis = {\n      love: '💕',\n      career: '💼',\n      health: '🌿',\n      finance: '💰',\n      general: '✨'\n    };\n\n    const fallback = fallbackHoroscopes[signSinhala];\n    if (fallback && fallback[category]) {\n      return {\n        success: true,\n        data: {\n          id: category,\n          title: categoryTitles[category] || category,\n          emoji: categoryEmojis[category] || '⭐',\n          content: fallback[category]\n        },\n        dateCreated: new Date().toISOString().split('T')[0],\n        lastUpdated: new Date().toISOString()\n      };\n    }\n\n    // Ultimate fallback\n    return {\n      success: true,\n      data: {\n        id: category,\n        title: categoryTitles[category] || category,\n        emoji: categoryEmojis[category] || '⭐',\n        content: `${categoryTitles[category] || category} ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.`\n      },\n      dateCreated: new Date().toISOString().split('T')[0],\n      lastUpdated: new Date().toISOString()\n    };\n  }\n\n  // Get specific category for a sign\n  async getCategory(signId, category, forceRefresh = false) {\n    try {\n      // Check cache first (unless force refresh)\n      const cacheKey = `${signId}_${category}`;\n      if (!forceRefresh) {\n        const cached = this.getCachedHoroscope(cacheKey);\n        if (cached) {\n          console.log('Returning cached category for', signId, category);\n          return cached;\n        }\n      }\n\n      console.log('Fetching fresh category for', signId, category);\n      \n      // Try to get from API\n      try {\n        const apiData = await this.getCategoryFromAPI(signId, category);\n        \n        // Cache the result\n        this.setCachedHoroscope(cacheKey, apiData);\n        \n        return apiData;\n      } catch (apiError) {\n        console.warn('Category API failed, using fallback:', apiError.message);\n        \n        // Generate fallback for this category\n        const fallbackData = this.generateFallbackCategory(signId, category);\n        \n        // Cache the fallback (with shorter expiry)\n        this.setCachedHoroscope(cacheKey, fallbackData, 1); // 1 hour expiry for fallback\n        \n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Complete category fetch failure:', error);\n      \n      // Last resort: return a basic fallback\n      return this.generateFallbackCategory(signId, category);\n    }\n  }\n\n  // Get structured horoscope data (for backward compatibility)\n  async getStructuredHoroscope(signEnglish, signSinhala, forceRefresh = false) {\n    const horoscopeData = await this.getHoroscope(signEnglish, signSinhala, forceRefresh);\n    return horoscopeData.categories ? Object.values(horoscopeData.categories) : [];\n  }\n\n  // Manual refresh all horoscopes (admin function)\n  async refreshAllHoroscopes() {\n    try {\n      const response = await axios.post(`${this.baseURL}/refresh-horoscopes`, {}, {\n        timeout: 60000 // 1 minute timeout for bulk operation\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Manual refresh failed:', error.message);\n      throw error;\n    }\n  }\n}\n\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,gBAAiB,CACrBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAC3E,IAAI,CAACC,KAAK,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACtB,IAAI,CAACC,WAAW,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAE;AAC1C,CAEA;AACAC,kBAAkBA,CAACC,MAAM,CAAE,CACzB,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAC,QAAQ,IAAAC,MAAA,CAAML,MAAM,MAAAK,MAAA,CAAIJ,KAAK,CAAE,CAErC,GAAI,IAAI,CAACL,KAAK,CAACU,GAAG,CAACF,QAAQ,CAAC,CAAE,CAC5B,KAAM,CAAAG,MAAM,CAAG,IAAI,CAACX,KAAK,CAACY,GAAG,CAACJ,QAAQ,CAAC,CACvC,GAAIF,IAAI,CAACO,GAAG,CAAC,CAAC,CAAGF,MAAM,CAACG,SAAS,CAAG,IAAI,CAACZ,WAAW,CAAE,CACpD,MAAO,CAAAS,MAAM,CAACI,IAAI,CACpB,CAAC,IAAM,CACL,IAAI,CAACf,KAAK,CAACgB,MAAM,CAACR,QAAQ,CAAC,CAC7B,CACF,CAEA,MAAO,KAAI,CACb,CAEA;AACAS,cAAcA,CAACb,MAAM,CAAEc,SAAS,CAAE,CAChC,KAAM,CAAAb,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAC,QAAQ,IAAAC,MAAA,CAAML,MAAM,MAAAK,MAAA,CAAIJ,KAAK,CAAE,CAErC,IAAI,CAACL,KAAK,CAACmB,GAAG,CAACX,QAAQ,CAAE,CACvBO,IAAI,CAAEG,SAAS,CACfJ,SAAS,CAAER,IAAI,CAACO,GAAG,CAAC,CACtB,CAAC,CAAC,CACJ,CAEA;AACAO,kBAAkBA,CAAChB,MAAM,CAAEW,IAAI,CAAoB,IAAlB,CAAAM,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC/C,IAAI,CAACL,cAAc,CAACb,MAAM,CAAEW,IAAI,CAAEM,WAAW,CAAC,CAChD,CAEA;AACAI,cAAcA,CAACrB,MAAM,CAAE,CACrB,KAAM,CAAAsB,OAAO,CAAG,CACd,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,KAAK,CACf,KAAK,CAAE,MAAM,CACb,OAAO,CAAE,QAAQ,CACjB,OAAO,CAAE,MAAM,CACf,SAAS,CAAE,SAAS,CACpB,aAAa,CAAE,KAAK,CACpB,WAAW,CAAE,KAAK,CAClB,UAAU,CAAE,OAAO,CACnB,QAAQ,CAAE,KACZ,CAAC,CACD,MAAO,CAAAA,OAAO,CAACtB,MAAM,CAAC,EAAIA,MAAM,CAClC,CAEA;AACA,KAAM,CAAAuB,mBAAmBA,CAACvB,MAAM,CAAE,CAChC,GAAI,CACF,KAAM,CAAAwB,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACmB,GAAG,IAAAH,MAAA,CAAI,IAAI,CAACb,OAAO,gBAAAa,MAAA,CAAcL,MAAM,EAAI,CACtEyB,OAAO,CAAE,KAAM;AACjB,CAAC,CAAC,CAEF,GAAID,QAAQ,CAACb,IAAI,EAAIa,QAAQ,CAACb,IAAI,CAACe,OAAO,CAAE,CAC1C,MAAO,CAAAF,QAAQ,CAACb,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACL,KAAM,IAAI,CAAAgB,KAAK,CAAC,2BAA2B,CAAC,CAC9C,CACF,CAAE,MAAOC,KAAK,CAAE,KAAAC,eAAA,CAAAC,gBAAA,CAAAC,gBAAA,CACdC,OAAO,CAACJ,KAAK,CAAC,oBAAoB,CAAE,CAClCK,OAAO,CAAEL,KAAK,CAACK,OAAO,CACtBC,MAAM,EAAAL,eAAA,CAAED,KAAK,CAACJ,QAAQ,UAAAK,eAAA,iBAAdA,eAAA,CAAgBK,MAAM,CAC9BC,UAAU,EAAAL,gBAAA,CAAEF,KAAK,CAACJ,QAAQ,UAAAM,gBAAA,iBAAdA,gBAAA,CAAgBK,UAAU,CACtCxB,IAAI,EAAAoB,gBAAA,CAAEH,KAAK,CAACJ,QAAQ,UAAAO,gBAAA,iBAAdA,gBAAA,CAAgBpB,IACxB,CAAC,CAAC,CACF,KAAM,CAAAiB,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAQ,kBAAkBA,CAACpC,MAAM,CAAEqC,QAAQ,CAAE,CACzC,GAAI,CACF,KAAM,CAAAb,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACmB,GAAG,IAAAH,MAAA,CAAI,IAAI,CAACb,OAAO,gBAAAa,MAAA,CAAcL,MAAM,MAAAK,MAAA,CAAIgC,QAAQ,EAAI,CAClFZ,OAAO,CAAE,KAAM;AACjB,CAAC,CAAC,CAEF,GAAID,QAAQ,CAACb,IAAI,EAAIa,QAAQ,CAACb,IAAI,CAACe,OAAO,CAAE,CAC1C,MAAO,CAAAF,QAAQ,CAACb,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACL,KAAM,IAAI,CAAAgB,KAAK,CAAC,2BAA2B,CAAC,CAC9C,CACF,CAAE,MAAOC,KAAK,CAAE,KAAAU,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CACdR,OAAO,CAACJ,KAAK,CAAC,oBAAoB,CAAE,CAClCK,OAAO,CAAEL,KAAK,CAACK,OAAO,CACtBC,MAAM,EAAAI,gBAAA,CAAEV,KAAK,CAACJ,QAAQ,UAAAc,gBAAA,iBAAdA,gBAAA,CAAgBJ,MAAM,CAC9BC,UAAU,EAAAI,gBAAA,CAAEX,KAAK,CAACJ,QAAQ,UAAAe,gBAAA,iBAAdA,gBAAA,CAAgBJ,UAAU,CACtCxB,IAAI,EAAA6B,gBAAA,CAAEZ,KAAK,CAACJ,QAAQ,UAAAgB,gBAAA,iBAAdA,gBAAA,CAAgB7B,IACxB,CAAC,CAAC,CACF,KAAM,CAAAiB,KAAK,CACb,CACF,CAEA;AACAa,yBAAyBA,CAACC,WAAW,CAAE,CACrC,KAAM,CAAAC,kBAAkB,CAAG,CACzB,KAAK,CAAE,CACLC,IAAI,CAAE,iJAAiJ,CACvJC,MAAM,CAAE,mIAAmI,CAC3IC,MAAM,CAAE,0GAA0G,CAClHC,OAAO,CAAE,qGAAqG,CAC9GC,OAAO,CAAE,6FACX,CAAC,CACD,MAAM,CAAE,CACNJ,IAAI,CAAE,qHAAqH,CAC3HC,MAAM,CAAE,8GAA8G,CACtHC,MAAM,CAAE,uFAAuF,CAC/FC,OAAO,CAAE,oFAAoF,CAC7FC,OAAO,CAAE,iFACX,CAAC,CACD,OAAO,CAAE,CACPJ,IAAI,CAAE,0FAA0F,CAChGC,MAAM,CAAE,4GAA4G,CACpHC,MAAM,CAAE,gFAAgF,CACxFC,OAAO,CAAE,0EAA0E,CACnFC,OAAO,CAAE,gFACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,yFAAyF,CAC/FC,MAAM,CAAE,8GAA8G,CACtHC,MAAM,CAAE,kFAAkF,CAC1FC,OAAO,CAAE,yFAAyF,CAClGC,OAAO,CAAE,uEACX,CAAC,CACD,MAAM,CAAE,CACNJ,IAAI,CAAE,gHAAgH,CACtHC,MAAM,CAAE,mGAAmG,CAC3GC,MAAM,CAAE,mFAAmF,CAC3FC,OAAO,CAAE,iFAAiF,CAC1FC,OAAO,CAAE,6FACX,CAAC,CACD,QAAQ,CAAE,CACRJ,IAAI,CAAE,4FAA4F,CAClGC,MAAM,CAAE,wGAAwG,CAChHC,MAAM,CAAE,0FAA0F,CAClGC,OAAO,CAAE,2FAA2F,CACpGC,OAAO,CAAE,yFACX,CAAC,CACD,MAAM,CAAE,CACNJ,IAAI,CAAE,4FAA4F,CAClGC,MAAM,CAAE,kHAAkH,CAC1HC,MAAM,CAAE,uFAAuF,CAC/FC,OAAO,CAAE,yEAAyE,CAClFC,OAAO,CAAE,8DACX,CAAC,CACD,SAAS,CAAE,CACTJ,IAAI,CAAE,uFAAuF,CAC7FC,MAAM,CAAE,sGAAsG,CAC9GC,MAAM,CAAE,gGAAgG,CACxGC,OAAO,CAAE,sEAAsE,CAC/EC,OAAO,CAAE,gFACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,4FAA4F,CAClGC,MAAM,CAAE,2GAA2G,CACnHC,MAAM,CAAE,6FAA6F,CACrGC,OAAO,CAAE,0EAA0E,CACnFC,OAAO,CAAE,6EACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,oFAAoF,CAC1FC,MAAM,CAAE,6GAA6G,CACrHC,MAAM,CAAE,4FAA4F,CACpGC,OAAO,CAAE,wEAAwE,CACjFC,OAAO,CAAE,4EACX,CAAC,CACD,OAAO,CAAE,CACPJ,IAAI,CAAE,wFAAwF,CAC9FC,MAAM,CAAE,yFAAyF,CACjGC,MAAM,CAAE,kGAAkG,CAC1GC,OAAO,CAAE,oEAAoE,CAC7EC,OAAO,CAAE,mFACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,sFAAsF,CAC5FC,MAAM,CAAE,qGAAqG,CAC7GC,MAAM,CAAE,kGAAkG,CAC1GC,OAAO,CAAE,kFAAkF,CAC3FC,OAAO,CAAE,2GACX,CACF,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGN,kBAAkB,CAACD,WAAW,CAAC,CAChD,GAAIO,QAAQ,CAAE,CACZ,MAAO,CACLC,UAAU,CAAE,CACVN,IAAI,CAAE,CACJO,EAAE,CAAE,MAAM,CACVC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAEL,QAAQ,CAACL,IACpB,CAAC,CACDC,MAAM,CAAE,CACNM,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,gBAAgB,CACvBC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAEL,QAAQ,CAACJ,MACpB,CAAC,CACDC,MAAM,CAAE,CACNK,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAEL,QAAQ,CAACH,MACpB,CAAC,CACDC,OAAO,CAAE,CACPI,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAEL,QAAQ,CAACF,OACpB,CAAC,CACDC,OAAO,CAAE,CACPG,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,GAAG,CACVC,OAAO,CAAEL,QAAQ,CAACD,OACpB,CACF,CAAC,CACDO,UAAU,CAAEC,MAAM,CAACC,MAAM,CAACR,QAAQ,CAAC,CAACS,IAAI,CAAC,GAAG,CAAC,CAC7CC,WAAW,CAAE,GAAI,CAAAzD,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnDC,WAAW,CAAE,GAAI,CAAA5D,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CACtC,CAAC,CACH,CAEA;AACA,MAAO,CACLV,UAAU,CAAE,CACVN,IAAI,CAAE,CACJO,EAAE,CAAE,MAAM,CACVC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,8DACX,CAAC,CACDT,MAAM,CAAE,CACNM,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,gBAAgB,CACvBC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,yDACX,CAAC,CACDR,MAAM,CAAE,CACNK,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,mDACX,CAAC,CACDP,OAAO,CAAE,CACPI,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAE,IAAI,CACXC,OAAO,CAAE,0DACX,CAAC,CACDN,OAAO,CAAE,CACPG,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,GAAG,CACVC,OAAO,CAAE,2DACX,CACF,CAAC,CACDC,UAAU,CAAE,2DAA2D,CACvEI,WAAW,CAAE,GAAI,CAAAzD,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnDC,WAAW,CAAE,GAAI,CAAA5D,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CACtC,CAAC,CACH,CAEA;AACAG,UAAUA,CAAC/D,MAAM,CAAE,CACjB,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAC,QAAQ,IAAAC,MAAA,CAAML,MAAM,MAAAK,MAAA,CAAIJ,KAAK,CAAE,CACrC,IAAI,CAACL,KAAK,CAACgB,MAAM,CAACR,QAAQ,CAAC,CAC7B,CAEA;AACA4D,aAAaA,CAAA,CAAG,CACd,IAAI,CAACpE,KAAK,CAACqE,KAAK,CAAC,CAAC,CACpB,CAEA;AACA,KAAM,CAAAC,cAAcA,CAAA,CAAG,CACrB,GAAI,CACF,KAAM,CAAA1C,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAACmB,GAAG,IAAAH,MAAA,CAAI,IAAI,CAACb,OAAO,YAAW,CACzDiC,OAAO,CAAE,IACX,CAAC,CAAC,CACF,MAAO,CAAAD,QAAQ,CAACb,IAAI,CAACe,OAAO,CAC9B,CAAE,MAAOE,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAACK,OAAO,CAAC,CACxD,MAAO,MAAK,CACd,CACF,CAEA;AACA,KAAM,CAAAkC,YAAYA,CAACnE,MAAM,CAAwB,IAAtB,CAAAoE,YAAY,CAAAlD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAC7C,GAAI,CACF;AACA,GAAI,CAACkD,YAAY,CAAE,CACjB,KAAM,CAAA7D,MAAM,CAAG,IAAI,CAACR,kBAAkB,CAACC,MAAM,CAAC,CAC9C,GAAIO,MAAM,CAAE,CACVyB,OAAO,CAACqC,GAAG,CAAC,gCAAgC,CAAErE,MAAM,CAAC,CACrD,MAAO,CAAAO,MAAM,CACf,CACF,CAEAyB,OAAO,CAACqC,GAAG,CAAC,8BAA8B,CAAErE,MAAM,CAAC,CAEnD;AACA,GAAI,CACF,KAAM,CAAAsE,OAAO,CAAG,KAAM,KAAI,CAAC/C,mBAAmB,CAACvB,MAAM,CAAC,CAEtD;AACA,IAAI,CAACgB,kBAAkB,CAAChB,MAAM,CAAEsE,OAAO,CAAC,CAExC,MAAO,CAAAA,OAAO,CAChB,CAAE,MAAOC,QAAQ,CAAE,CACjBvC,OAAO,CAACwC,IAAI,CAAC,uCAAuC,CAAED,QAAQ,CAACtC,OAAO,CAAC,CAEvE;AACA,KAAM,CAAAS,WAAW,CAAG,IAAI,CAACrB,cAAc,CAACrB,MAAM,CAAC,CAC/C,KAAM,CAAAyE,YAAY,CAAG,IAAI,CAAChC,yBAAyB,CAACC,WAAW,CAAC,CAEhE;AACA,IAAI,CAAC1B,kBAAkB,CAAChB,MAAM,CAAEyE,YAAY,CAAE,CAAC,CAAC,CAAE;AAElD,MAAO,CAAAA,YAAY,CACrB,CACF,CAAE,MAAO7C,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CAEzD;AACA,KAAM,CAAAc,WAAW,CAAG,IAAI,CAACrB,cAAc,CAACrB,MAAM,CAAC,CAC/C,MAAO,KAAI,CAACyC,yBAAyB,CAACC,WAAW,CAAC,CACpD,CACF,CAEA;AACAgC,wBAAwBA,CAAC1E,MAAM,CAAEqC,QAAQ,CAAE,CACzC,KAAM,CAAAK,WAAW,CAAG,IAAI,CAACrB,cAAc,CAACrB,MAAM,CAAC,CAC/C,KAAM,CAAA2C,kBAAkB,CAAG,CACzB,KAAK,CAAE,CACLC,IAAI,CAAE,8DAA8D,CACpEC,MAAM,CAAE,qDAAqD,CAC7DC,MAAM,CAAE,iCAAiC,CACzCC,OAAO,CAAE,8CAA8C,CACvDC,OAAO,CAAE,gCACX,CAAC,CACD,MAAM,CAAE,CACNJ,IAAI,CAAE,mDAAmD,CACzDC,MAAM,CAAE,wDAAwD,CAChEC,MAAM,CAAE,kDAAkD,CAC1DC,OAAO,CAAE,yDAAyD,CAClEC,OAAO,CAAE,0CACX,CAAC,CACD,OAAO,CAAE,CACPJ,IAAI,CAAE,iDAAiD,CACvDC,MAAM,CAAE,kDAAkD,CAC1DC,MAAM,CAAE,yCAAyC,CACjDC,OAAO,CAAE,0CAA0C,CACnDC,OAAO,CAAE,kCACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,iDAAiD,CACvDC,MAAM,CAAE,yCAAyC,CACjDC,MAAM,CAAE,4CAA4C,CACpDC,OAAO,CAAE,mDAAmD,CAC5DC,OAAO,CAAE,uCACX,CAAC,CACD,MAAM,CAAE,CACNJ,IAAI,CAAE,wDAAwD,CAC9DC,MAAM,CAAE,iDAAiD,CACzDC,MAAM,CAAE,iCAAiC,CACzCC,OAAO,CAAE,4CAA4C,CACrDC,OAAO,CAAE,2CACX,CAAC,CACD,QAAQ,CAAE,CACRJ,IAAI,CAAE,mDAAmD,CACzDC,MAAM,CAAE,0CAA0C,CAClDC,MAAM,CAAE,4CAA4C,CACpDC,OAAO,CAAE,wCAAwC,CACjDC,OAAO,CAAE,yCACX,CAAC,CACD,MAAM,CAAE,CACNJ,IAAI,CAAE,wCAAwC,CAC9CC,MAAM,CAAE,+CAA+C,CACvDC,MAAM,CAAE,uCAAuC,CAC/CC,OAAO,CAAE,6CAA6C,CACtDC,OAAO,CAAE,+BACX,CAAC,CACD,SAAS,CAAE,CACTJ,IAAI,CAAE,8CAA8C,CACpDC,MAAM,CAAE,4CAA4C,CACpDC,MAAM,CAAE,qDAAqD,CAC7DC,OAAO,CAAE,yCAAyC,CAClDC,OAAO,CAAE,8CACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,6CAA6C,CACnDC,MAAM,CAAE,gDAAgD,CACxDC,MAAM,CAAE,+DAA+D,CACvEC,OAAO,CAAE,2CAA2C,CACpDC,OAAO,CAAE,4CACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,gDAAgD,CACtDC,MAAM,CAAE,0DAA0D,CAClEC,MAAM,CAAE,yDAAyD,CACjEC,OAAO,CAAE,yCAAyC,CAClDC,OAAO,CAAE,4CACX,CAAC,CACD,OAAO,CAAE,CACPJ,IAAI,CAAE,qDAAqD,CAC3DC,MAAM,CAAE,uDAAuD,CAC/DC,MAAM,CAAE,mDAAmD,CAC3DC,OAAO,CAAE,8CAA8C,CACvDC,OAAO,CAAE,6CACX,CAAC,CACD,KAAK,CAAE,CACLJ,IAAI,CAAE,mDAAmD,CACzDC,MAAM,CAAE,mDAAmD,CAC3DC,MAAM,CAAE,6CAA6C,CACrDC,OAAO,CAAE,6CAA6C,CACtDC,OAAO,CAAE,+CACX,CACF,CAAC,CAED,KAAM,CAAA2B,cAAc,CAAG,CACrB/B,IAAI,CAAE,mBAAmB,CACzBC,MAAM,CAAE,gBAAgB,CACxBC,MAAM,CAAE,qBAAqB,CAC7BC,OAAO,CAAE,eAAe,CACxBC,OAAO,CAAE,iBACX,CAAC,CAED,KAAM,CAAA4B,cAAc,CAAG,CACrBhC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,IAAI,CACZC,MAAM,CAAE,IAAI,CACZC,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,GACX,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGN,kBAAkB,CAACD,WAAW,CAAC,CAChD,GAAIO,QAAQ,EAAIA,QAAQ,CAACZ,QAAQ,CAAC,CAAE,CAClC,MAAO,CACLX,OAAO,CAAE,IAAI,CACbf,IAAI,CAAE,CACJwC,EAAE,CAAEd,QAAQ,CACZe,KAAK,CAAEuB,cAAc,CAACtC,QAAQ,CAAC,EAAIA,QAAQ,CAC3CgB,KAAK,CAAEuB,cAAc,CAACvC,QAAQ,CAAC,EAAI,GAAG,CACtCiB,OAAO,CAAEL,QAAQ,CAACZ,QAAQ,CAC5B,CAAC,CACDsB,WAAW,CAAE,GAAI,CAAAzD,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnDC,WAAW,CAAE,GAAI,CAAA5D,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CACtC,CAAC,CACH,CAEA;AACA,MAAO,CACLlC,OAAO,CAAE,IAAI,CACbf,IAAI,CAAE,CACJwC,EAAE,CAAEd,QAAQ,CACZe,KAAK,CAAEuB,cAAc,CAACtC,QAAQ,CAAC,EAAIA,QAAQ,CAC3CgB,KAAK,CAAEuB,cAAc,CAACvC,QAAQ,CAAC,EAAI,GAAG,CACtCiB,OAAO,IAAAjD,MAAA,CAAKsE,cAAc,CAACtC,QAAQ,CAAC,EAAIA,QAAQ,wOAClD,CAAC,CACDsB,WAAW,CAAE,GAAI,CAAAzD,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACnDC,WAAW,CAAE,GAAI,CAAA5D,IAAI,CAAC,CAAC,CAAC0D,WAAW,CAAC,CACtC,CAAC,CACH,CAEA;AACA,KAAM,CAAAiB,WAAWA,CAAC7E,MAAM,CAAEqC,QAAQ,CAAwB,IAAtB,CAAA+B,YAAY,CAAAlD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACtD,GAAI,CACF;AACA,KAAM,CAAAd,QAAQ,IAAAC,MAAA,CAAML,MAAM,MAAAK,MAAA,CAAIgC,QAAQ,CAAE,CACxC,GAAI,CAAC+B,YAAY,CAAE,CACjB,KAAM,CAAA7D,MAAM,CAAG,IAAI,CAACR,kBAAkB,CAACK,QAAQ,CAAC,CAChD,GAAIG,MAAM,CAAE,CACVyB,OAAO,CAACqC,GAAG,CAAC,+BAA+B,CAAErE,MAAM,CAAEqC,QAAQ,CAAC,CAC9D,MAAO,CAAA9B,MAAM,CACf,CACF,CAEAyB,OAAO,CAACqC,GAAG,CAAC,6BAA6B,CAAErE,MAAM,CAAEqC,QAAQ,CAAC,CAE5D;AACA,GAAI,CACF,KAAM,CAAAiC,OAAO,CAAG,KAAM,KAAI,CAAClC,kBAAkB,CAACpC,MAAM,CAAEqC,QAAQ,CAAC,CAE/D;AACA,IAAI,CAACrB,kBAAkB,CAACZ,QAAQ,CAAEkE,OAAO,CAAC,CAE1C,MAAO,CAAAA,OAAO,CAChB,CAAE,MAAOC,QAAQ,CAAE,CACjBvC,OAAO,CAACwC,IAAI,CAAC,sCAAsC,CAAED,QAAQ,CAACtC,OAAO,CAAC,CAEtE;AACA,KAAM,CAAAwC,YAAY,CAAG,IAAI,CAACC,wBAAwB,CAAC1E,MAAM,CAAEqC,QAAQ,CAAC,CAEpE;AACA,IAAI,CAACrB,kBAAkB,CAACZ,QAAQ,CAAEqE,YAAY,CAAE,CAAC,CAAC,CAAE;AAEpD,MAAO,CAAAA,YAAY,CACrB,CACF,CAAE,MAAO7C,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CAExD;AACA,MAAO,KAAI,CAAC8C,wBAAwB,CAAC1E,MAAM,CAAEqC,QAAQ,CAAC,CACxD,CACF,CAEA;AACA,KAAM,CAAAyC,sBAAsBA,CAACC,WAAW,CAAErC,WAAW,CAAwB,IAAtB,CAAA0B,YAAY,CAAAlD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACzE,KAAM,CAAA8D,aAAa,CAAG,KAAM,KAAI,CAACb,YAAY,CAACY,WAAW,CAAErC,WAAW,CAAE0B,YAAY,CAAC,CACrF,MAAO,CAAAY,aAAa,CAAC9B,UAAU,CAAGM,MAAM,CAACC,MAAM,CAACuB,aAAa,CAAC9B,UAAU,CAAC,CAAG,EAAE,CAChF,CAEA;AACA,KAAM,CAAA+B,oBAAoBA,CAAA,CAAG,CAC3B,GAAI,CACF,KAAM,CAAAzD,QAAQ,CAAG,KAAM,CAAAnC,KAAK,CAAC6F,IAAI,IAAA7E,MAAA,CAAI,IAAI,CAACb,OAAO,wBAAuB,CAAC,CAAC,CAAE,CAC1EiC,OAAO,CAAE,KAAM;AACjB,CAAC,CAAC,CACF,MAAO,CAAAD,QAAQ,CAACb,IAAI,CACtB,CAAE,MAAOiB,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAACK,OAAO,CAAC,CACtD,KAAM,CAAAL,KAAK,CACb,CACF,CACF,CAEA,KAAM,CAAAuD,gBAAgB,CAAG,GAAI,CAAA7F,gBAAgB,CAAC,CAAC,CAC/C,cAAe,CAAA6F,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}