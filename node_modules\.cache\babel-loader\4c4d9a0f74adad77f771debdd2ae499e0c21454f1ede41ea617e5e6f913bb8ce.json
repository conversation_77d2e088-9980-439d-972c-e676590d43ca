{"ast": null, "code": "'use strict';\n\nvar VERSION = require('../env/data').version;\nvar AxiosError = require('../core/AxiosError');\nvar validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach(function (type, i) {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\nvar deprecatedWarnings = {};\n\n/**\n * Transitional option validator\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return function (value, opt, opts) {\n    if (validator === false) {\n      throw new AxiosError(formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')), AxiosError.ERR_DEPRECATED);\n    }\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(formatMessage(opt, ' has been deprecated since v' + version + ' and will be removed in the near future'));\n    }\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\n/**\n * Assert object's properties type\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n  var keys = Object.keys(options);\n  var i = keys.length;\n  while (i-- > 0) {\n    var opt = keys[i];\n    var validator = schema[opt];\n    if (validator) {\n      var value = options[opt];\n      var result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\nmodule.exports = {\n  assertOptions: assertOptions,\n  validators: validators\n};", "map": {"version": 3, "names": ["VERSION", "require", "version", "AxiosError", "validators", "for<PERSON>ach", "type", "i", "validator", "thing", "deprecatedWarnings", "transitional", "message", "formatMessage", "opt", "desc", "value", "opts", "ERR_DEPRECATED", "console", "warn", "assertOptions", "options", "schema", "allowUnknown", "ERR_BAD_OPTION_VALUE", "keys", "Object", "length", "result", "undefined", "ERR_BAD_OPTION", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/helpers/validator.js"], "sourcesContent": ["'use strict';\n\nvar VERSION = require('../env/data').version;\nvar AxiosError = require('../core/AxiosError');\n\nvar validators = {};\n\n// eslint-disable-next-line func-names\n['object', 'boolean', 'number', 'function', 'string', 'symbol'].forEach(function(type, i) {\n  validators[type] = function validator(thing) {\n    return typeof thing === type || 'a' + (i < 1 ? 'n ' : ' ') + type;\n  };\n});\n\nvar deprecatedWarnings = {};\n\n/**\n * Transitional option validator\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n * @returns {function}\n */\nvalidators.transitional = function transitional(validator, version, message) {\n  function formatMessage(opt, desc) {\n    return '[Axios v' + VERSION + '] Transitional option \\'' + opt + '\\'' + desc + (message ? '. ' + message : '');\n  }\n\n  // eslint-disable-next-line func-names\n  return function(value, opt, opts) {\n    if (validator === false) {\n      throw new AxiosError(\n        formatMessage(opt, ' has been removed' + (version ? ' in ' + version : '')),\n        AxiosError.ERR_DEPRECATED\n      );\n    }\n\n    if (version && !deprecatedWarnings[opt]) {\n      deprecatedWarnings[opt] = true;\n      // eslint-disable-next-line no-console\n      console.warn(\n        formatMessage(\n          opt,\n          ' has been deprecated since v' + version + ' and will be removed in the near future'\n        )\n      );\n    }\n\n    return validator ? validator(value, opt, opts) : true;\n  };\n};\n\n/**\n * Assert object's properties type\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n */\n\nfunction assertOptions(options, schema, allowUnknown) {\n  if (typeof options !== 'object') {\n    throw new AxiosError('options must be an object', AxiosError.ERR_BAD_OPTION_VALUE);\n  }\n  var keys = Object.keys(options);\n  var i = keys.length;\n  while (i-- > 0) {\n    var opt = keys[i];\n    var validator = schema[opt];\n    if (validator) {\n      var value = options[opt];\n      var result = value === undefined || validator(value, opt, options);\n      if (result !== true) {\n        throw new AxiosError('option ' + opt + ' must be ' + result, AxiosError.ERR_BAD_OPTION_VALUE);\n      }\n      continue;\n    }\n    if (allowUnknown !== true) {\n      throw new AxiosError('Unknown option ' + opt, AxiosError.ERR_BAD_OPTION);\n    }\n  }\n}\n\nmodule.exports = {\n  assertOptions: assertOptions,\n  validators: validators\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC,CAACC,OAAO;AAC5C,IAAIC,UAAU,GAAGF,OAAO,CAAC,oBAAoB,CAAC;AAE9C,IAAIG,UAAU,GAAG,CAAC,CAAC;;AAEnB;AACA,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACC,OAAO,CAAC,UAASC,IAAI,EAAEC,CAAC,EAAE;EACxFH,UAAU,CAACE,IAAI,CAAC,GAAG,SAASE,SAASA,CAACC,KAAK,EAAE;IAC3C,OAAO,OAAOA,KAAK,KAAKH,IAAI,IAAI,GAAG,IAAIC,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,GAAGD,IAAI;EACnE,CAAC;AACH,CAAC,CAAC;AAEF,IAAII,kBAAkB,GAAG,CAAC,CAAC;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACAN,UAAU,CAACO,YAAY,GAAG,SAASA,YAAYA,CAACH,SAAS,EAAEN,OAAO,EAAEU,OAAO,EAAE;EAC3E,SAASC,aAAaA,CAACC,GAAG,EAAEC,IAAI,EAAE;IAChC,OAAO,UAAU,GAAGf,OAAO,GAAG,0BAA0B,GAAGc,GAAG,GAAG,IAAI,GAAGC,IAAI,IAAIH,OAAO,GAAG,IAAI,GAAGA,OAAO,GAAG,EAAE,CAAC;EAChH;;EAEA;EACA,OAAO,UAASI,KAAK,EAAEF,GAAG,EAAEG,IAAI,EAAE;IAChC,IAAIT,SAAS,KAAK,KAAK,EAAE;MACvB,MAAM,IAAIL,UAAU,CAClBU,aAAa,CAACC,GAAG,EAAE,mBAAmB,IAAIZ,OAAO,GAAG,MAAM,GAAGA,OAAO,GAAG,EAAE,CAAC,CAAC,EAC3EC,UAAU,CAACe,cACb,CAAC;IACH;IAEA,IAAIhB,OAAO,IAAI,CAACQ,kBAAkB,CAACI,GAAG,CAAC,EAAE;MACvCJ,kBAAkB,CAACI,GAAG,CAAC,GAAG,IAAI;MAC9B;MACAK,OAAO,CAACC,IAAI,CACVP,aAAa,CACXC,GAAG,EACH,8BAA8B,GAAGZ,OAAO,GAAG,yCAC7C,CACF,CAAC;IACH;IAEA,OAAOM,SAAS,GAAGA,SAAS,CAACQ,KAAK,EAAEF,GAAG,EAAEG,IAAI,CAAC,GAAG,IAAI;EACvD,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASI,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,YAAY,EAAE;EACpD,IAAI,OAAOF,OAAO,KAAK,QAAQ,EAAE;IAC/B,MAAM,IAAInB,UAAU,CAAC,2BAA2B,EAAEA,UAAU,CAACsB,oBAAoB,CAAC;EACpF;EACA,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACJ,OAAO,CAAC;EAC/B,IAAIf,CAAC,GAAGmB,IAAI,CAACE,MAAM;EACnB,OAAOrB,CAAC,EAAE,GAAG,CAAC,EAAE;IACd,IAAIO,GAAG,GAAGY,IAAI,CAACnB,CAAC,CAAC;IACjB,IAAIC,SAAS,GAAGe,MAAM,CAACT,GAAG,CAAC;IAC3B,IAAIN,SAAS,EAAE;MACb,IAAIQ,KAAK,GAAGM,OAAO,CAACR,GAAG,CAAC;MACxB,IAAIe,MAAM,GAAGb,KAAK,KAAKc,SAAS,IAAItB,SAAS,CAACQ,KAAK,EAAEF,GAAG,EAAEQ,OAAO,CAAC;MAClE,IAAIO,MAAM,KAAK,IAAI,EAAE;QACnB,MAAM,IAAI1B,UAAU,CAAC,SAAS,GAAGW,GAAG,GAAG,WAAW,GAAGe,MAAM,EAAE1B,UAAU,CAACsB,oBAAoB,CAAC;MAC/F;MACA;IACF;IACA,IAAID,YAAY,KAAK,IAAI,EAAE;MACzB,MAAM,IAAIrB,UAAU,CAAC,iBAAiB,GAAGW,GAAG,EAAEX,UAAU,CAAC4B,cAAc,CAAC;IAC1E;EACF;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG;EACfZ,aAAa,EAAEA,aAAa;EAC5BjB,UAAU,EAAEA;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}