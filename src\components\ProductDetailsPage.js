import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import KuberaAnimation from './KuberaAnimation';
import { getCardById } from '../data/kuberaCards';
import { useCart } from '../context/CartContext';

const ProductDetailsPage = () => {
  const { cardId } = useParams();
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const [card, setCard] = useState(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const cardData = getCardById(cardId);
    if (cardData) {
      setCard(cardData);
      setLoading(false);
    } else {
      // Redirect to cards page if card not found
      navigate('/kubera-cards');
    }
  }, [cardId, navigate]);

  const handleAddToCart = () => {
    const cartItem = {
      id: card.id,
      name: card.name,
      price: card.price,
      quantity: quantity,
      image: card.images[0]
    };

    addToCart(cartItem);

    // Show success message (you can implement a toast notification here)
    alert(`${card.name} කාර්ට් එකට එකතු කරන ලදී!`);
  };

  const handleBuyNow = () => {
    // Add to cart first
    handleAddToCart();
    // Navigate to checkout
    navigate('/checkout');
  };

  if (loading) {
    return (
      <div className="product-details-page">
        <ParticleBackground />
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>කාඩ්පත් තොරතුරු ලබා ගනිමින්...</p>
        </div>
      </div>
    );
  }

  if (!card) {
    return (
      <div className="product-details-page">
        <ParticleBackground />
        <div className="error-container">
          <h2>කාඩ්පත සොයා ගත නොහැක</h2>
          <Link to="/kubera-cards" className="back-to-cards-btn dark-glass-card">
            කාඩ්පත් පිටුවට ආපසු යන්න
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="product-details-page">
      <ParticleBackground />
      <KuberaAnimation />

      {/* Back Button */}
      <Link to="/" className="back-button dark-glass-card">
        <span className="back-arrow">←</span>
        <span>ආපසු</span>
      </Link>

      <div className="product-details-container">
        {/* Product Images Section */}
        <div className="product-images-section">
          <div className="main-image-container">
            <img 
              src={card.images[selectedImage]} 
              alt={card.name}
              className="main-product-image"
              onError={(e) => {
                e.target.src = '/god.jpg'; // Fallback image
              }}
            />
            {card.discount > 0 && (
              <div className="discount-badge-large">
                -{card.discount}%
              </div>
            )}
          </div>
          
          <div className="thumbnail-images">
            {card.images.map((image, index) => (
              <img
                key={index}
                src={image}
                alt={`${card.name} ${index + 1}`}
                className={`thumbnail ${selectedImage === index ? 'active' : ''}`}
                onClick={() => setSelectedImage(index)}
                onError={(e) => {
                  e.target.src = '/god.jpg'; // Fallback image
                }}
              />
            ))}
          </div>
        </div>

        {/* Product Info Section */}
        <div className="product-info-section">
          <div className="product-header dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>
            
            <h1 className="product-title">{card.name}</h1>
            <p className="product-english-name">{card.englishName}</p>
            
            <div className="product-rating">
              <div className="stars">
                {[...Array(5)].map((_, i) => (
                  <span key={i} className={i < Math.floor(card.rating) ? 'star filled' : 'star'}>
                    ⭐
                  </span>
                ))}
              </div>
              <span className="rating-value">{card.rating}</span>
              <span className="review-count">({card.reviewCount} සමාලෝචන)</span>
            </div>

            <div className="product-pricing">
              <span className="current-price">රු. {card.price.toLocaleString()}</span>
              {card.originalPrice > card.price && (
                <>
                  <span className="original-price">රු. {card.originalPrice.toLocaleString()}</span>
                  <span className="savings">ඔබ ඉතිරි කරන්නේ රු. {(card.originalPrice - card.price).toLocaleString()}</span>
                </>
              )}
            </div>

            <p className="product-description">{card.description}</p>
          </div>

          {/* Quantity and Purchase Section */}
          <div className="purchase-section dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>
            
            <div className="quantity-selector">
              <label htmlFor="quantity">ප්‍රමාණය:</label>
              <div className="quantity-controls">
                <button 
                  className="quantity-btn"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                >
                  -
                </button>
                <input
                  type="number"
                  id="quantity"
                  value={quantity}
                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  min="1"
                  max="10"
                />
                <button 
                  className="quantity-btn"
                  onClick={() => setQuantity(Math.min(10, quantity + 1))}
                  disabled={quantity >= 10}
                >
                  +
                </button>
              </div>
            </div>

            <div className="total-price">
              <span>මුළු මිල: රු. {(card.price * quantity).toLocaleString()}</span>
            </div>

            <div className="purchase-buttons">
              <button 
                className="add-to-cart-btn dark-glass-card"
                onClick={handleAddToCart}
              >
                <span className="btn-icon">🛒</span>
                <span>කාර්ට් එකට එකතු කරන්න</span>
              </button>
              
              <button 
                className="buy-now-btn dark-glass-card primary"
                onClick={handleBuyNow}
              >
                <span className="btn-icon">⚡</span>
                <span>දැන් මිලදී ගන්න</span>
              </button>
            </div>

            <div className="payment-info">
              <div className="payment-method">
                <span className="payment-icon">💰</span>
                <span>ගෙවීම: Cash on Delivery (COD)</span>
              </div>
              <div className="delivery-info">
                <span className="delivery-icon">🚚</span>
                <span>නිවසටම ගෙන්වා දීම: 2-3 දින</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Information Sections */}
      <div className="product-details-sections">
        {/* Benefits Section */}
        <div className="details-section dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>

          <h3 className="section-title">ප්‍රතිලාභ</h3>
          <div className="benefits-list">
            {card.benefits.map((benefit, index) => (
              <div key={index} className="benefit-item">
                <span className="benefit-icon">✨</span>
                <span className="benefit-text">{benefit}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Long Description Section */}
        <div className="details-section dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>

          <h3 className="section-title">විස්තරය</h3>
          <div className="long-description">
            {card.longDescription.split('\n').map((paragraph, index) => (
              paragraph.trim() && (
                <p key={index} className="description-paragraph">
                  {paragraph.trim()}
                </p>
              )
            ))}
          </div>
        </div>

        {/* Specifications Section */}
        <div className="details-section dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>

          <h3 className="section-title">විශේෂාංග</h3>
          <div className="specifications-list">
            {Object.entries(card.specifications).map(([key, value]) => (
              <div key={key} className="spec-item">
                <span className="spec-label">
                  {key === 'material' && 'ද්‍රව්‍ය:'}
                  {key === 'size' && 'ප්‍රමාණය:'}
                  {key === 'thickness' && 'ඝනකම:'}
                  {key === 'finish' && 'නිම කිරීම:'}
                  {key === 'packaging' && 'ඇසුරුම:'}
                </span>
                <span className="spec-value">{value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Related Products Section */}
      <div className="related-products-section">
        <div className="section-header dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>
          <h3 className="section-title">අනෙකුත් කුබේර කාඩ්පත්</h3>
        </div>

        <div className="related-products-grid">
          <Link to="/kubera-cards" className="view-all-products-btn dark-glass-card">
            <span className="btn-icon">🔮</span>
            <span className="btn-text">සියලුම කුබේර කාඩ්පත් බලන්න</span>
            <span className="btn-arrow">→</span>
          </Link>
        </div>
      </div>

      {/* Footer Blessing */}
      <div className="product-footer">
        <div className="divine-blessing">
          <span className="blessing-text">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailsPage;
