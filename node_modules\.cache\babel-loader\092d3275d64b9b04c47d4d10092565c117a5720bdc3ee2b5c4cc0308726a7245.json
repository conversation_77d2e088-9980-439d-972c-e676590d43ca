{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\ParticleBackground.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParticleBackground = () => {\n  _s();\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.size = Math.random() * 3 + 1;\n        this.speedX = (Math.random() - 0.5) * 0.5;\n        this.speedY = (Math.random() - 0.5) * 0.5;\n        this.opacity = Math.random() * 0.5 + 0.2;\n        this.color = `rgba(244, 208, 63, ${this.opacity})`;\n        this.pulseSpeed = Math.random() * 0.02 + 0.01;\n        this.pulsePhase = Math.random() * Math.PI * 2;\n      }\n      update() {\n        this.x += this.speedX;\n        this.y += this.speedY;\n\n        // Pulse effect\n        this.pulsePhase += this.pulseSpeed;\n        this.opacity = 0.2 + Math.sin(this.pulsePhase) * 0.3;\n        this.color = `rgba(244, 208, 63, ${this.opacity})`;\n\n        // Wrap around edges\n        if (this.x > canvas.width) this.x = 0;\n        if (this.x < 0) this.x = canvas.width;\n        if (this.y > canvas.height) this.y = 0;\n        if (this.y < 0) this.y = canvas.height;\n      }\n      draw() {\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fillStyle = this.color;\n        ctx.fill();\n\n        // Add glow effect\n        ctx.shadowBlur = 10;\n        ctx.shadowColor = this.color;\n        ctx.fill();\n        ctx.shadowBlur = 0;\n      }\n    }\n\n    // Create particles\n    const createParticles = () => {\n      const particleCount = Math.floor(canvas.width * canvas.height / 15000);\n      particlesRef.current = [];\n      for (let i = 0; i < particleCount; i++) {\n        particlesRef.current.push(new Particle());\n      }\n    };\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // Draw connections between nearby particles\n      for (let i = 0; i < particlesRef.current.length; i++) {\n        for (let j = i + 1; j < particlesRef.current.length; j++) {\n          const dx = particlesRef.current[i].x - particlesRef.current[j].x;\n          const dy = particlesRef.current[i].y - particlesRef.current[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particlesRef.current[i].x, particlesRef.current[i].y);\n            ctx.lineTo(particlesRef.current[j].x, particlesRef.current[j].y);\n            ctx.strokeStyle = `rgba(244, 208, 63, ${0.1 * (1 - distance / 100)})`;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        }\n      }\n\n      // Update and draw particles\n      particlesRef.current.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    createParticles();\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"canvas\", {\n    ref: canvasRef,\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      width: '100%',\n      height: '100%',\n      zIndex: 1,\n      pointerEvents: 'none'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(ParticleBackground, \"AoANNor1ZemK+BAAyEBUpug8tqg=\");\n_c = ParticleBackground;\nexport default ParticleBackground;\nvar _c;\n$RefreshReg$(_c, \"ParticleBackground\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ParticleBackground", "_s", "canvasRef", "animationRef", "particlesRef", "canvas", "current", "ctx", "getContext", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "Particle", "constructor", "x", "Math", "random", "y", "size", "speedX", "speedY", "opacity", "color", "pulseSpeed", "pulsePhase", "PI", "update", "sin", "draw", "beginPath", "arc", "fillStyle", "fill", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "createParticles", "particleCount", "floor", "i", "push", "animate", "clearRect", "length", "j", "dx", "dy", "distance", "sqrt", "moveTo", "lineTo", "strokeStyle", "lineWidth", "stroke", "for<PERSON>ach", "particle", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "ref", "style", "position", "top", "left", "zIndex", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/ParticleBackground.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst ParticleBackground = () => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    \n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    \n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.size = Math.random() * 3 + 1;\n        this.speedX = (Math.random() - 0.5) * 0.5;\n        this.speedY = (Math.random() - 0.5) * 0.5;\n        this.opacity = Math.random() * 0.5 + 0.2;\n        this.color = `rgba(244, 208, 63, ${this.opacity})`;\n        this.pulseSpeed = Math.random() * 0.02 + 0.01;\n        this.pulsePhase = Math.random() * Math.PI * 2;\n      }\n\n      update() {\n        this.x += this.speedX;\n        this.y += this.speedY;\n        \n        // Pulse effect\n        this.pulsePhase += this.pulseSpeed;\n        this.opacity = 0.2 + Math.sin(this.pulsePhase) * 0.3;\n        this.color = `rgba(244, 208, 63, ${this.opacity})`;\n        \n        // Wrap around edges\n        if (this.x > canvas.width) this.x = 0;\n        if (this.x < 0) this.x = canvas.width;\n        if (this.y > canvas.height) this.y = 0;\n        if (this.y < 0) this.y = canvas.height;\n      }\n\n      draw() {\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fillStyle = this.color;\n        ctx.fill();\n        \n        // Add glow effect\n        ctx.shadowBlur = 10;\n        ctx.shadowColor = this.color;\n        ctx.fill();\n        ctx.shadowBlur = 0;\n      }\n    }\n\n    // Create particles\n    const createParticles = () => {\n      const particleCount = Math.floor((canvas.width * canvas.height) / 15000);\n      particlesRef.current = [];\n      \n      for (let i = 0; i < particleCount; i++) {\n        particlesRef.current.push(new Particle());\n      }\n    };\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      // Draw connections between nearby particles\n      for (let i = 0; i < particlesRef.current.length; i++) {\n        for (let j = i + 1; j < particlesRef.current.length; j++) {\n          const dx = particlesRef.current[i].x - particlesRef.current[j].x;\n          const dy = particlesRef.current[i].y - particlesRef.current[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particlesRef.current[i].x, particlesRef.current[i].y);\n            ctx.lineTo(particlesRef.current[j].x, particlesRef.current[j].y);\n            ctx.strokeStyle = `rgba(244, 208, 63, ${0.1 * (1 - distance / 100)})`;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        }\n      }\n      \n      // Update and draw particles\n      particlesRef.current.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      \n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    createParticles();\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: 1,\n        pointerEvents: 'none'\n      }}\n    />\n  );\n};\n\nexport default ParticleBackground;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAMC,SAAS,GAAGL,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMM,YAAY,GAAGN,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMO,YAAY,GAAGP,MAAM,CAAC,EAAE,CAAC;EAE/BD,SAAS,CAAC,MAAM;IACd,MAAMS,MAAM,GAAGH,SAAS,CAACI,OAAO;IAChC,MAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;MACzBJ,MAAM,CAACK,KAAK,GAAGC,MAAM,CAACC,UAAU;MAChCP,MAAM,CAACQ,MAAM,GAAGF,MAAM,CAACG,WAAW;IACpC,CAAC;IAEDL,YAAY,CAAC,CAAC;IACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,EAAEN,YAAY,CAAC;;IAE/C;IACA,MAAMO,QAAQ,CAAC;MACbC,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGf,MAAM,CAACK,KAAK;QACrC,IAAI,CAACW,CAAC,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGf,MAAM,CAACQ,MAAM;QACtC,IAAI,CAACS,IAAI,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;QACjC,IAAI,CAACG,MAAM,GAAG,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACzC,IAAI,CAACI,MAAM,GAAG,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QACzC,IAAI,CAACK,OAAO,GAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;QACxC,IAAI,CAACM,KAAK,GAAG,sBAAsB,IAAI,CAACD,OAAO,GAAG;QAClD,IAAI,CAACE,UAAU,GAAGR,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;QAC7C,IAAI,CAACQ,UAAU,GAAGT,IAAI,CAACC,MAAM,CAAC,CAAC,GAAGD,IAAI,CAACU,EAAE,GAAG,CAAC;MAC/C;MAEAC,MAAMA,CAAA,EAAG;QACP,IAAI,CAACZ,CAAC,IAAI,IAAI,CAACK,MAAM;QACrB,IAAI,CAACF,CAAC,IAAI,IAAI,CAACG,MAAM;;QAErB;QACA,IAAI,CAACI,UAAU,IAAI,IAAI,CAACD,UAAU;QAClC,IAAI,CAACF,OAAO,GAAG,GAAG,GAAGN,IAAI,CAACY,GAAG,CAAC,IAAI,CAACH,UAAU,CAAC,GAAG,GAAG;QACpD,IAAI,CAACF,KAAK,GAAG,sBAAsB,IAAI,CAACD,OAAO,GAAG;;QAElD;QACA,IAAI,IAAI,CAACP,CAAC,GAAGb,MAAM,CAACK,KAAK,EAAE,IAAI,CAACQ,CAAC,GAAG,CAAC;QACrC,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EAAE,IAAI,CAACA,CAAC,GAAGb,MAAM,CAACK,KAAK;QACrC,IAAI,IAAI,CAACW,CAAC,GAAGhB,MAAM,CAACQ,MAAM,EAAE,IAAI,CAACQ,CAAC,GAAG,CAAC;QACtC,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EAAE,IAAI,CAACA,CAAC,GAAGhB,MAAM,CAACQ,MAAM;MACxC;MAEAmB,IAAIA,CAAA,EAAG;QACLzB,GAAG,CAAC0B,SAAS,CAAC,CAAC;QACf1B,GAAG,CAAC2B,GAAG,CAAC,IAAI,CAAChB,CAAC,EAAE,IAAI,CAACG,CAAC,EAAE,IAAI,CAACC,IAAI,EAAE,CAAC,EAAEH,IAAI,CAACU,EAAE,GAAG,CAAC,CAAC;QAClDtB,GAAG,CAAC4B,SAAS,GAAG,IAAI,CAACT,KAAK;QAC1BnB,GAAG,CAAC6B,IAAI,CAAC,CAAC;;QAEV;QACA7B,GAAG,CAAC8B,UAAU,GAAG,EAAE;QACnB9B,GAAG,CAAC+B,WAAW,GAAG,IAAI,CAACZ,KAAK;QAC5BnB,GAAG,CAAC6B,IAAI,CAAC,CAAC;QACV7B,GAAG,CAAC8B,UAAU,GAAG,CAAC;MACpB;IACF;;IAEA;IACA,MAAME,eAAe,GAAGA,CAAA,KAAM;MAC5B,MAAMC,aAAa,GAAGrB,IAAI,CAACsB,KAAK,CAAEpC,MAAM,CAACK,KAAK,GAAGL,MAAM,CAACQ,MAAM,GAAI,KAAK,CAAC;MACxET,YAAY,CAACE,OAAO,GAAG,EAAE;MAEzB,KAAK,IAAIoC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,aAAa,EAAEE,CAAC,EAAE,EAAE;QACtCtC,YAAY,CAACE,OAAO,CAACqC,IAAI,CAAC,IAAI3B,QAAQ,CAAC,CAAC,CAAC;MAC3C;IACF,CAAC;;IAED;IACA,MAAM4B,OAAO,GAAGA,CAAA,KAAM;MACpBrC,GAAG,CAACsC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAExC,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACQ,MAAM,CAAC;;MAEhD;MACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtC,YAAY,CAACE,OAAO,CAACwC,MAAM,EAAEJ,CAAC,EAAE,EAAE;QACpD,KAAK,IAAIK,CAAC,GAAGL,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAG3C,YAAY,CAACE,OAAO,CAACwC,MAAM,EAAEC,CAAC,EAAE,EAAE;UACxD,MAAMC,EAAE,GAAG5C,YAAY,CAACE,OAAO,CAACoC,CAAC,CAAC,CAACxB,CAAC,GAAGd,YAAY,CAACE,OAAO,CAACyC,CAAC,CAAC,CAAC7B,CAAC;UAChE,MAAM+B,EAAE,GAAG7C,YAAY,CAACE,OAAO,CAACoC,CAAC,CAAC,CAACrB,CAAC,GAAGjB,YAAY,CAACE,OAAO,CAACyC,CAAC,CAAC,CAAC1B,CAAC;UAChE,MAAM6B,QAAQ,GAAG/B,IAAI,CAACgC,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;UAE7C,IAAIC,QAAQ,GAAG,GAAG,EAAE;YAClB3C,GAAG,CAAC0B,SAAS,CAAC,CAAC;YACf1B,GAAG,CAAC6C,MAAM,CAAChD,YAAY,CAACE,OAAO,CAACoC,CAAC,CAAC,CAACxB,CAAC,EAAEd,YAAY,CAACE,OAAO,CAACoC,CAAC,CAAC,CAACrB,CAAC,CAAC;YAChEd,GAAG,CAAC8C,MAAM,CAACjD,YAAY,CAACE,OAAO,CAACyC,CAAC,CAAC,CAAC7B,CAAC,EAAEd,YAAY,CAACE,OAAO,CAACyC,CAAC,CAAC,CAAC1B,CAAC,CAAC;YAChEd,GAAG,CAAC+C,WAAW,GAAG,sBAAsB,GAAG,IAAI,CAAC,GAAGJ,QAAQ,GAAG,GAAG,CAAC,GAAG;YACrE3C,GAAG,CAACgD,SAAS,GAAG,GAAG;YACnBhD,GAAG,CAACiD,MAAM,CAAC,CAAC;UACd;QACF;MACF;;MAEA;MACApD,YAAY,CAACE,OAAO,CAACmD,OAAO,CAACC,QAAQ,IAAI;QACvCA,QAAQ,CAAC5B,MAAM,CAAC,CAAC;QACjB4B,QAAQ,CAAC1B,IAAI,CAAC,CAAC;MACjB,CAAC,CAAC;MAEF7B,YAAY,CAACG,OAAO,GAAGqD,qBAAqB,CAACf,OAAO,CAAC;IACvD,CAAC;IAEDL,eAAe,CAAC,CAAC;IACjBK,OAAO,CAAC,CAAC;;IAET;IACA,OAAO,MAAM;MACXjC,MAAM,CAACiD,mBAAmB,CAAC,QAAQ,EAAEnD,YAAY,CAAC;MAClD,IAAIN,YAAY,CAACG,OAAO,EAAE;QACxBuD,oBAAoB,CAAC1D,YAAY,CAACG,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEP,OAAA;IACE+D,GAAG,EAAE5D,SAAU;IACf6D,KAAK,EAAE;MACLC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPxD,KAAK,EAAE,MAAM;MACbG,MAAM,EAAE,MAAM;MACdsD,MAAM,EAAE,CAAC;MACTC,aAAa,EAAE;IACjB;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEN,CAAC;AAACvE,EAAA,CAjIID,kBAAkB;AAAAyE,EAAA,GAAlBzE,kBAAkB;AAmIxB,eAAeA,kBAAkB;AAAC,IAAAyE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}