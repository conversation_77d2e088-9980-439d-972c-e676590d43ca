import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import KuberaAnimation from './KuberaAnimation';
import { kuberaCards, cardCategories, getCardsByCategory } from '../data/kuberaCards';

const KuberaCardsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredCards, setFilteredCards] = useState(kuberaCards);

  useEffect(() => {
    setFilteredCards(getCardsByCategory(selectedCategory));
  }, [selectedCategory]);

  return (
    <div className="kubera-cards-page">
      <ParticleBackground />
      <KuberaAnimation />

      {/* Back Button */}
      <Link to="/" className="back-button dark-glass-card">
        <span className="back-arrow">←</span>
        <span>ආපසු</span>
      </Link>

      {/* Page Header */}
      <div className="page-header">
        <h1 className="page-title">කුබේර කාඩ්පත් එකතුව</h1>
        <p className="page-subtitle">
          ධන සමෘද්ධිය සහ අධ්‍යාත්මික ශක්තිය සඳහා විශේෂයෙන් නිර්මාණය කරන ලද කුබේර කාඩ්පත්
        </p>
      </div>

      {/* Category Filter */}
      <div className="category-filter">
        <div className="filter-container dark-glass-card">
          <div className="card-glow"></div>
          <div className="card-shine"></div>
          
          <h3 className="filter-title">කාඩ්පත් වර්ගය තෝරන්න</h3>
          <div className="category-buttons">
            {cardCategories.map((category) => (
              <button
                key={category.id}
                className={`category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Cards Grid */}
      <div className="cards-grid-container">
        <div className="cards-grid">
          {filteredCards.map((card, index) => (
            <Link
              key={card.id}
              to={`/kubera-card/${card.id}`}
              className="card-item dark-glass-card"
              style={{
                animationDelay: `${index * 0.1}s`
              }}
            >
              <div className="card-glow"></div>
              <div className="card-shine"></div>

              <div className="card-image-container">
                <img 
                  src={card.images[0]} 
                  alt={card.name}
                  className="card-image"
                  onError={(e) => {
                    e.target.src = '/god.jpg'; // Fallback to existing image
                  }}
                />
                {card.discount > 0 && (
                  <div className="discount-badge">
                    -{card.discount}%
                  </div>
                )}
                {card.featured && (
                  <div className="featured-badge">
                    ⭐ විශේෂ
                  </div>
                )}
              </div>

              <div className="card-content">
                <h4 className="card-name">{card.name}</h4>
                <p className="card-description">{card.description}</p>
                
                <div className="card-pricing">
                  <span className="current-price">රු. {card.price.toLocaleString()}</span>
                  {card.originalPrice > card.price && (
                    <span className="original-price">රු. {card.originalPrice.toLocaleString()}</span>
                  )}
                </div>

                <div className="card-rating">
                  <div className="stars">
                    {[...Array(5)].map((_, i) => (
                      <span key={i} className={i < Math.floor(card.rating) ? 'star filled' : 'star'}>
                        ⭐
                      </span>
                    ))}
                  </div>
                  <span className="rating-text">({card.reviewCount})</span>
                </div>

                <div className="card-benefits">
                  <div className="benefits-preview">
                    {card.benefits.slice(0, 2).map((benefit, idx) => (
                      <div key={idx} className="benefit-preview">
                        <span className="benefit-icon">✨</span>
                        <span className="benefit-text">{benefit}</span>
                      </div>
                    ))}
                    {card.benefits.length > 2 && (
                      <div className="more-benefits">
                        +{card.benefits.length - 2} තවත්
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="card-action">
                <span className="action-text">විස්තර බලන්න</span>
                <span className="action-arrow">→</span>
              </div>
            </Link>
          ))}
        </div>

        {filteredCards.length === 0 && (
          <div className="no-cards-message dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>
            <h3>කාඩ්පත් සොයා ගත නොහැක</h3>
            <p>මෙම වර්ගයේ කාඩ්පත් දැනට නොමැත. කරුණාකර වෙනත් වර්ගයක් තෝරන්න.</p>
          </div>
        )}
      </div>

      {/* Footer Blessing */}
      <div className="page-footer">
        <div className="divine-blessing">
          <span className="blessing-text">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>
        </div>
      </div>
    </div>
  );
};

export default KuberaCardsPage;
