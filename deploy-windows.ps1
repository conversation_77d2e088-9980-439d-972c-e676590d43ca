# PowerShell Deployment Script for Windows
# Deploy Premium Landing Page Updates to kubera.help

param(
    [switch]$BuildOnly,
    [switch]$UploadOnly,
    [switch]$Help
)

# Configuration
$SERVER_IP = "************"
$SERVER_USER = "ubuntu"
$APP_DIR = "/var/www/kubera.help"
$LOCAL_BUILD_DIR = ".\build"

# Colors for output
function Write-Info($message) {
    Write-Host "ℹ️  $message" -ForegroundColor Yellow
}

function Write-Success($message) {
    Write-Host "✅ $message" -ForegroundColor Green
}

function Write-Error($message) {
    Write-Host "❌ $message" -ForegroundColor Red
}

function Show-Help {
    Write-Host "Premium Landing Page Deployment Script" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage: .\deploy-windows.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -BuildOnly    Build the application only"
    Write-Host "  -UploadOnly   Upload files only (build must exist)"
    Write-Host "  -Help         Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\deploy-windows.ps1                # Full deployment"
    Write-Host "  .\deploy-windows.ps1 -BuildOnly     # Build only"
    Write-Host "  .\deploy-windows.ps1 -UploadOnly    # Upload only"
    Write-Host ""
}

function Test-ServerConnection {
    Write-Info "Testing server connection..."
    $ping = Test-Connection -ComputerName $SERVER_IP -Count 1 -Quiet
    if ($ping) {
        Write-Success "Server is reachable"
        return $true
    } else {
        Write-Error "Cannot reach server $SERVER_IP"
        return $false
    }
}

function Build-Application {
    Write-Info "Building React application..."
    
    if (!(Test-Path "package.json")) {
        Write-Error "package.json not found. Are you in the correct directory?"
        exit 1
    }
    
    try {
        Write-Info "Installing dependencies..."
        npm install
        
        Write-Info "Building production bundle..."
        npm run build
        
        if (Test-Path $LOCAL_BUILD_DIR) {
            Write-Success "Build completed successfully"
            return $true
        } else {
            Write-Error "Build failed - build directory not found"
            return $false
        }
    }
    catch {
        Write-Error "Build failed: $($_.Exception.Message)"
        return $false
    }
}

function Upload-Files {
    Write-Info "Uploading files to production server..."
    
    if (!(Test-Path $LOCAL_BUILD_DIR)) {
        Write-Error "Build directory not found. Run with -BuildOnly first."
        exit 1
    }
    
    try {
        # Check if scp is available
        $scpAvailable = Get-Command scp -ErrorAction SilentlyContinue
        if ($scpAvailable) {
            Write-Info "Using SCP to upload files..."
            scp -r "$LOCAL_BUILD_DIR\*" "${SERVER_USER}@${SERVER_IP}:${APP_DIR}/build/"
        } else {
            Write-Info "SCP not available. Please use one of these methods:"
            Write-Host ""
            Write-Host "Method 1 - Using Windows Subsystem for Linux (WSL):"
            Write-Host "  wsl rsync -avz --delete ./build/ ubuntu@************:/var/www/kubera.help/build/"
            Write-Host ""
            Write-Host "Method 2 - Using Git Bash:"
            Write-Host "  Open Git Bash in this directory and run:"
            Write-Host "  rsync -avz --delete ./build/ ubuntu@************:/var/www/kubera.help/build/"
            Write-Host ""
            Write-Host "Method 3 - Using WinSCP or FileZilla:"
            Write-Host "  1. Connect to ************ with username 'ubuntu'"
            Write-Host "  2. Navigate to /var/www/kubera.help/build/"
            Write-Host "  3. Upload all files from .\build\ directory"
            Write-Host ""
            return $false
        }
        
        Write-Success "Files uploaded successfully"
        return $true
    }
    catch {
        Write-Error "Upload failed: $($_.Exception.Message)"
        return $false
    }
}

function Restart-Services {
    Write-Info "Restarting services on production server..."
    
    try {
        # Restart PM2 backend
        ssh "${SERVER_USER}@${SERVER_IP}" "pm2 restart kubera-backend"
        
        # Reload Nginx
        ssh "${SERVER_USER}@${SERVER_IP}" "sudo systemctl reload nginx"
        
        # Check status
        Write-Info "Checking service status..."
        ssh "${SERVER_USER}@${SERVER_IP}" "pm2 status && sudo systemctl status nginx --no-pager -l"
        
        Write-Success "Services restarted successfully"
        return $true
    }
    catch {
        Write-Error "Service restart failed: $($_.Exception.Message)"
        return $false
    }
}

function Show-DeploymentSummary {
    Write-Host ""
    Write-Success "🎉 Deployment completed!"
    Write-Host ""
    Write-Host "🌐 Website: https://kubera.help" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📊 Useful commands:" -ForegroundColor Yellow
    Write-Host "  Check logs: ssh ubuntu@************ 'pm2 logs kubera-backend'"
    Write-Host "  Check status: ssh ubuntu@************ 'pm2 status'"
    Write-Host "  Monitor: ssh ubuntu@************ 'pm2 monit'"
    Write-Host ""
    Write-Host "🧪 Testing checklist:" -ForegroundColor Yellow
    Write-Host "  ✓ Visit https://kubera.help"
    Write-Host "  ✓ Hard refresh (Ctrl+F5)"
    Write-Host "  ✓ Test dark glass cards"
    Write-Host "  ✓ Test mobile responsiveness"
    Write-Host "  ✓ Verify all zodiac links work"
    Write-Host ""
}

# Main execution
if ($Help) {
    Show-Help
    exit 0
}

Write-Host "🚀 Deploying Premium Landing Page Updates" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

if ($BuildOnly) {
    Write-Info "Build-only mode"
    if (Build-Application) {
        Write-Success "Build completed successfully!"
    } else {
        Write-Error "Build failed!"
        exit 1
    }
    exit 0
}

if ($UploadOnly) {
    Write-Info "Upload-only mode"
    if (Test-ServerConnection) {
        if (Upload-Files) {
            Restart-Services
            Show-DeploymentSummary
        }
    }
    exit 0
}

# Full deployment
Write-Info "Starting full deployment..."

# Step 1: Build
if (!(Build-Application)) {
    exit 1
}

# Step 2: Test connection
if (!(Test-ServerConnection)) {
    Write-Info "You can still deploy manually using the steps in MANUAL_DEPLOYMENT_STEPS.md"
    exit 1
}

# Step 3: Upload
if (Upload-Files) {
    # Step 4: Restart services
    Restart-Services
    
    # Step 5: Show summary
    Show-DeploymentSummary
} else {
    Write-Info "Please follow the manual upload instructions shown above."
}
