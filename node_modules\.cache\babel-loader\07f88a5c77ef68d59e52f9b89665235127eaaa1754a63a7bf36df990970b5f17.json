{"ast": null, "code": "module.exports = require('./lib/axios');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/node_modules/axios/index.js"], "sourcesContent": ["module.exports = require('./lib/axios');"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}