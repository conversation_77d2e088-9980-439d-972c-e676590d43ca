{"ast": null, "code": "import React,{useEffect,useRef}from'react';import{jsx as _jsx}from\"react/jsx-runtime\";const ParticleBackground=()=>{const canvasRef=useRef(null);const animationRef=useRef(null);const particlesRef=useRef([]);useEffect(()=>{const canvas=canvasRef.current;const ctx=canvas.getContext('2d');// Set canvas size\nconst resizeCanvas=()=>{canvas.width=window.innerWidth;canvas.height=window.innerHeight;};resizeCanvas();window.addEventListener('resize',resizeCanvas);// Particle class\nclass Particle{constructor(){this.x=Math.random()*canvas.width;this.y=Math.random()*canvas.height;this.size=Math.random()*3+1;this.speedX=(Math.random()-0.5)*0.5;this.speedY=(Math.random()-0.5)*0.5;this.opacity=Math.random()*0.5+0.2;this.color=\"rgba(244, 208, 63, \".concat(this.opacity,\")\");this.pulseSpeed=Math.random()*0.02+0.01;this.pulsePhase=Math.random()*Math.PI*2;}update(){this.x+=this.speedX;this.y+=this.speedY;// Pulse effect\nthis.pulsePhase+=this.pulseSpeed;this.opacity=0.2+Math.sin(this.pulsePhase)*0.3;this.color=\"rgba(244, 208, 63, \".concat(this.opacity,\")\");// Wrap around edges\nif(this.x>canvas.width)this.x=0;if(this.x<0)this.x=canvas.width;if(this.y>canvas.height)this.y=0;if(this.y<0)this.y=canvas.height;}draw(){ctx.beginPath();ctx.arc(this.x,this.y,this.size,0,Math.PI*2);ctx.fillStyle=this.color;ctx.fill();// Add glow effect\nctx.shadowBlur=10;ctx.shadowColor=this.color;ctx.fill();ctx.shadowBlur=0;}}// Create particles\nconst createParticles=()=>{const particleCount=Math.floor(canvas.width*canvas.height/15000);particlesRef.current=[];for(let i=0;i<particleCount;i++){particlesRef.current.push(new Particle());}};// Animation loop\nconst animate=()=>{ctx.clearRect(0,0,canvas.width,canvas.height);// Draw connections between nearby particles\nfor(let i=0;i<particlesRef.current.length;i++){for(let j=i+1;j<particlesRef.current.length;j++){const dx=particlesRef.current[i].x-particlesRef.current[j].x;const dy=particlesRef.current[i].y-particlesRef.current[j].y;const distance=Math.sqrt(dx*dx+dy*dy);if(distance<100){ctx.beginPath();ctx.moveTo(particlesRef.current[i].x,particlesRef.current[i].y);ctx.lineTo(particlesRef.current[j].x,particlesRef.current[j].y);ctx.strokeStyle=\"rgba(244, 208, 63, \".concat(0.1*(1-distance/100),\")\");ctx.lineWidth=0.5;ctx.stroke();}}}// Update and draw particles\nparticlesRef.current.forEach(particle=>{particle.update();particle.draw();});animationRef.current=requestAnimationFrame(animate);};createParticles();animate();// Cleanup\nreturn()=>{window.removeEventListener('resize',resizeCanvas);if(animationRef.current){cancelAnimationFrame(animationRef.current);}};},[]);return/*#__PURE__*/_jsx(\"canvas\",{ref:canvasRef,style:{position:'fixed',top:0,left:0,width:'100%',height:'100%',zIndex:1,pointerEvents:'none'}});};export default ParticleBackground;", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "jsx", "_jsx", "ParticleBackground", "canvasRef", "animationRef", "particlesRef", "canvas", "current", "ctx", "getContext", "resizeCanvas", "width", "window", "innerWidth", "height", "innerHeight", "addEventListener", "Particle", "constructor", "x", "Math", "random", "y", "size", "speedX", "speedY", "opacity", "color", "concat", "pulseSpeed", "pulsePhase", "PI", "update", "sin", "draw", "beginPath", "arc", "fillStyle", "fill", "<PERSON><PERSON><PERSON><PERSON>", "shadowColor", "createParticles", "particleCount", "floor", "i", "push", "animate", "clearRect", "length", "j", "dx", "dy", "distance", "sqrt", "moveTo", "lineTo", "strokeStyle", "lineWidth", "stroke", "for<PERSON>ach", "particle", "requestAnimationFrame", "removeEventListener", "cancelAnimationFrame", "ref", "style", "position", "top", "left", "zIndex", "pointerEvents"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/ParticleBackground.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\n\nconst ParticleBackground = () => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  const particlesRef = useRef([]);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    \n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n    \n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Particle class\n    class Particle {\n      constructor() {\n        this.x = Math.random() * canvas.width;\n        this.y = Math.random() * canvas.height;\n        this.size = Math.random() * 3 + 1;\n        this.speedX = (Math.random() - 0.5) * 0.5;\n        this.speedY = (Math.random() - 0.5) * 0.5;\n        this.opacity = Math.random() * 0.5 + 0.2;\n        this.color = `rgba(244, 208, 63, ${this.opacity})`;\n        this.pulseSpeed = Math.random() * 0.02 + 0.01;\n        this.pulsePhase = Math.random() * Math.PI * 2;\n      }\n\n      update() {\n        this.x += this.speedX;\n        this.y += this.speedY;\n        \n        // Pulse effect\n        this.pulsePhase += this.pulseSpeed;\n        this.opacity = 0.2 + Math.sin(this.pulsePhase) * 0.3;\n        this.color = `rgba(244, 208, 63, ${this.opacity})`;\n        \n        // Wrap around edges\n        if (this.x > canvas.width) this.x = 0;\n        if (this.x < 0) this.x = canvas.width;\n        if (this.y > canvas.height) this.y = 0;\n        if (this.y < 0) this.y = canvas.height;\n      }\n\n      draw() {\n        ctx.beginPath();\n        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);\n        ctx.fillStyle = this.color;\n        ctx.fill();\n        \n        // Add glow effect\n        ctx.shadowBlur = 10;\n        ctx.shadowColor = this.color;\n        ctx.fill();\n        ctx.shadowBlur = 0;\n      }\n    }\n\n    // Create particles\n    const createParticles = () => {\n      const particleCount = Math.floor((canvas.width * canvas.height) / 15000);\n      particlesRef.current = [];\n      \n      for (let i = 0; i < particleCount; i++) {\n        particlesRef.current.push(new Particle());\n      }\n    };\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      // Draw connections between nearby particles\n      for (let i = 0; i < particlesRef.current.length; i++) {\n        for (let j = i + 1; j < particlesRef.current.length; j++) {\n          const dx = particlesRef.current[i].x - particlesRef.current[j].x;\n          const dy = particlesRef.current[i].y - particlesRef.current[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particlesRef.current[i].x, particlesRef.current[i].y);\n            ctx.lineTo(particlesRef.current[j].x, particlesRef.current[j].y);\n            ctx.strokeStyle = `rgba(244, 208, 63, ${0.1 * (1 - distance / 100)})`;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        }\n      }\n      \n      // Update and draw particles\n      particlesRef.current.forEach(particle => {\n        particle.update();\n        particle.draw();\n      });\n      \n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    createParticles();\n    animate();\n\n    // Cleanup\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        zIndex: 1,\n        pointerEvents: 'none'\n      }}\n    />\n  );\n};\n\nexport default ParticleBackground;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,MAAM,KAAQ,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEjD,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,SAAS,CAAGJ,MAAM,CAAC,IAAI,CAAC,CAC9B,KAAM,CAAAK,YAAY,CAAGL,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAM,YAAY,CAAGN,MAAM,CAAC,EAAE,CAAC,CAE/BD,SAAS,CAAC,IAAM,CACd,KAAM,CAAAQ,MAAM,CAAGH,SAAS,CAACI,OAAO,CAChC,KAAM,CAAAC,GAAG,CAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC,CAEnC;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBJ,MAAM,CAACK,KAAK,CAAGC,MAAM,CAACC,UAAU,CAChCP,MAAM,CAACQ,MAAM,CAAGF,MAAM,CAACG,WAAW,CACpC,CAAC,CAEDL,YAAY,CAAC,CAAC,CACdE,MAAM,CAACI,gBAAgB,CAAC,QAAQ,CAAEN,YAAY,CAAC,CAE/C;AACA,KAAM,CAAAO,QAAS,CACbC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,CAAC,CAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGf,MAAM,CAACK,KAAK,CACrC,IAAI,CAACW,CAAC,CAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGf,MAAM,CAACQ,MAAM,CACtC,IAAI,CAACS,IAAI,CAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAG,CAAC,CACjC,IAAI,CAACG,MAAM,CAAG,CAACJ,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,GAAG,CACzC,IAAI,CAACI,MAAM,CAAG,CAACL,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,EAAI,GAAG,CACzC,IAAI,CAACK,OAAO,CAAGN,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAG,GAAG,CACxC,IAAI,CAACM,KAAK,uBAAAC,MAAA,CAAyB,IAAI,CAACF,OAAO,KAAG,CAClD,IAAI,CAACG,UAAU,CAAGT,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,IAAI,CAAG,IAAI,CAC7C,IAAI,CAACS,UAAU,CAAGV,IAAI,CAACC,MAAM,CAAC,CAAC,CAAGD,IAAI,CAACW,EAAE,CAAG,CAAC,CAC/C,CAEAC,MAAMA,CAAA,CAAG,CACP,IAAI,CAACb,CAAC,EAAI,IAAI,CAACK,MAAM,CACrB,IAAI,CAACF,CAAC,EAAI,IAAI,CAACG,MAAM,CAErB;AACA,IAAI,CAACK,UAAU,EAAI,IAAI,CAACD,UAAU,CAClC,IAAI,CAACH,OAAO,CAAG,GAAG,CAAGN,IAAI,CAACa,GAAG,CAAC,IAAI,CAACH,UAAU,CAAC,CAAG,GAAG,CACpD,IAAI,CAACH,KAAK,uBAAAC,MAAA,CAAyB,IAAI,CAACF,OAAO,KAAG,CAElD;AACA,GAAI,IAAI,CAACP,CAAC,CAAGb,MAAM,CAACK,KAAK,CAAE,IAAI,CAACQ,CAAC,CAAG,CAAC,CACrC,GAAI,IAAI,CAACA,CAAC,CAAG,CAAC,CAAE,IAAI,CAACA,CAAC,CAAGb,MAAM,CAACK,KAAK,CACrC,GAAI,IAAI,CAACW,CAAC,CAAGhB,MAAM,CAACQ,MAAM,CAAE,IAAI,CAACQ,CAAC,CAAG,CAAC,CACtC,GAAI,IAAI,CAACA,CAAC,CAAG,CAAC,CAAE,IAAI,CAACA,CAAC,CAAGhB,MAAM,CAACQ,MAAM,CACxC,CAEAoB,IAAIA,CAAA,CAAG,CACL1B,GAAG,CAAC2B,SAAS,CAAC,CAAC,CACf3B,GAAG,CAAC4B,GAAG,CAAC,IAAI,CAACjB,CAAC,CAAE,IAAI,CAACG,CAAC,CAAE,IAAI,CAACC,IAAI,CAAE,CAAC,CAAEH,IAAI,CAACW,EAAE,CAAG,CAAC,CAAC,CAClDvB,GAAG,CAAC6B,SAAS,CAAG,IAAI,CAACV,KAAK,CAC1BnB,GAAG,CAAC8B,IAAI,CAAC,CAAC,CAEV;AACA9B,GAAG,CAAC+B,UAAU,CAAG,EAAE,CACnB/B,GAAG,CAACgC,WAAW,CAAG,IAAI,CAACb,KAAK,CAC5BnB,GAAG,CAAC8B,IAAI,CAAC,CAAC,CACV9B,GAAG,CAAC+B,UAAU,CAAG,CAAC,CACpB,CACF,CAEA;AACA,KAAM,CAAAE,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,aAAa,CAAGtB,IAAI,CAACuB,KAAK,CAAErC,MAAM,CAACK,KAAK,CAAGL,MAAM,CAACQ,MAAM,CAAI,KAAK,CAAC,CACxET,YAAY,CAACE,OAAO,CAAG,EAAE,CAEzB,IAAK,GAAI,CAAAqC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,aAAa,CAAEE,CAAC,EAAE,CAAE,CACtCvC,YAAY,CAACE,OAAO,CAACsC,IAAI,CAAC,GAAI,CAAA5B,QAAQ,CAAC,CAAC,CAAC,CAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAA6B,OAAO,CAAGA,CAAA,GAAM,CACpBtC,GAAG,CAACuC,SAAS,CAAC,CAAC,CAAE,CAAC,CAAEzC,MAAM,CAACK,KAAK,CAAEL,MAAM,CAACQ,MAAM,CAAC,CAEhD;AACA,IAAK,GAAI,CAAA8B,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGvC,YAAY,CAACE,OAAO,CAACyC,MAAM,CAAEJ,CAAC,EAAE,CAAE,CACpD,IAAK,GAAI,CAAAK,CAAC,CAAGL,CAAC,CAAG,CAAC,CAAEK,CAAC,CAAG5C,YAAY,CAACE,OAAO,CAACyC,MAAM,CAAEC,CAAC,EAAE,CAAE,CACxD,KAAM,CAAAC,EAAE,CAAG7C,YAAY,CAACE,OAAO,CAACqC,CAAC,CAAC,CAACzB,CAAC,CAAGd,YAAY,CAACE,OAAO,CAAC0C,CAAC,CAAC,CAAC9B,CAAC,CAChE,KAAM,CAAAgC,EAAE,CAAG9C,YAAY,CAACE,OAAO,CAACqC,CAAC,CAAC,CAACtB,CAAC,CAAGjB,YAAY,CAACE,OAAO,CAAC0C,CAAC,CAAC,CAAC3B,CAAC,CAChE,KAAM,CAAA8B,QAAQ,CAAGhC,IAAI,CAACiC,IAAI,CAACH,EAAE,CAAGA,EAAE,CAAGC,EAAE,CAAGA,EAAE,CAAC,CAE7C,GAAIC,QAAQ,CAAG,GAAG,CAAE,CAClB5C,GAAG,CAAC2B,SAAS,CAAC,CAAC,CACf3B,GAAG,CAAC8C,MAAM,CAACjD,YAAY,CAACE,OAAO,CAACqC,CAAC,CAAC,CAACzB,CAAC,CAAEd,YAAY,CAACE,OAAO,CAACqC,CAAC,CAAC,CAACtB,CAAC,CAAC,CAChEd,GAAG,CAAC+C,MAAM,CAAClD,YAAY,CAACE,OAAO,CAAC0C,CAAC,CAAC,CAAC9B,CAAC,CAAEd,YAAY,CAACE,OAAO,CAAC0C,CAAC,CAAC,CAAC3B,CAAC,CAAC,CAChEd,GAAG,CAACgD,WAAW,uBAAA5B,MAAA,CAAyB,GAAG,EAAI,CAAC,CAAGwB,QAAQ,CAAG,GAAG,CAAC,KAAG,CACrE5C,GAAG,CAACiD,SAAS,CAAG,GAAG,CACnBjD,GAAG,CAACkD,MAAM,CAAC,CAAC,CACd,CACF,CACF,CAEA;AACArD,YAAY,CAACE,OAAO,CAACoD,OAAO,CAACC,QAAQ,EAAI,CACvCA,QAAQ,CAAC5B,MAAM,CAAC,CAAC,CACjB4B,QAAQ,CAAC1B,IAAI,CAAC,CAAC,CACjB,CAAC,CAAC,CAEF9B,YAAY,CAACG,OAAO,CAAGsD,qBAAqB,CAACf,OAAO,CAAC,CACvD,CAAC,CAEDL,eAAe,CAAC,CAAC,CACjBK,OAAO,CAAC,CAAC,CAET;AACA,MAAO,IAAM,CACXlC,MAAM,CAACkD,mBAAmB,CAAC,QAAQ,CAAEpD,YAAY,CAAC,CAClD,GAAIN,YAAY,CAACG,OAAO,CAAE,CACxBwD,oBAAoB,CAAC3D,YAAY,CAACG,OAAO,CAAC,CAC5C,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEN,IAAA,WACE+D,GAAG,CAAE7D,SAAU,CACf8D,KAAK,CAAE,CACLC,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPzD,KAAK,CAAE,MAAM,CACbG,MAAM,CAAE,MAAM,CACduD,MAAM,CAAE,CAAC,CACTC,aAAa,CAAE,MACjB,CAAE,CACH,CAAC,CAEN,CAAC,CAED,cAAe,CAAApE,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}