import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import KuberaAnimation from './KuberaAnimation';
import { useCart } from '../context/CartContext';

const CartPage = () => {
  const { cart, updateQuantity, removeFromCart, getCartTotal, getCartItemCount } = useCart();
  const navigate = useNavigate();

  const handleQuantityChange = (itemId, newQuantity) => {
    if (newQuantity < 1) {
      removeFromCart(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleCheckout = () => {
    if (cart.items.length > 0) {
      navigate('/checkout');
    }
  };

  const formatPrice = (price) => {
    return `රු. ${price.toLocaleString()}`;
  };

  if (cart.items.length === 0) {
    return (
      <div className="cart-page">
        <ParticleBackground />
        <KuberaAnimation />

        {/* Back Button */}
        <Link to="/" className="back-button dark-glass-card">
          <span className="back-arrow">←</span>
          <span>ආපසු</span>
        </Link>

        {/* Empty Cart */}
        <div className="empty-cart-container">
          <div className="empty-cart-card dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>
            
            <div className="empty-cart-icon">🛒</div>
            <h2 className="empty-cart-title">ඔබගේ කාර්ට් එක හිස්ය</h2>
            <p className="empty-cart-message">
              කුබේර කාඩ්පත් එකතුවෙන් ඔබට අවශ්‍ය කාඩ්පත් තෝරා ගන්න
            </p>
            
            <div className="empty-cart-actions">
              <Link to="/kubera-cards" className="shop-now-btn dark-glass-card">
                <span className="btn-icon">🔮</span>
                <span>කාඩ්පත් බලන්න</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Footer Blessing */}
        <div className="cart-footer">
          <div className="divine-blessing">
            <span className="blessing-text">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="cart-page">
      <ParticleBackground />
      <KuberaAnimation />

      {/* Back Button */}
      <Link to="/" className="back-button dark-glass-card">
        <span className="back-arrow">←</span>
        <span>ආපසු</span>
      </Link>

      {/* Page Header */}
      <div className="cart-header">
        <h1 className="cart-title">ඔබගේ කාර්ට් එක</h1>
        <p className="cart-subtitle">
          {getCartItemCount()} කාඩ්පත් තෝරා ගෙන ඇත
        </p>
      </div>

      <div className="cart-container">
        {/* Cart Items */}
        <div className="cart-items-section">
          <div className="cart-items-header dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>
            <h3>තෝරාගත් කාඩ්පත්</h3>
          </div>

          <div className="cart-items-list">
            {cart.items.map((item) => (
              <div key={item.id} className="cart-item dark-glass-card">
                <div className="card-glow"></div>
                <div className="card-shine"></div>

                <div className="item-image">
                  <img 
                    src={item.image} 
                    alt={item.name}
                    onError={(e) => {
                      e.target.src = '/god.jpg';
                    }}
                  />
                </div>

                <div className="item-details">
                  <h4 className="item-name">{item.name}</h4>
                  <p className="item-price">{formatPrice(item.price)}</p>
                  
                  <div className="quantity-controls">
                    <button 
                      className="quantity-btn"
                      onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                    >
                      -
                    </button>
                    <span className="quantity-display">{item.quantity}</span>
                    <button 
                      className="quantity-btn"
                      onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                    >
                      +
                    </button>
                  </div>
                </div>

                <div className="item-total">
                  <div className="total-price">
                    {formatPrice(item.price * item.quantity)}
                  </div>
                  <button 
                    className="remove-btn"
                    onClick={() => removeFromCart(item.id)}
                    title="කාර්ට් එකෙන් ඉවත් කරන්න"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Cart Summary */}
        <div className="cart-summary-section">
          <div className="cart-summary dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>

            <h3 className="summary-title">ගණන් කිරීම</h3>
            
            <div className="summary-details">
              <div className="summary-row">
                <span>කාඩ්පත් ගණන:</span>
                <span>{getCartItemCount()}</span>
              </div>
              
              <div className="summary-row">
                <span>උප එකතුව:</span>
                <span>{formatPrice(getCartTotal())}</span>
              </div>
              
              <div className="summary-row">
                <span>ගෙන්වා දීමේ ගාස්තුව:</span>
                <span>නොමිලේ</span>
              </div>
              
              <div className="summary-divider"></div>
              
              <div className="summary-row total-row">
                <span>මුළු එකතුව:</span>
                <span>{formatPrice(getCartTotal())}</span>
              </div>
            </div>

            <div className="payment-info">
              <div className="payment-method">
                <span className="payment-icon">💰</span>
                <span>ගෙවීම: Cash on Delivery</span>
              </div>
              <div className="delivery-info">
                <span className="delivery-icon">🚚</span>
                <span>ගෙන්වා දීම: 2-3 දින</span>
              </div>
            </div>

            <button 
              className="checkout-btn dark-glass-card primary"
              onClick={handleCheckout}
            >
              <span className="btn-icon">⚡</span>
              <span>ගෙවීමට යන්න</span>
            </button>

            <Link to="/kubera-cards" className="continue-shopping-btn">
              <span>← තවත් කාඩ්පත් එකතු කරන්න</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Footer Blessing */}
      <div className="cart-footer">
        <div className="divine-blessing">
          <span className="blessing-text">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>
        </div>
      </div>
    </div>
  );
};

export default CartPage;
