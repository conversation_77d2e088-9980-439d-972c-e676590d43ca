{"ast": null, "code": "'use strict';\n\nvar utils = require('./../utils');\nvar defaults = require('../defaults');\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Object|String} data The data to be transformed\n * @param {Array} headers The headers for the request or response\n * @param {Array|Function} fns A single function or Array of functions\n * @returns {*} The resulting transformed data\n */\nmodule.exports = function transformData(data, headers, fns) {\n  var context = this || defaults;\n  /*eslint no-param-reassign:0*/\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(context, data, headers);\n  });\n  return data;\n};", "map": {"version": 3, "names": ["utils", "require", "defaults", "module", "exports", "transformData", "data", "headers", "fns", "context", "for<PERSON>ach", "transform", "fn", "call"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/core/transformData.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('./../utils');\nvar defaults = require('../defaults');\n\n/**\n * Transform the data for a request or a response\n *\n * @param {Object|String} data The data to be transformed\n * @param {Array} headers The headers for the request or response\n * @param {Array|Function} fns A single function or Array of functions\n * @returns {*} The resulting transformed data\n */\nmodule.exports = function transformData(data, headers, fns) {\n  var context = this || defaults;\n  /*eslint no-param-reassign:0*/\n  utils.forEach(fns, function transform(fn) {\n    data = fn.call(context, data, headers);\n  });\n\n  return data;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAE,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAEC,GAAG,EAAE;EAC1D,IAAIC,OAAO,GAAG,IAAI,IAAIP,QAAQ;EAC9B;EACAF,KAAK,CAACU,OAAO,CAACF,GAAG,EAAE,SAASG,SAASA,CAACC,EAAE,EAAE;IACxCN,IAAI,GAAGM,EAAE,CAACC,IAAI,CAACJ,OAAO,EAAEH,IAAI,EAAEC,OAAO,CAAC;EACxC,CAAC,CAAC;EAEF,OAAOD,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}