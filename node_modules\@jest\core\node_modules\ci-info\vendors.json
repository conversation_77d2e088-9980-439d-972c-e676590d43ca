[{"name": "Appcircle", "constant": "APPCIRCLE", "env": "AC_APPCIRCLE"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constant": "APPVEYOR", "env": "APPVEYOR", "pr": "APPVEYOR_PULL_REQUEST_NUMBER"}, {"name": "AWS CodeBuild", "constant": "CODEBUILD", "env": "CODEBUILD_BUILD_ARN"}, {"name": "Azure Pipelines", "constant": "AZURE_PIPELINES", "env": "TF_BUILD", "pr": {"BUILD_REASON": "PullRequest"}}, {"name": "Bamboo", "constant": "BAMBOO", "env": "bamboo_planKey"}, {"name": "Bitbucket Pipelines", "constant": "BITBUCKET", "env": "BITBUCKET_COMMIT", "pr": "BITBUCKET_PR_ID"}, {"name": "Bitrise", "constant": "BITRISE", "env": "BITRISE_IO", "pr": "BITRISE_PULL_REQUEST"}, {"name": "<PERSON>", "constant": "BUDDY", "env": "BUDDY_WORKSPACE_ID", "pr": "BUDDY_EXECUTION_PULL_REQUEST_ID"}, {"name": "Buildkite", "constant": "BUILDKITE", "env": "BUILDKITE", "pr": {"env": "BUILDKITE_PULL_REQUEST", "ne": "false"}}, {"name": "CircleCI", "constant": "CIRCLE", "env": "CIRCLECI", "pr": "CIRCLE_PULL_REQUEST"}, {"name": "Cirrus CI", "constant": "CIRRUS", "env": "CIRRUS_CI", "pr": "CIRRUS_PR"}, {"name": "Codefresh", "constant": "CODEFRESH", "env": "CF_BUILD_ID", "pr": {"any": ["CF_PULL_REQUEST_NUMBER", "CF_PULL_REQUEST_ID"]}}, {"name": "Codemagic", "constant": "CODEMAGIC", "env": "CM_BUILD_ID", "pr": "CM_PULL_REQUEST"}, {"name": "Codeship", "constant": "CODESHIP", "env": {"CI_NAME": "codeship"}}, {"name": "Drone", "constant": "DRONE", "env": "DRONE", "pr": {"DRONE_BUILD_EVENT": "pull_request"}}, {"name": "<PERSON><PERSON><PERSON>", "constant": "DSARI", "env": "DSARI"}, {"name": "Expo Application Services", "constant": "EAS", "env": "EAS_BUILD"}, {"name": "<PERSON><PERSON><PERSON>", "constant": "GERRIT", "env": "GERRIT_PROJECT"}, {"name": "GitHub Actions", "constant": "GITHUB_ACTIONS", "env": "GITHUB_ACTIONS", "pr": {"GITHUB_EVENT_NAME": "pull_request"}}, {"name": "GitLab CI", "constant": "GITLAB", "env": "GITLAB_CI", "pr": "CI_MERGE_REQUEST_ID"}, {"name": "GoCD", "constant": "GOCD", "env": "GO_PIPELINE_LABEL"}, {"name": "Google Cloud Build", "constant": "GOOGLE_CLOUD_BUILD", "env": "BUILDER_OUTPUT"}, {"name": "Harness CI", "constant": "HARNESS", "env": "HARNESS_BUILD_ID"}, {"name": "Heroku", "constant": "HEROKU", "env": {"env": "NODE", "includes": "/app/.heroku/node/bin/node"}}, {"name": "<PERSON>", "constant": "HUDSON", "env": "HUDSON_URL"}, {"name": "<PERSON>", "constant": "JENKINS", "env": ["JENKINS_URL", "BUILD_ID"], "pr": {"any": ["ghprbPullId", "CHANGE_ID"]}}, {"name": "LayerCI", "constant": "LAYERCI", "env": "LAYERCI", "pr": "LAYERCI_PULL_REQUEST"}, {"name": "Magnum CI", "constant": "MAGNUM", "env": "MAGNUM"}, {"name": "Netlify CI", "constant": "NETLIFY", "env": "NETLIFY", "pr": {"env": "PULL_REQUEST", "ne": "false"}}, {"name": "Nevercode", "constant": "NEVERCODE", "env": "NEVERCODE", "pr": {"env": "NEVERCODE_PULL_REQUEST", "ne": "false"}}, {"name": "ReleaseHub", "constant": "RELEASEHUB", "env": "RELEASE_BUILD_ID"}, {"name": "Render", "constant": "RENDER", "env": "RENDER", "pr": {"IS_PULL_REQUEST": "true"}}, {"name": "Sail CI", "constant": "SAIL", "env": "SAILCI", "pr": "SAIL_PULL_REQUEST_NUMBER"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "constant": "SCREWDRIVER", "env": "SCREWDRIVER", "pr": {"env": "SD_PULL_REQUEST", "ne": "false"}}, {"name": "Semaphore", "constant": "SEMAPHORE", "env": "SEMAPHORE", "pr": "PULL_REQUEST_NUMBER"}, {"name": "Shippable", "constant": "SHIPPABLE", "env": "SHIPPABLE", "pr": {"IS_PULL_REQUEST": "true"}}, {"name": "Solano CI", "constant": "SOLANO", "env": "TDDIUM", "pr": "TDDIUM_PR_ID"}, {"name": "<PERSON><PERSON><PERSON>", "constant": "SOURCEHUT", "env": {"CI_NAME": "<PERSON><PERSON><PERSON>"}}, {"name": "Strider CD", "constant": "STRIDER", "env": "STRIDER"}, {"name": "TaskCluster", "constant": "TASKCLUSTER", "env": ["TASK_ID", "RUN_ID"]}, {"name": "TeamCity", "constant": "TEAMCITY", "env": "TEAMCITY_VERSION"}, {"name": "Travis CI", "constant": "TRAVIS", "env": "TRAVIS", "pr": {"env": "TRAVIS_PULL_REQUEST", "ne": "false"}}, {"name": "Vercel", "constant": "VERCEL", "env": {"any": ["NOW_BUILDER", "VERCEL"]}, "pr": "VERCEL_GIT_PULL_REQUEST_ID"}, {"name": "Visual Studio App Center", "constant": "APPCENTER", "env": "APPCENTER_BUILD_ID"}, {"name": "Wood<PERSON>ker", "constant": "WOODPECKER", "env": {"CI": "woodpecker"}, "pr": {"CI_BUILD_EVENT": "pull_request"}}, {"name": "Xcode Cloud", "constant": "XCODE_CLOUD", "env": "CI_XCODE_PROJECT", "pr": "CI_PULL_REQUEST_NUMBER"}, {"name": "Xcode Server", "constant": "XCODE_SERVER", "env": "XCS"}]