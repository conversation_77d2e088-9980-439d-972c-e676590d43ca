import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import ParticleBackground from './ParticleBackground';
import KuberaAnimation from './KuberaAnimation';
import { useCart } from '../context/CartContext';

const CheckoutPage = () => {
  const { cart, getCartTotal, getCartItemCount, clearCart } = useCart();
  const navigate = useNavigate();
  
  const [customerInfo, setCustomerInfo] = useState({
    fullName: '',
    phone: '',
    email: '',
    address: '',
    city: '',
    postalCode: '',
    specialInstructions: ''
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect to cart if empty
  useEffect(() => {
    if (cart.items.length === 0) {
      navigate('/cart');
    }
  }, [cart.items.length, navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCustomerInfo(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!customerInfo.fullName.trim()) {
      newErrors.fullName = 'සම්පූර්ණ නම අවශ්‍යයි';
    }
    
    if (!customerInfo.phone.trim()) {
      newErrors.phone = 'දුරකථන අංකය අවශ්‍යයි';
    } else if (!/^[0-9+\-\s()]{10,}$/.test(customerInfo.phone.trim())) {
      newErrors.phone = 'වලංගු දුරකථන අංකයක් ඇතුළත් කරන්න';
    }
    
    if (!customerInfo.address.trim()) {
      newErrors.address = 'ලිපිනය අවශ්‍යයි';
    }
    
    if (!customerInfo.city.trim()) {
      newErrors.city = 'නගරය අවශ්‍යයි';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Create order object
      const order = {
        id: `KUB-${Date.now()}`,
        items: cart.items,
        customerInfo,
        total: getCartTotal(),
        itemCount: getCartItemCount(),
        orderDate: new Date().toISOString(),
        status: 'pending',
        paymentMethod: 'cod'
      };
      
      // Save order to localStorage (in a real app, this would be sent to a server)
      const existingOrders = JSON.parse(localStorage.getItem('kuberaOrders') || '[]');
      existingOrders.push(order);
      localStorage.setItem('kuberaOrders', JSON.stringify(existingOrders));
      
      // Clear cart
      clearCart();
      
      // Navigate to confirmation page
      navigate(`/order-confirmation/${order.id}`, { 
        state: { order } 
      });
      
    } catch (error) {
      console.error('Order submission error:', error);
      alert('ඇණවුම ගබඩා කිරීමේදී දෝෂයක් ඇතිවිය. කරුණාකර නැවත උත්සාහ කරන්න.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatPrice = (price) => {
    return `රු. ${price.toLocaleString()}`;
  };

  if (cart.items.length === 0) {
    return null; // Will redirect via useEffect
  }

  return (
    <div className="checkout-page">
      <ParticleBackground />
      <KuberaAnimation />

      {/* Back Button */}
      <Link to="/cart" className="back-button dark-glass-card">
        <span className="back-arrow">←</span>
        <span>කාර්ට් එකට ආපසු</span>
      </Link>

      {/* Page Header */}
      <div className="checkout-header">
        <h1 className="checkout-title">ගෙවීම් පිටුව</h1>
        <p className="checkout-subtitle">
          ඔබගේ ඇණවුම සම්පූර්ණ කිරීම සඳහා තොරතුරු ඇතුළත් කරන්න
        </p>
      </div>

      <div className="checkout-container">
        {/* Customer Information Form */}
        <div className="checkout-form-section">
          <div className="form-card dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>
            
            <h3 className="form-title">ගනුදෙනුකරු තොරතුරු</h3>
            
            <form onSubmit={handleSubmit} className="checkout-form">
              <div className="form-group">
                <label htmlFor="fullName">සම්පූර්ණ නම *</label>
                <input
                  type="text"
                  id="fullName"
                  name="fullName"
                  value={customerInfo.fullName}
                  onChange={handleInputChange}
                  className={errors.fullName ? 'error' : ''}
                  placeholder="ඔබගේ සම්පූර්ණ නම ඇතුළත් කරන්න"
                />
                {errors.fullName && <span className="error-message">{errors.fullName}</span>}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="phone">දුරකථන අංකය *</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={customerInfo.phone}
                    onChange={handleInputChange}
                    className={errors.phone ? 'error' : ''}
                    placeholder="************"
                  />
                  {errors.phone && <span className="error-message">{errors.phone}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="email">ඊමේල් ලිපිනය</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={customerInfo.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="address">ලිපිනය *</label>
                <textarea
                  id="address"
                  name="address"
                  value={customerInfo.address}
                  onChange={handleInputChange}
                  className={errors.address ? 'error' : ''}
                  placeholder="ඔබගේ සම්පූර්ණ ලිපිනය ඇතුළත් කරන්න"
                  rows="3"
                />
                {errors.address && <span className="error-message">{errors.address}</span>}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="city">නගරය *</label>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    value={customerInfo.city}
                    onChange={handleInputChange}
                    className={errors.city ? 'error' : ''}
                    placeholder="කොළඹ"
                  />
                  {errors.city && <span className="error-message">{errors.city}</span>}
                </div>

                <div className="form-group">
                  <label htmlFor="postalCode">තැපැල් කේතය</label>
                  <input
                    type="text"
                    id="postalCode"
                    name="postalCode"
                    value={customerInfo.postalCode}
                    onChange={handleInputChange}
                    placeholder="00100"
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="specialInstructions">විශේෂ උපදෙස්</label>
                <textarea
                  id="specialInstructions"
                  name="specialInstructions"
                  value={customerInfo.specialInstructions}
                  onChange={handleInputChange}
                  placeholder="ගෙන්වා දීම සඳහා විශේෂ උපදෙස් (වෛකල්පික)"
                  rows="2"
                />
              </div>

              <div className="payment-method-info">
                <h4>ගෙවීමේ ක්‍රමය</h4>
                <div className="payment-option">
                  <span className="payment-icon">💰</span>
                  <div className="payment-details">
                    <strong>Cash on Delivery (COD)</strong>
                    <p>භාණ්ඩ ලැබෙන විට ගෙවන්න</p>
                  </div>
                </div>
              </div>

              <button 
                type="submit" 
                className="submit-order-btn dark-glass-card primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading-spinner"></span>
                    <span>ඇණවුම ගබඩා කරමින්...</span>
                  </>
                ) : (
                  <>
                    <span className="btn-icon">✅</span>
                    <span>ඇණවුම තහවුරු කරන්න</span>
                  </>
                )}
              </button>
            </form>
          </div>
        </div>

        {/* Order Summary Section */}
        <div className="order-summary-section">
          <div className="summary-card dark-glass-card">
            <div className="card-glow"></div>
            <div className="card-shine"></div>

            <h3 className="summary-title">ඇණවුම් සාරාංශය</h3>

            <div className="order-items">
              {cart.items.map((item) => (
                <div key={item.id} className="order-item">
                  <div className="item-image">
                    <img
                      src={item.image}
                      alt={item.name}
                      onError={(e) => {
                        e.target.src = '/god.jpg';
                      }}
                    />
                  </div>
                  <div className="item-details">
                    <h4 className="item-name">{item.name}</h4>
                    <div className="item-quantity">ප්‍රමාණය: {item.quantity}</div>
                    <div className="item-price">{formatPrice(item.price)}</div>
                  </div>
                  <div className="item-total">
                    {formatPrice(item.price * item.quantity)}
                  </div>
                </div>
              ))}
            </div>

            <div className="order-totals">
              <div className="total-row">
                <span>කාඩ්පත් ගණන:</span>
                <span>{getCartItemCount()}</span>
              </div>

              <div className="total-row">
                <span>උප එකතුව:</span>
                <span>{formatPrice(getCartTotal())}</span>
              </div>

              <div className="total-row">
                <span>ගෙන්වා දීමේ ගාස්තුව:</span>
                <span>නොමිලේ</span>
              </div>

              <div className="total-divider"></div>

              <div className="total-row final-total">
                <span>මුළු එකතුව:</span>
                <span>{formatPrice(getCartTotal())}</span>
              </div>
            </div>

            <div className="delivery-info">
              <div className="delivery-item">
                <span className="delivery-icon">🚚</span>
                <div className="delivery-details">
                  <strong>ගෙන්වා දීම</strong>
                  <p>2-3 වැඩ කරන දින</p>
                </div>
              </div>

              <div className="delivery-item">
                <span className="delivery-icon">📞</span>
                <div className="delivery-details">
                  <strong>සම්බන්ධතාව</strong>
                  <p>ගෙන්වා දීමට පෙර අමතනු ලැබේ</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer Blessing */}
      <div className="checkout-footer">
        <div className="divine-blessing">
          <span className="blessing-text">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
