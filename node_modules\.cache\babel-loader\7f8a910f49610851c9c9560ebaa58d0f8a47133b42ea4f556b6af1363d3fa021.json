{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\ZodiacPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport SmokeAnimation from './SmokeAnimation';\nimport KuberaAnimation from './KuberaAnimation';\nimport HoroscopeService from '../services/HoroscopeService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\nconst ZodiacPage = ({\n  sign\n}) => {\n  _s();\n  const [horoscope, setHoroscope] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [soundEnabled, setSoundEnabled] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const fetchHoroscope = async (forceRefresh = false) => {\n    try {\n      if (forceRefresh) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError('');\n\n      // Check cache first (skip cache if force refresh)\n      if (!forceRefresh) {\n        const cachedHoroscope = HoroscopeService.getCachedHoroscope(sign.id);\n        if (cachedHoroscope) {\n          setHoroscope(cachedHoroscope);\n          setLastUpdated(new Date());\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Try Gemini API first, then OpenAI as fallback\n      const horoscopeText = await HoroscopeService.getHoroscope(sign.english, sign.sinhala, forceRefresh);\n      setHoroscope(horoscopeText);\n      setLastUpdated(new Date());\n\n      // Cache the result\n      HoroscopeService.cacheHoroscope(sign.id, horoscopeText);\n    } catch (err) {\n      setError('රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');\n      console.error('Error fetching horoscope:', err);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n  useEffect(() => {\n    fetchHoroscope();\n  }, [sign, fetchHoroscope]);\n  const handleRefresh = () => {\n    fetchHoroscope(true);\n  };\n  const toggleSound = () => {\n    setSoundEnabled(!soundEnabled);\n    // Here you would implement actual sound toggle functionality\n    // For now, we'll just toggle the state\n  };\n  const getCurrentDate = () => {\n    const today = new Date();\n    const options = {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      weekday: 'long'\n    };\n    return today.toLocaleDateString('si-LK', options);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"zodiac-page\",\n    children: [/*#__PURE__*/_jsxDEV(ParticleBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmokeAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(KuberaAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: \"/\",\n      className: \"back-button\",\n      children: \"\\u2190 \\u0DB8\\u0DD4\\u0DBD\\u0DCA \\u0DB4\\u0DD2\\u0DA7\\u0DD4\\u0DC0\\u0DA7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"zodiac-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"zodiac-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"zodiac-icon\",\n          style: {\n            fontSize: '5rem',\n            marginBottom: '1rem'\n          },\n          children: zodiacIcons[sign.id]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"zodiac-title\",\n          children: sign.sinhala\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"zodiac-subtitle\",\n          children: [sign.english, \" \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DBA\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#aeb6bf',\n            marginBottom: '2rem'\n          },\n          children: getCurrentDate()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"horoscope-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            marginBottom: '1.5rem',\n            flexWrap: 'wrap',\n            gap: '1rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"horoscope-title\",\n            style: {\n              margin: 0\n            },\n            children: \"\\u0D85\\u0DAF \\u0DAF\\u0DD2\\u0DB1\\u0DBA\\u0DDA \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            disabled: loading || refreshing,\n            style: {\n              background: refreshing ? 'rgba(244, 208, 63, 0.3)' : 'rgba(244, 208, 63, 0.1)',\n              border: '1px solid #f4d03f',\n              color: '#f4d03f',\n              padding: '0.6rem 1.2rem',\n              borderRadius: '20px',\n              cursor: refreshing ? 'not-allowed' : 'pointer',\n              fontFamily: 'Noto Sans Sinhala, sans-serif',\n              fontSize: '0.9rem',\n              transition: 'all 0.3s ease',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                transform: refreshing ? 'rotate(360deg)' : 'rotate(0deg)',\n                transition: 'transform 1s ease',\n                display: 'inline-block'\n              },\n              children: \"\\uD83D\\uDD04\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), refreshing ? 'නැවුම් කරමින්...' : 'නැවුම් කරන්න']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), lastUpdated && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.85rem',\n            color: '#aeb6bf',\n            marginBottom: '1rem',\n            textAlign: 'center',\n            fontStyle: 'italic'\n          },\n          children: [\"\\u0D85\\u0DC0\\u0DC3\\u0DB1\\u0DCA \\u0DC0\\u0DBB\\u0DA7 \\u0DBA\\u0DCF\\u0DC0\\u0DAD\\u0DCA\\u0D9A\\u0DCF\\u0DBD\\u0DD3\\u0DB1 \\u0D9A\\u0DC5\\u0DDA: \", lastUpdated.toLocaleTimeString('si-LK', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: true\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: \"\\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DB1\\u0DD2\\u0DB8\\u0DD2\\u0DB1\\u0DCA... \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DBB\\u0DD0\\u0DB3\\u0DD3 \\u0DC3\\u0DD2\\u0DA7\\u0DD2\\u0DB1\\u0DCA\\u0DB1.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), refreshing && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"loading\",\n          children: \"\\u0DB1\\u0DC0 \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DB1\\u0DD2\\u0DB8\\u0DD2\\u0DB1\\u0DCA... \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DBB\\u0DD0\\u0DB3\\u0DD3 \\u0DC3\\u0DD2\\u0DA7\\u0DD2\\u0DB1\\u0DCA\\u0DB1.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error\",\n          children: [error, /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleRefresh,\n            style: {\n              marginLeft: '1rem',\n              background: 'rgba(231, 76, 60, 0.1)',\n              border: '1px solid #e74c3c',\n              color: '#e74c3c',\n              padding: '0.4rem 0.8rem',\n              borderRadius: '15px',\n              cursor: 'pointer',\n              fontSize: '0.8rem'\n            },\n            children: \"\\u0DB1\\u0DD0\\u0DC0\\u0DAD \\u0D8B\\u0DAD\\u0DCA\\u0DC3\\u0DCF\\u0DC4 \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), !loading && !refreshing && !error && horoscope && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"horoscope-content\",\n          children: horoscope.split('\\n').map((paragraph, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              marginBottom: '1rem'\n            },\n            children: paragraph\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"controls\",\n        style: {\n          marginTop: '2rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleSound,\n          className: \"sound-toggle\",\n          style: {\n            background: soundEnabled ? 'rgba(244, 208, 63, 0.2)' : 'rgba(255, 255, 255, 0.1)',\n            border: '1px solid #f4d03f',\n            color: '#f4d03f',\n            padding: '0.8rem 1.5rem',\n            borderRadius: '25px',\n            cursor: 'pointer',\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            transition: 'all 0.3s ease'\n          },\n          children: soundEnabled ? '🔊 ශබ්දය නිශ්ශබ්ද කරන්න' : '🔇 දේවාල ශබ්දය සක්‍රිය කරන්න'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spiritual-message\",\n        style: {\n          marginTop: '3rem',\n          padding: '2rem',\n          background: 'rgba(244, 208, 63, 0.1)',\n          borderRadius: '15px',\n          border: '1px solid rgba(244, 208, 63, 0.3)',\n          textAlign: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#f4d03f',\n            fontStyle: 'italic',\n            fontSize: '1.1rem'\n          },\n          children: \"\\\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0D94\\u0DB6 \\u0DC3\\u0DB8\\u0D9F \\u0DC0\\u0DDA\\u0DC0\\u0DCF\\\"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(ZodiacPage, \"UzE/TgxgOoEPeKHQQ9vcLt9ybbE=\");\n_c = ZodiacPage;\nexport default ZodiacPage;\nvar _c;\n$RefreshReg$(_c, \"ZodiacPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "ParticleBackground", "SmokeAnimation", "KuberaAnimation", "HoroscopeService", "jsxDEV", "_jsxDEV", "zodiacIcons", "aries", "taurus", "gemini", "cancer", "leo", "virgo", "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces", "ZodiacPage", "sign", "_s", "horoscope", "setHoroscope", "loading", "setLoading", "error", "setError", "soundEnabled", "setSoundEnabled", "lastUpdated", "setLastUpdated", "refreshing", "setRefreshing", "fetchHoroscope", "forceRefresh", "cachedHoroscope", "getCachedHoroscope", "id", "Date", "horoscopeText", "getHoroscope", "english", "sinhala", "cacheHoroscope", "err", "console", "handleRefresh", "toggleSound", "getCurrentDate", "today", "options", "year", "month", "day", "weekday", "toLocaleDateString", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "style", "fontSize", "marginBottom", "color", "display", "justifyContent", "alignItems", "flexWrap", "gap", "margin", "onClick", "disabled", "background", "border", "padding", "borderRadius", "cursor", "fontFamily", "transition", "transform", "textAlign", "fontStyle", "toLocaleTimeString", "hour", "minute", "hour12", "marginLeft", "split", "map", "paragraph", "index", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport SmokeAnimation from './SmokeAnimation';\nimport KuberaAnimation from './KuberaAnimation';\nimport HoroscopeService from '../services/HoroscopeService';\n\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\nconst ZodiacPage = ({ sign }) => {\n  const [horoscope, setHoroscope] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [soundEnabled, setSoundEnabled] = useState(false);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [refreshing, setRefreshing] = useState(false);\n\n  const fetchHoroscope = async (forceRefresh = false) => {\n    try {\n      if (forceRefresh) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError('');\n      \n      // Check cache first (skip cache if force refresh)\n      if (!forceRefresh) {\n        const cachedHoroscope = HoroscopeService.getCachedHoroscope(sign.id);\n        if (cachedHoroscope) {\n          setHoroscope(cachedHoroscope);\n          setLastUpdated(new Date());\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Try Gemini API first, then OpenAI as fallback\n      const horoscopeText = await HoroscopeService.getHoroscope(sign.english, sign.sinhala, forceRefresh);\n      setHoroscope(horoscopeText);\n      setLastUpdated(new Date());\n      \n      // Cache the result\n      HoroscopeService.cacheHoroscope(sign.id, horoscopeText);\n      \n    } catch (err) {\n      setError('රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');\n      console.error('Error fetching horoscope:', err);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchHoroscope();\n  }, [sign, fetchHoroscope]);\n\n  const handleRefresh = () => {\n    fetchHoroscope(true);\n  };\n\n  const toggleSound = () => {\n    setSoundEnabled(!soundEnabled);\n    // Here you would implement actual sound toggle functionality\n    // For now, we'll just toggle the state\n  };\n\n  const getCurrentDate = () => {\n    const today = new Date();\n    const options = { \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric',\n      weekday: 'long'\n    };\n    return today.toLocaleDateString('si-LK', options);\n  };\n\n  return (\n    <div className=\"zodiac-page\">\n      <ParticleBackground />\n      <SmokeAnimation />\n      <KuberaAnimation />\n      \n      <Link to=\"/\" className=\"back-button\">\n        ← මුල් පිටුවට\n      </Link>\n\n      <div className=\"zodiac-content\">\n        <div className=\"zodiac-header\">\n          <div className=\"zodiac-icon\" style={{ fontSize: '5rem', marginBottom: '1rem' }}>\n            {zodiacIcons[sign.id]}\n          </div>\n          <h1 className=\"zodiac-title\">{sign.sinhala}</h1>\n          <h2 className=\"zodiac-subtitle\">{sign.english} රාශිය</h2>\n          <p style={{ color: '#aeb6bf', marginBottom: '2rem' }}>\n            {getCurrentDate()}\n          </p>\n        </div>\n\n        <div className=\"horoscope-section\">\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1.5rem', flexWrap: 'wrap', gap: '1rem' }}>\n            <h3 className=\"horoscope-title\" style={{ margin: 0 }}>අද දිනයේ රාශිඵල</h3>\n            <button \n              onClick={handleRefresh}\n              disabled={loading || refreshing}\n              style={{\n                background: refreshing ? 'rgba(244, 208, 63, 0.3)' : 'rgba(244, 208, 63, 0.1)',\n                border: '1px solid #f4d03f',\n                color: '#f4d03f',\n                padding: '0.6rem 1.2rem',\n                borderRadius: '20px',\n                cursor: refreshing ? 'not-allowed' : 'pointer',\n                fontFamily: 'Noto Sans Sinhala, sans-serif',\n                fontSize: '0.9rem',\n                transition: 'all 0.3s ease',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}\n            >\n              <span style={{ transform: refreshing ? 'rotate(360deg)' : 'rotate(0deg)', transition: 'transform 1s ease', display: 'inline-block' }}>🔄</span>\n              {refreshing ? 'නැවුම් කරමින්...' : 'නැවුම් කරන්න'}\n            </button>\n          </div>\n          \n          {lastUpdated && (\n            <div style={{ \n              fontSize: '0.85rem', \n              color: '#aeb6bf', \n              marginBottom: '1rem',\n              textAlign: 'center',\n              fontStyle: 'italic'\n            }}>\n              අවසන් වරට යාවත්කාලීන කළේ: {lastUpdated.toLocaleTimeString('si-LK', { \n                hour: '2-digit', \n                minute: '2-digit',\n                hour12: true\n              })}\n            </div>\n          )}\n          \n          {loading && (\n            <div className=\"loading\">\n              රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.\n            </div>\n          )}\n          \n          {refreshing && (\n            <div className=\"loading\">\n              නව රාශිඵල ලබා ගනිමින්... කරුණාකර රැඳී සිටින්න.\n            </div>\n          )}\n          \n          {error && (\n            <div className=\"error\">\n              {error}\n              <button \n                onClick={handleRefresh}\n                style={{\n                  marginLeft: '1rem',\n                  background: 'rgba(231, 76, 60, 0.1)',\n                  border: '1px solid #e74c3c',\n                  color: '#e74c3c',\n                  padding: '0.4rem 0.8rem',\n                  borderRadius: '15px',\n                  cursor: 'pointer',\n                  fontSize: '0.8rem'\n                }}\n              >\n                නැවත උත්සාහ කරන්න\n              </button>\n            </div>\n          )}\n          \n          {!loading && !refreshing && !error && horoscope && (\n            <div className=\"horoscope-content\">\n              {horoscope.split('\\n').map((paragraph, index) => (\n                <p key={index} style={{ marginBottom: '1rem' }}>\n                  {paragraph}\n                </p>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"controls\" style={{ marginTop: '2rem' }}>\n          <button \n            onClick={toggleSound}\n            className=\"sound-toggle\"\n            style={{\n              background: soundEnabled ? 'rgba(244, 208, 63, 0.2)' : 'rgba(255, 255, 255, 0.1)',\n              border: '1px solid #f4d03f',\n              color: '#f4d03f',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '25px',\n              cursor: 'pointer',\n              fontFamily: 'Noto Sans Sinhala, sans-serif',\n              transition: 'all 0.3s ease'\n            }}\n          >\n            {soundEnabled ? '🔊 ශබ්දය නිශ්ශබ්ද කරන්න' : '🔇 දේවාල ශබ්දය සක්‍රිය කරන්න'}\n          </button>\n        </div>\n\n        <div className=\"spiritual-message\" style={{\n          marginTop: '3rem',\n          padding: '2rem',\n          background: 'rgba(244, 208, 63, 0.1)',\n          borderRadius: '15px',\n          border: '1px solid rgba(244, 208, 63, 0.3)',\n          textAlign: 'center'\n        }}>\n          <p style={{ color: '#f4d03f', fontStyle: 'italic', fontSize: '1.1rem' }}>\n            \"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා\"\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ZodiacPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,gBAAgB,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,GAAG,EAAE,GAAG;EACRC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,GAAG;EACZC,WAAW,EAAE,GAAG;EAChBC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,UAAU,GAAGA,CAAC;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EAC/B,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMqC,cAAc,GAAG,MAAAA,CAAOC,YAAY,GAAG,KAAK,KAAK;IACrD,IAAI;MACF,IAAIA,YAAY,EAAE;QAChBF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACLR,UAAU,CAAC,IAAI,CAAC;MAClB;MACAE,QAAQ,CAAC,EAAE,CAAC;;MAEZ;MACA,IAAI,CAACQ,YAAY,EAAE;QACjB,MAAMC,eAAe,GAAGjC,gBAAgB,CAACkC,kBAAkB,CAACjB,IAAI,CAACkB,EAAE,CAAC;QACpE,IAAIF,eAAe,EAAE;UACnBb,YAAY,CAACa,eAAe,CAAC;UAC7BL,cAAc,CAAC,IAAIQ,IAAI,CAAC,CAAC,CAAC;UAC1Bd,UAAU,CAAC,KAAK,CAAC;UACjB;QACF;MACF;;MAEA;MACA,MAAMe,aAAa,GAAG,MAAMrC,gBAAgB,CAACsC,YAAY,CAACrB,IAAI,CAACsB,OAAO,EAAEtB,IAAI,CAACuB,OAAO,EAAER,YAAY,CAAC;MACnGZ,YAAY,CAACiB,aAAa,CAAC;MAC3BT,cAAc,CAAC,IAAIQ,IAAI,CAAC,CAAC,CAAC;;MAE1B;MACApC,gBAAgB,CAACyC,cAAc,CAACxB,IAAI,CAACkB,EAAE,EAAEE,aAAa,CAAC;IAEzD,CAAC,CAAC,OAAOK,GAAG,EAAE;MACZlB,QAAQ,CAAC,gEAAgE,CAAC;MAC1EmB,OAAO,CAACpB,KAAK,CAAC,2BAA2B,EAAEmB,GAAG,CAAC;IACjD,CAAC,SAAS;MACRpB,UAAU,CAAC,KAAK,CAAC;MACjBQ,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAEDnC,SAAS,CAAC,MAAM;IACdoC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACd,IAAI,EAAEc,cAAc,CAAC,CAAC;EAE1B,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1Bb,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMc,WAAW,GAAGA,CAAA,KAAM;IACxBnB,eAAe,CAAC,CAACD,YAAY,CAAC;IAC9B;IACA;EACF,CAAC;EAED,MAAMqB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,KAAK,GAAG,IAAIX,IAAI,CAAC,CAAC;IACxB,MAAMY,OAAO,GAAG;MACdC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,OAAO,EAAE;IACX,CAAC;IACD,OAAOL,KAAK,CAACM,kBAAkB,CAAC,OAAO,EAAEL,OAAO,CAAC;EACnD,CAAC;EAED,oBACE9C,OAAA;IAAKoD,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BrD,OAAA,CAACL,kBAAkB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBzD,OAAA,CAACJ,cAAc;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBzD,OAAA,CAACH,eAAe;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEnBzD,OAAA,CAACN,IAAI;MAACgE,EAAE,EAAC,GAAG;MAACN,SAAS,EAAC,aAAa;MAAAC,QAAA,EAAC;IAErC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEPzD,OAAA;MAAKoD,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrD,OAAA;QAAKoD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrD,OAAA;UAAKoD,SAAS,EAAC,aAAa;UAACO,KAAK,EAAE;YAAEC,QAAQ,EAAE,MAAM;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,EAC5EpD,WAAW,CAACc,IAAI,CAACkB,EAAE;QAAC;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC,eACNzD,OAAA;UAAIoD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAEtC,IAAI,CAACuB;QAAO;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAChDzD,OAAA;UAAIoD,SAAS,EAAC,iBAAiB;UAAAC,QAAA,GAAEtC,IAAI,CAACsB,OAAO,EAAC,iCAAM;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDzD,OAAA;UAAG2D,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAED,YAAY,EAAE;UAAO,CAAE;UAAAR,QAAA,EAClDT,cAAc,CAAC;QAAC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCrD,OAAA;UAAK2D,KAAK,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEC,UAAU,EAAE,QAAQ;YAAEJ,YAAY,EAAE,QAAQ;YAAEK,QAAQ,EAAE,MAAM;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAd,QAAA,gBAC5IrD,OAAA;YAAIoD,SAAS,EAAC,iBAAiB;YAACO,KAAK,EAAE;cAAES,MAAM,EAAE;YAAE,CAAE;YAAAf,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1EzD,OAAA;YACEqE,OAAO,EAAE3B,aAAc;YACvB4B,QAAQ,EAAEnD,OAAO,IAAIQ,UAAW;YAChCgC,KAAK,EAAE;cACLY,UAAU,EAAE5C,UAAU,GAAG,yBAAyB,GAAG,yBAAyB;cAC9E6C,MAAM,EAAE,mBAAmB;cAC3BV,KAAK,EAAE,SAAS;cAChBW,OAAO,EAAE,eAAe;cACxBC,YAAY,EAAE,MAAM;cACpBC,MAAM,EAAEhD,UAAU,GAAG,aAAa,GAAG,SAAS;cAC9CiD,UAAU,EAAE,+BAA+B;cAC3ChB,QAAQ,EAAE,QAAQ;cAClBiB,UAAU,EAAE,eAAe;cAC3Bd,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBE,GAAG,EAAE;YACP,CAAE;YAAAd,QAAA,gBAEFrD,OAAA;cAAM2D,KAAK,EAAE;gBAAEmB,SAAS,EAAEnD,UAAU,GAAG,gBAAgB,GAAG,cAAc;gBAAEkD,UAAU,EAAE,mBAAmB;gBAAEd,OAAO,EAAE;cAAe,CAAE;cAAAV,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EAC9I9B,UAAU,GAAG,kBAAkB,GAAG,cAAc;UAAA;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELhC,WAAW,iBACVzB,OAAA;UAAK2D,KAAK,EAAE;YACVC,QAAQ,EAAE,SAAS;YACnBE,KAAK,EAAE,SAAS;YAChBD,YAAY,EAAE,MAAM;YACpBkB,SAAS,EAAE,QAAQ;YACnBC,SAAS,EAAE;UACb,CAAE;UAAA3B,QAAA,GAAC,qIACyB,EAAC5B,WAAW,CAACwD,kBAAkB,CAAC,OAAO,EAAE;YACjEC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,SAAS;YACjBC,MAAM,EAAE;UACV,CAAC,CAAC;QAAA;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAtC,OAAO,iBACNnB,OAAA;UAAKoD,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAEzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EAEA9B,UAAU,iBACT3B,OAAA;UAAKoD,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAEzB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EAEApC,KAAK,iBACJrB,OAAA;UAAKoD,SAAS,EAAC,OAAO;UAAAC,QAAA,GACnBhC,KAAK,eACNrB,OAAA;YACEqE,OAAO,EAAE3B,aAAc;YACvBiB,KAAK,EAAE;cACL0B,UAAU,EAAE,MAAM;cAClBd,UAAU,EAAE,wBAAwB;cACpCC,MAAM,EAAE,mBAAmB;cAC3BV,KAAK,EAAE,SAAS;cAChBW,OAAO,EAAE,eAAe;cACxBC,YAAY,EAAE,MAAM;cACpBC,MAAM,EAAE,SAAS;cACjBf,QAAQ,EAAE;YACZ,CAAE;YAAAP,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEA,CAACtC,OAAO,IAAI,CAACQ,UAAU,IAAI,CAACN,KAAK,IAAIJ,SAAS,iBAC7CjB,OAAA;UAAKoD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/BpC,SAAS,CAACqE,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBAC1CzF,OAAA;YAAe2D,KAAK,EAAE;cAAEE,YAAY,EAAE;YAAO,CAAE;YAAAR,QAAA,EAC5CmC;UAAS,GADJC,KAAK;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,UAAU;QAACO,KAAK,EAAE;UAAE+B,SAAS,EAAE;QAAO,CAAE;QAAArC,QAAA,eACrDrD,OAAA;UACEqE,OAAO,EAAE1B,WAAY;UACrBS,SAAS,EAAC,cAAc;UACxBO,KAAK,EAAE;YACLY,UAAU,EAAEhD,YAAY,GAAG,yBAAyB,GAAG,0BAA0B;YACjFiD,MAAM,EAAE,mBAAmB;YAC3BV,KAAK,EAAE,SAAS;YAChBW,OAAO,EAAE,eAAe;YACxBC,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE,SAAS;YACjBC,UAAU,EAAE,+BAA+B;YAC3CC,UAAU,EAAE;UACd,CAAE;UAAAxB,QAAA,EAED9B,YAAY,GAAG,yBAAyB,GAAG;QAA8B;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENzD,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAACO,KAAK,EAAE;UACxC+B,SAAS,EAAE,MAAM;UACjBjB,OAAO,EAAE,MAAM;UACfF,UAAU,EAAE,yBAAyB;UACrCG,YAAY,EAAE,MAAM;UACpBF,MAAM,EAAE,mCAAmC;UAC3CO,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,eACArD,OAAA;UAAG2D,KAAK,EAAE;YAAEG,KAAK,EAAE,SAAS;YAAEkB,SAAS,EAAE,QAAQ;YAAEpB,QAAQ,EAAE;UAAS,CAAE;UAAAP,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzC,EAAA,CApNIF,UAAU;AAAA6E,EAAA,GAAV7E,UAAU;AAsNhB,eAAeA,UAAU;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}