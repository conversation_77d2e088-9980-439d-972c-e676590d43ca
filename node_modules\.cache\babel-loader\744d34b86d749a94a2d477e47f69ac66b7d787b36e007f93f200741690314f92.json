{"ast": null, "code": "import axios from 'axios';\nclass HoroscopeService {\n  constructor() {\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Try Gemini API first with enhanced real-time functionality\n  async getHoroscopeFromGemini(signEnglish, signSinhala) {\n    try {\n      var _response$data, _response$data$candid, _response$data$candid2, _response$data$candid3, _response$data$candid4, _response$data$candid5;\n      // Note: Replace with your actual Gemini API key\n      const GEMINI_API_KEY = process.env.REACT_APP_GEMINI_API_KEY;\n      if (!GEMINI_API_KEY) {\n        throw new Error('Gemini API key not found');\n      }\n      const today = new Date();\n      const dateString = today.toLocaleDateString('si-LK', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        weekday: 'long'\n      });\n      const prompt = `Generate a detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language for today ${dateString}. Include specific predictions about:\n      \n      1. ආදරය සහ සම්බන්ධතා (Love and Relationships)\n      2. වෘත්තීය ජීවිතය (Career and Work)\n      3. සෞඛ්‍ය සහ යහපැවැත්ම (Health and Wellness)\n      4. මූල්‍ය කටයුතු (Financial matters)\n      5. සාමාන්‍ය උපදෙස් (General advice)\n      \n      Make it spiritual, positive, and culturally relevant to Sri Lankan context. Write in beautiful Sinhala script with proper formatting. Include specific guidance for today's date and make it feel personalized and authentic.`;\n      const response = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GEMINI_API_KEY}`, {\n        contents: [{\n          parts: [{\n            text: prompt\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.8,\n          topK: 40,\n          topP: 0.95,\n          maxOutputTokens: 1024\n        }\n      }, {\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 10000 // 10 second timeout\n      });\n      if ((_response$data = response.data) !== null && _response$data !== void 0 && (_response$data$candid = _response$data.candidates) !== null && _response$data$candid !== void 0 && (_response$data$candid2 = _response$data$candid[0]) !== null && _response$data$candid2 !== void 0 && (_response$data$candid3 = _response$data$candid2.content) !== null && _response$data$candid3 !== void 0 && (_response$data$candid4 = _response$data$candid3.parts) !== null && _response$data$candid4 !== void 0 && (_response$data$candid5 = _response$data$candid4[0]) !== null && _response$data$candid5 !== void 0 && _response$data$candid5.text) {\n        return response.data.candidates[0].content.parts[0].text;\n      } else {\n        throw new Error('Invalid response from Gemini API');\n      }\n    } catch (error) {\n      console.error('Gemini API error:', error);\n      throw error;\n    }\n  }\n\n  // OpenAI API as fallback\n  async getHoroscopeFromOpenAI(signEnglish, signSinhala) {\n    try {\n      var _response$data2, _response$data2$choic, _response$data2$choic2, _response$data2$choic3;\n      // Note: Replace with your actual OpenAI API key\n      const OPENAI_API_KEY = process.env.REACT_APP_OPENAI_API_KEY;\n      if (!OPENAI_API_KEY) {\n        throw new Error('OpenAI API key not found');\n      }\n      const prompt = `Generate a detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language. Include predictions about love, career, health, and general advice. Make it spiritual and positive. Write in beautiful Sinhala script. Today's date is ${new Date().toLocaleDateString()}.`;\n      const response = await axios.post('https://api.openai.com/v1/chat/completions', {\n        model: 'gpt-3.5-turbo',\n        messages: [{\n          role: 'system',\n          content: 'You are a wise Sinhala astrologer who provides spiritual guidance and daily horoscopes in beautiful Sinhala language.'\n        }, {\n          role: 'user',\n          content: prompt\n        }],\n        max_tokens: 500,\n        temperature: 0.7\n      }, {\n        headers: {\n          'Authorization': `Bearer ${OPENAI_API_KEY}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      if ((_response$data2 = response.data) !== null && _response$data2 !== void 0 && (_response$data2$choic = _response$data2.choices) !== null && _response$data2$choic !== void 0 && (_response$data2$choic2 = _response$data2$choic[0]) !== null && _response$data2$choic2 !== void 0 && (_response$data2$choic3 = _response$data2$choic2.message) !== null && _response$data2$choic3 !== void 0 && _response$data2$choic3.content) {\n        return response.data.choices[0].message.content;\n      } else {\n        throw new Error('Invalid response from OpenAI API');\n      }\n    } catch (error) {\n      console.error('OpenAI API error:', error);\n      throw error;\n    }\n  }\n\n  // Generate fallback horoscope if APIs fail\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': 'අද දිනය ඔබට ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ආදරය සහ කාර්යක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n      'වෘෂභ': 'ස්ථාවරත්වය සහ ඉවසීම අදට ඔබේ ප්‍රධාන ගුණාංග වනු ඇත. මූල්‍ය කටයුතුවල සැලකිලිමත් වන්න. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න.',\n      'මිථුන': 'සන්නිවේදනය සහ නව අදහස් අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සමාජ සම්බන්ධතා ශක්තිමත් කරන්න. ගමන් සහ ඉගෙනීම සඳහා හොඳ දිනයකි.',\n      'කටක': 'පවුල සහ නිවස අද ඔබේ අවධානයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වන්න. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n      'සිංහ': 'නායකත්වය සහ විශ්වාසය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. කලාත්මක ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. ආදරයේ ක්ෂේත්‍රයේ ධනාත්මක ප්‍රගතියක් අපේක්ෂා කරන්න.',\n      'කන්‍යා': 'විස්තර සහ සංවිධානය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සෞඛ්‍ය සහ සේවා කටයුතුවලට අවධානය යොමු කරන්න. කුඩා ප්‍රගතියන් ප්‍රශංසා කරන්න.',\n      'තුලා': 'සමතුලිතතාවය සහ සාධාරණත්වය අද ඔබේ මග පෙන්වනු ඇත. සම්බන්ධතා සහ හවුල්කාරිත්වයන් ශක්තිමත් කරන්න. සෞන්දර්යය සහ කලාව අගය කරන්න.',\n      'වෘශ්චික': 'ගැඹුරු අවබෝධය සහ පරිවර්තනය අද ඔබේ ප්‍රධාන තේමාවන් වනු ඇත. අභ්‍යන්තර ශක්තිය සොයා ගන්න. රහස්‍ය සහ ගවේෂණ සඳහා සුදුසු කාලයකි.',\n      'ධනු': 'ඉගෙනීම සහ ගවේෂණය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. දර්ශනය සහ ආධ්‍යාත්මිකත්වය කෙරෙහි අවධානය යොමු කරන්න. දුර ගමන් සඳහා හොඳ දිනයකි.',\n      'මකර': 'වගකීම සහ අභිලාෂය අද ඔබේ මග පෙන්වනු ඇත. දිගුකාලීන ඉලක්ක සඳහා වැඩ කරන්න. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',\n      'කුම්භ': 'නවෝත්පාදනය සහ මිත්‍රත්වය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. අනාගතය සඳහා සැලසුම් කරන්න.',\n      'මීන': 'අන්තර්ज්ඤානය සහ කරුණාව අද ඔබේ මග පෙන්වනු ඇත. ආධ්‍යාත්මික ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. කලාත්මක ප්‍රකාශනය සඳහා සුදුසු කාලයකි.'\n    };\n    return fallbackHoroscopes[signSinhala] || 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.';\n  }\n\n  // Clear cache for a specific sign (useful for refresh)\n  clearCache(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.delete(cacheKey);\n  }\n\n  // Clear all cache\n  clearAllCache() {\n    this.cache.clear();\n  }\n\n  // Main method to get horoscope\n  async getHoroscope(signEnglish, signSinhala, forceRefresh = false) {\n    try {\n      // Clear cache if force refresh is requested\n      if (forceRefresh) {\n        this.clearCache(signEnglish.toLowerCase());\n      }\n\n      // Try Gemini API first\n      return await this.getHoroscopeFromGemini(signEnglish, signSinhala);\n    } catch (geminiError) {\n      console.log('Gemini API failed, trying OpenAI...');\n      try {\n        // Try OpenAI API as fallback\n        return await this.getHoroscopeFromOpenAI(signEnglish, signSinhala);\n      } catch (openaiError) {\n        console.log('Both APIs failed, using fallback horoscope...');\n\n        // Use fallback horoscope if both APIs fail\n        return this.generateFallbackHoroscope(signSinhala);\n      }\n    }\n  }\n}\nexport default new HoroscopeService();", "map": {"version": 3, "names": ["axios", "HoroscopeService", "constructor", "cache", "Map", "cacheExpiry", "getCachedHoroscope", "signId", "today", "Date", "toDateString", "cache<PERSON>ey", "has", "cached", "get", "now", "timestamp", "data", "delete", "cacheHoroscope", "horoscope", "set", "getHoroscopeFromGemini", "signEnglish", "signSinhala", "_response$data", "_response$data$candid", "_response$data$candid2", "_response$data$candid3", "_response$data$candid4", "_response$data$candid5", "GEMINI_API_KEY", "process", "env", "REACT_APP_GEMINI_API_KEY", "Error", "dateString", "toLocaleDateString", "year", "month", "day", "weekday", "prompt", "response", "post", "contents", "parts", "text", "generationConfig", "temperature", "topK", "topP", "maxOutputTokens", "headers", "timeout", "candidates", "content", "error", "console", "getHoroscopeFromOpenAI", "_response$data2", "_response$data2$choic", "_response$data2$choic2", "_response$data2$choic3", "OPENAI_API_KEY", "REACT_APP_OPENAI_API_KEY", "model", "messages", "role", "max_tokens", "choices", "message", "generateFallbackHoroscope", "fallbackHoroscopes", "clearCache", "clearAllCache", "clear", "getHoroscope", "forceRefresh", "toLowerCase", "geminiError", "log", "openaiError"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass HoroscopeService {\n  constructor() {\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    \n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Try Gemini API first with enhanced real-time functionality\n  async getHoroscopeFromGemini(signEnglish, signSinhala) {\n    try {\n      // Note: Replace with your actual Gemini API key\n      const GEMINI_API_KEY = process.env.REACT_APP_GEMINI_API_KEY;\n      \n      if (!GEMINI_API_KEY) {\n        throw new Error('Gemini API key not found');\n      }\n\n      const today = new Date();\n      const dateString = today.toLocaleDateString('si-LK', { \n        year: 'numeric', \n        month: 'long', \n        day: 'numeric',\n        weekday: 'long'\n      });\n\n      const prompt = `Generate a detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language for today ${dateString}. Include specific predictions about:\n      \n      1. ආදරය සහ සම්බන්ධතා (Love and Relationships)\n      2. වෘත්තීය ජීවිතය (Career and Work)\n      3. සෞඛ්‍ය සහ යහපැවැත්ම (Health and Wellness)\n      4. මූල්‍ය කටයුතු (Financial matters)\n      5. සාමාන්‍ය උපදෙස් (General advice)\n      \n      Make it spiritual, positive, and culturally relevant to Sri Lankan context. Write in beautiful Sinhala script with proper formatting. Include specific guidance for today's date and make it feel personalized and authentic.`;\n\n      const response = await axios.post(\n        `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${GEMINI_API_KEY}`,\n        {\n          contents: [{\n            parts: [{\n              text: prompt\n            }]\n          }],\n          generationConfig: {\n            temperature: 0.8,\n            topK: 40,\n            topP: 0.95,\n            maxOutputTokens: 1024\n          }\n        },\n        {\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          timeout: 10000 // 10 second timeout\n        }\n      );\n\n      if (response.data?.candidates?.[0]?.content?.parts?.[0]?.text) {\n        return response.data.candidates[0].content.parts[0].text;\n      } else {\n        throw new Error('Invalid response from Gemini API');\n      }\n    } catch (error) {\n      console.error('Gemini API error:', error);\n      throw error;\n    }\n  }\n\n  // OpenAI API as fallback\n  async getHoroscopeFromOpenAI(signEnglish, signSinhala) {\n    try {\n      // Note: Replace with your actual OpenAI API key\n      const OPENAI_API_KEY = process.env.REACT_APP_OPENAI_API_KEY;\n      \n      if (!OPENAI_API_KEY) {\n        throw new Error('OpenAI API key not found');\n      }\n\n      const prompt = `Generate a detailed daily horoscope for ${signEnglish} (${signSinhala}) zodiac sign in Sinhala language. Include predictions about love, career, health, and general advice. Make it spiritual and positive. Write in beautiful Sinhala script. Today's date is ${new Date().toLocaleDateString()}.`;\n\n      const response = await axios.post(\n        'https://api.openai.com/v1/chat/completions',\n        {\n          model: 'gpt-3.5-turbo',\n          messages: [\n            {\n              role: 'system',\n              content: 'You are a wise Sinhala astrologer who provides spiritual guidance and daily horoscopes in beautiful Sinhala language.'\n            },\n            {\n              role: 'user',\n              content: prompt\n            }\n          ],\n          max_tokens: 500,\n          temperature: 0.7\n        },\n        {\n          headers: {\n            'Authorization': `Bearer ${OPENAI_API_KEY}`,\n            'Content-Type': 'application/json'\n          }\n        }\n      );\n\n      if (response.data?.choices?.[0]?.message?.content) {\n        return response.data.choices[0].message.content;\n      } else {\n        throw new Error('Invalid response from OpenAI API');\n      }\n    } catch (error) {\n      console.error('OpenAI API error:', error);\n      throw error;\n    }\n  }\n\n  // Generate fallback horoscope if APIs fail\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': 'අද දිනය ඔබට ශක්තිමත් දිනයක් වනු ඇත. නව ආරම්භයන් සඳහා සුදුසු කාලයකි. ආදරය සහ කාර්යක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n      'වෘෂභ': 'ස්ථාවරත්වය සහ ඉවසීම අදට ඔබේ ප්‍රධාන ගුණාංග වනු ඇත. මූල්‍ය කටයුතුවල සැලකිලිමත් වන්න. පවුලේ සාමාජිකයන් සමඟ ගුණාත්මක කාලය ගත කරන්න.',\n      'මිථුන': 'සන්නිවේදනය සහ නව අදහස් අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සමාජ සම්බන්ධතා ශක්තිමත් කරන්න. ගමන් සහ ඉගෙනීම සඳහා හොඳ දිනයකි.',\n      'කටක': 'පවුල සහ නිවස අද ඔබේ අවධානයේ කේන්ද්‍රස්ථානය වනු ඇත. හැඟීම්වලට සැලකිලිමත් වන්න. නිර්මාණශීලී ක්‍රියාකාරකම් සඳහා සුදුසු කාලයකි.',\n      'සිංහ': 'නායකත්වය සහ විශ්වාසය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. කලාත්මක ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. ආදරයේ ක්ෂේත්‍රයේ ධනාත්මක ප්‍රගතියක් අපේක්ෂා කරන්න.',\n      'කන්‍යා': 'විස්තර සහ සංවිධානය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. සෞඛ්‍ය සහ සේවා කටයුතුවලට අවධානය යොමු කරන්න. කුඩා ප්‍රගතියන් ප්‍රශංසා කරන්න.',\n      'තුලා': 'සමතුලිතතාවය සහ සාධාරණත්වය අද ඔබේ මග පෙන්වනු ඇත. සම්බන්ධතා සහ හවුල්කාරිත්වයන් ශක්තිමත් කරන්න. සෞන්දර්යය සහ කලාව අගය කරන්න.',\n      'වෘශ්චික': 'ගැඹුරු අවබෝධය සහ පරිවර්තනය අද ඔබේ ප්‍රධාන තේමාවන් වනු ඇත. අභ්‍යන්තර ශක්තිය සොයා ගන්න. රහස්‍ය සහ ගවේෂණ සඳහා සුදුසු කාලයකි.',\n      'ධනු': 'ඉගෙනීම සහ ගවේෂණය අද ඔබට ප්‍රයෝජනවත් වනු ඇත. දර්ශනය සහ ආධ්‍යාත්මිකත්වය කෙරෙහි අවධානය යොමු කරන්න. දුර ගමන් සඳහා හොඳ දිනයකි.',\n      'මකර': 'වගකීම සහ අභිලාෂය අද ඔබේ මග පෙන්වනු ඇත. දිගුකාලීන ඉලක්ක සඳහා වැඩ කරන්න. සාම්ප්‍රදායික ක්‍රම අගය කරන්න.',\n      'කුම්භ': 'නවෝත්පාදනය සහ මිත්‍රත්වය අද ඔබේ ප්‍රධාන ශක්තීන් වනු ඇත. සමූහ ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. අනාගතය සඳහා සැලසුම් කරන්න.',\n      'මීන': 'අන්තර්ज්ඤානය සහ කරුණාව අද ඔබේ මග පෙන්වනු ඇත. ආධ්‍යාත්මික ක්‍රියාකාරකම්වල සම්බන්ධ වන්න. කලාත්මක ප්‍රකාශනය සඳහා සුදුසු කාලයකි.'\n    };\n\n    return fallbackHoroscopes[signSinhala] || 'අද දිනය ඔබට ශුභ දිනයක් වනු ඇත. ධනාත්මක සිතුවිලි තබා ගන්න.';\n  }\n\n  // Clear cache for a specific sign (useful for refresh)\n  clearCache(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    this.cache.delete(cacheKey);\n  }\n\n  // Clear all cache\n  clearAllCache() {\n    this.cache.clear();\n  }\n\n  // Main method to get horoscope\n  async getHoroscope(signEnglish, signSinhala, forceRefresh = false) {\n    try {\n      // Clear cache if force refresh is requested\n      if (forceRefresh) {\n        this.clearCache(signEnglish.toLowerCase());\n      }\n      \n      // Try Gemini API first\n      return await this.getHoroscopeFromGemini(signEnglish, signSinhala);\n    } catch (geminiError) {\n      console.log('Gemini API failed, trying OpenAI...');\n      \n      try {\n        // Try OpenAI API as fallback\n        return await this.getHoroscopeFromOpenAI(signEnglish, signSinhala);\n      } catch (openaiError) {\n        console.log('Both APIs failed, using fallback horoscope...');\n        \n        // Use fallback horoscope if both APIs fail\n        return this.generateFallbackHoroscope(signSinhala);\n      }\n    }\n  }\n}\n\nexport default new HoroscopeService();"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,gBAAgB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EAC1C;;EAEA;EACAC,kBAAkBA,CAACC,MAAM,EAAE;IACzB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,IAAI,CAACL,KAAK,CAACS,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC5B,MAAME,MAAM,GAAG,IAAI,CAACV,KAAK,CAACW,GAAG,CAACH,QAAQ,CAAC;MACvC,IAAIF,IAAI,CAACM,GAAG,CAAC,CAAC,GAAGF,MAAM,CAACG,SAAS,GAAG,IAAI,CAACX,WAAW,EAAE;QACpD,OAAOQ,MAAM,CAACI,IAAI;MACpB,CAAC,MAAM;QACL,IAAI,CAACd,KAAK,CAACe,MAAM,CAACP,QAAQ,CAAC;MAC7B;IACF;IAEA,OAAO,IAAI;EACb;;EAEA;EACAQ,cAAcA,CAACZ,MAAM,EAAEa,SAAS,EAAE;IAChC,MAAMZ,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IAErC,IAAI,CAACL,KAAK,CAACkB,GAAG,CAACV,QAAQ,EAAE;MACvBM,IAAI,EAAEG,SAAS;MACfJ,SAAS,EAAEP,IAAI,CAACM,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ;;EAEA;EACA,MAAMO,sBAAsBA,CAACC,WAAW,EAAEC,WAAW,EAAE;IACrD,IAAI;MAAA,IAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,cAAc,GAAGC,OAAO,CAACC,GAAG,CAACC,wBAAwB;MAE3D,IAAI,CAACH,cAAc,EAAE;QACnB,MAAM,IAAII,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAM3B,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAM2B,UAAU,GAAG5B,KAAK,CAAC6B,kBAAkB,CAAC,OAAO,EAAE;QACnDC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,MAAM;QACbC,GAAG,EAAE,SAAS;QACdC,OAAO,EAAE;MACX,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAG,2CAA2CnB,WAAW,KAAKC,WAAW,+CAA+CY,UAAU;AACpJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oOAAoO;MAE9N,MAAMO,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,IAAI,CAC/B,0FAA0Fb,cAAc,EAAE,EAC1G;QACEc,QAAQ,EAAE,CAAC;UACTC,KAAK,EAAE,CAAC;YACNC,IAAI,EAAEL;UACR,CAAC;QACH,CAAC,CAAC;QACFM,gBAAgB,EAAE;UAChBC,WAAW,EAAE,GAAG;UAChBC,IAAI,EAAE,EAAE;UACRC,IAAI,EAAE,IAAI;UACVC,eAAe,EAAE;QACnB;MACF,CAAC,EACD;QACEC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,OAAO,EAAE,KAAK,CAAC;MACjB,CACF,CAAC;MAED,KAAA7B,cAAA,GAAIkB,QAAQ,CAAC1B,IAAI,cAAAQ,cAAA,gBAAAC,qBAAA,GAAbD,cAAA,CAAe8B,UAAU,cAAA7B,qBAAA,gBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,gBAAAC,sBAAA,GAA9BD,sBAAA,CAAgC6B,OAAO,cAAA5B,sBAAA,gBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCkB,KAAK,cAAAjB,sBAAA,gBAAAC,sBAAA,GAA9CD,sBAAA,CAAiD,CAAC,CAAC,cAAAC,sBAAA,eAAnDA,sBAAA,CAAqDiB,IAAI,EAAE;QAC7D,OAAOJ,QAAQ,CAAC1B,IAAI,CAACsC,UAAU,CAAC,CAAC,CAAC,CAACC,OAAO,CAACV,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI;MAC1D,CAAC,MAAM;QACL,MAAM,IAAIZ,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK;IACb;EACF;;EAEA;EACA,MAAME,sBAAsBA,CAACpC,WAAW,EAAEC,WAAW,EAAE;IACrD,IAAI;MAAA,IAAAoC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,cAAc,GAAGhC,OAAO,CAACC,GAAG,CAACgC,wBAAwB;MAE3D,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAI7B,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MAEA,MAAMO,MAAM,GAAG,2CAA2CnB,WAAW,KAAKC,WAAW,6LAA6L,IAAIf,IAAI,CAAC,CAAC,CAAC4B,kBAAkB,CAAC,CAAC,GAAG;MAEpT,MAAMM,QAAQ,GAAG,MAAM3C,KAAK,CAAC4C,IAAI,CAC/B,4CAA4C,EAC5C;QACEsB,KAAK,EAAE,eAAe;QACtBC,QAAQ,EAAE,CACR;UACEC,IAAI,EAAE,QAAQ;UACdZ,OAAO,EAAE;QACX,CAAC,EACD;UACEY,IAAI,EAAE,MAAM;UACZZ,OAAO,EAAEd;QACX,CAAC,CACF;QACD2B,UAAU,EAAE,GAAG;QACfpB,WAAW,EAAE;MACf,CAAC,EACD;QACEI,OAAO,EAAE;UACP,eAAe,EAAE,UAAUW,cAAc,EAAE;UAC3C,cAAc,EAAE;QAClB;MACF,CACF,CAAC;MAED,KAAAJ,eAAA,GAAIjB,QAAQ,CAAC1B,IAAI,cAAA2C,eAAA,gBAAAC,qBAAA,GAAbD,eAAA,CAAeU,OAAO,cAAAT,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAyB,CAAC,CAAC,cAAAC,sBAAA,gBAAAC,sBAAA,GAA3BD,sBAAA,CAA6BS,OAAO,cAAAR,sBAAA,eAApCA,sBAAA,CAAsCP,OAAO,EAAE;QACjD,OAAOb,QAAQ,CAAC1B,IAAI,CAACqD,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAACf,OAAO;MACjD,CAAC,MAAM;QACL,MAAM,IAAIrB,KAAK,CAAC,kCAAkC,CAAC;MACrD;IACF,CAAC,CAAC,OAAOsB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK;IACb;EACF;;EAEA;EACAe,yBAAyBA,CAAChD,WAAW,EAAE;IACrC,MAAMiD,kBAAkB,GAAG;MACzB,KAAK,EAAE,6HAA6H;MACpI,MAAM,EAAE,kIAAkI;MAC1I,OAAO,EAAE,kHAAkH;MAC3H,KAAK,EAAE,6HAA6H;MACpI,MAAM,EAAE,8IAA8I;MACtJ,QAAQ,EAAE,2HAA2H;MACrI,MAAM,EAAE,2HAA2H;MACnI,SAAS,EAAE,2HAA2H;MACtI,KAAK,EAAE,2HAA2H;MAClI,KAAK,EAAE,uGAAuG;MAC9G,OAAO,EAAE,uHAAuH;MAChI,KAAK,EAAE;IACT,CAAC;IAED,OAAOA,kBAAkB,CAACjD,WAAW,CAAC,IAAI,2DAA2D;EACvG;;EAEA;EACAkD,UAAUA,CAACnE,MAAM,EAAE;IACjB,MAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,YAAY,CAAC,CAAC;IACvC,MAAMC,QAAQ,GAAG,GAAGJ,MAAM,IAAIC,KAAK,EAAE;IACrC,IAAI,CAACL,KAAK,CAACe,MAAM,CAACP,QAAQ,CAAC;EAC7B;;EAEA;EACAgE,aAAaA,CAAA,EAAG;IACd,IAAI,CAACxE,KAAK,CAACyE,KAAK,CAAC,CAAC;EACpB;;EAEA;EACA,MAAMC,YAAYA,CAACtD,WAAW,EAAEC,WAAW,EAAEsD,YAAY,GAAG,KAAK,EAAE;IACjE,IAAI;MACF;MACA,IAAIA,YAAY,EAAE;QAChB,IAAI,CAACJ,UAAU,CAACnD,WAAW,CAACwD,WAAW,CAAC,CAAC,CAAC;MAC5C;;MAEA;MACA,OAAO,MAAM,IAAI,CAACzD,sBAAsB,CAACC,WAAW,EAAEC,WAAW,CAAC;IACpE,CAAC,CAAC,OAAOwD,WAAW,EAAE;MACpBtB,OAAO,CAACuB,GAAG,CAAC,qCAAqC,CAAC;MAElD,IAAI;QACF;QACA,OAAO,MAAM,IAAI,CAACtB,sBAAsB,CAACpC,WAAW,EAAEC,WAAW,CAAC;MACpE,CAAC,CAAC,OAAO0D,WAAW,EAAE;QACpBxB,OAAO,CAACuB,GAAG,CAAC,+CAA+C,CAAC;;QAE5D;QACA,OAAO,IAAI,CAACT,yBAAyB,CAAChD,WAAW,CAAC;MACpD;IACF;EACF;AACF;AAEA,eAAe,IAAIvB,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}