{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport SmokeAnimation from './SmokeAnimation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\nconst LandingPage = ({\n  zodiacSigns\n}) => {\n  _s();\n  useEffect(() => {\n    // Add floating animation to zodiac cards with staggered delay\n    const cards = document.querySelectorAll('.zodiac-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.1}s`;\n      card.classList.add('floating');\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page\",\n    children: [/*#__PURE__*/_jsxDEV(ParticleBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(KuberaAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SmokeAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"landing-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"main-title\",\n        children: \"\\u0DC3\\u0DD2\\u0D82\\u0DC4\\u0DBD \\u0DA2\\u0DCA\\u200D\\u0DBA\\u0DDC\\u0DAD\\u0DD2\\u0DC2 \\u0DC0\\u0DD9\\u0DB6\\u0DCA \\u0D85\\u0DA9\\u0DC0\\u0DD2\\u0DBA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"subtitle\",\n        children: \"\\u0DAF\\u0DDB\\u0DB1\\u0DD2\\u0D9A \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DC3\\u0DC4 \\u0D86\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DB8\\u0D9C \\u0DB4\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DD3\\u0DB8\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\u0D94\\u0DB6\\u0DDA \\u0DAF\\u0DDB\\u0DB1\\u0DD2\\u0D9A \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD \\u0DAF\\u0DD0\\u0DB1\\u0D9C\\u0DB1\\u0DCA\\u0DB1. \\u0DB4\\u0DD4\\u0DBB\\u0DCF\\u0DAB \\u0DA2\\u0DCA\\u200D\\u0DBA\\u0DDC\\u0DAD\\u0DD2\\u0DC2 \\u0DC1\\u0DCF\\u0DC3\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0D85\\u0DB1\\u0DD4\\u0DC0 \\u0D94\\u0DB6\\u0DDA \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DBA\\u0DA7 \\u0DB8\\u0D9C \\u0DB4\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DD3\\u0DB8 \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DB1\\u0DCA\\u0DB1.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"zodiac-grid\",\n      children: zodiacSigns.map((sign, index) => /*#__PURE__*/_jsxDEV(Link, {\n        to: `/${sign.id}`,\n        className: \"zodiac-card\",\n        style: {\n          animationDelay: `${index * 0.1}s`\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"zodiac-icon\",\n          children: zodiacIcons[sign.id]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"zodiac-name\",\n          children: sign.sinhala\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"zodiac-english\",\n          children: sign.english\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this)]\n      }, sign.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "Link", "ParticleBackground", "KuberaAnimation", "SmokeAnimation", "jsxDEV", "_jsxDEV", "zodiacIcons", "aries", "taurus", "gemini", "cancer", "leo", "virgo", "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces", "LandingPage", "zodiacSigns", "_s", "cards", "document", "querySelectorAll", "for<PERSON>ach", "card", "index", "style", "animationDelay", "classList", "add", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "sign", "to", "id", "sinhala", "english", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport SmokeAnimation from './SmokeAnimation';\n\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\nconst LandingPage = ({ zodiacSigns }) => {\n  useEffect(() => {\n    // Add floating animation to zodiac cards with staggered delay\n    const cards = document.querySelectorAll('.zodiac-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.1}s`;\n      card.classList.add('floating');\n    });\n  }, []);\n\n  return (\n    <div className=\"landing-page\">\n      <ParticleBackground />\n      <KuberaAnimation />\n      <SmokeAnimation />\n      \n      <div className=\"landing-header\">\n        <h1 className=\"main-title\">සිංහල ජ්‍යොතිෂ වෙබ් අඩවිය</h1>\n        <h2 className=\"subtitle\">දෛනික රාශිඵල සහ ආධ්‍යාත්මික මග පෙන්වීම</h2>\n        <p className=\"description\">\n          කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ ඔබේ දෛනික රාශිඵල දැනගන්න. \n          පුරාණ ජ්‍යොතිෂ ශාස්ත්‍රය අනුව ඔබේ ජීවිතයට මග පෙන්වීම ලබා ගන්න.\n        </p>\n      </div>\n\n      <div className=\"zodiac-grid\">\n        {zodiacSigns.map((sign, index) => (\n          <Link \n            key={sign.id} \n            to={`/${sign.id}`} \n            className=\"zodiac-card\"\n            style={{ animationDelay: `${index * 0.1}s` }}\n          >\n            <span className=\"zodiac-icon\">{zodiacIcons[sign.id]}</span>\n            <h3 className=\"zodiac-name\">{sign.sinhala}</h3>\n            <p className=\"zodiac-english\">{sign.english}</p>\n          </Link>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default LandingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,cAAc,MAAM,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,WAAW,GAAG;EAClBC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE,GAAG;EACXC,GAAG,EAAE,GAAG;EACRC,KAAK,EAAE,GAAG;EACVC,KAAK,EAAE,GAAG;EACVC,OAAO,EAAE,GAAG;EACZC,WAAW,EAAE,GAAG;EAChBC,SAAS,EAAE,GAAG;EACdC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EACvCtB,SAAS,CAAC,MAAM;IACd;IACA,MAAMuB,KAAK,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,cAAc,CAAC;IACvDF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAC7BD,IAAI,CAACE,KAAK,CAACC,cAAc,GAAG,GAAGF,KAAK,GAAG,GAAG,GAAG;MAC7CD,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE1B,OAAA;IAAK2B,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3B5B,OAAA,CAACJ,kBAAkB;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBhC,OAAA,CAACH,eAAe;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnBhC,OAAA,CAACF,cAAc;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAElBhC,OAAA;MAAK2B,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7B5B,OAAA;QAAI2B,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACzDhC,OAAA;QAAI2B,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAAsC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpEhC,OAAA;QAAG2B,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAG3B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAENhC,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBb,WAAW,CAACkB,GAAG,CAAC,CAACC,IAAI,EAAEZ,KAAK,kBAC3BtB,OAAA,CAACL,IAAI;QAEHwC,EAAE,EAAE,IAAID,IAAI,CAACE,EAAE,EAAG;QAClBT,SAAS,EAAC,aAAa;QACvBJ,KAAK,EAAE;UAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;QAAI,CAAE;QAAAM,QAAA,gBAE7C5B,OAAA;UAAM2B,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAE3B,WAAW,CAACiC,IAAI,CAACE,EAAE;QAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3DhC,OAAA;UAAI2B,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAEM,IAAI,CAACG;QAAO;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC/ChC,OAAA;UAAG2B,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAEM,IAAI,CAACI;QAAO;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA,GAP3CE,IAAI,CAACE,EAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQR,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAzCIF,WAAW;AAAAyB,EAAA,GAAXzB,WAAW;AA2CjB,eAAeA,WAAW;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}