{"ast": null, "code": "import * as THREE from 'three';\nimport * as React from 'react';\nimport { NoEventPriority, DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport { createWithEqualityFn } from 'zustand/traditional';\nimport { useFiber, useContextBridge, traverseFiber } from 'its-fine';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { suspend, preload, clear } from 'suspend-react';\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\n\nconst createReconciler = Reconciler;\n\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\n\nconst catalogue = {};\nlet i = 0;\nconst isConstructor$1 = object => typeof object === 'function';\nfunction extend(objects) {\n  if (isConstructor$1(objects)) {\n    const Component = `${i++}`;\n    catalogue[Component] = objects;\n    return Component;\n  } else {\n    Object.assign(catalogue, objects);\n  }\n}\nfunction validateInstance(type, props) {\n  // Get target from catalogue\n  const name = `${type[0].toUpperCase()}${type.slice(1)}`;\n  const target = catalogue[name];\n\n  // Validate element target\n  if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n\n  // Validate primitives\n  if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n\n  // Throw if an object or literal was passed for args\n  if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n  var _props$object;\n  validateInstance(type, props);\n\n  // Regenerate the R3F instance for primitives to simulate a new object\n  if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n  return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n  if (!instance.isHidden) {\n    var _instance$parent;\n    if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n      detach(instance.parent, instance);\n    } else if (isObject3D(instance.object)) {\n      instance.object.visible = false;\n    }\n    instance.isHidden = true;\n    invalidateInstance(instance);\n  }\n}\nfunction unhideInstance(instance) {\n  if (instance.isHidden) {\n    var _instance$parent2;\n    if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n      attach(instance.parent, instance);\n    } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n      instance.object.visible = true;\n    }\n    instance.isHidden = false;\n    invalidateInstance(instance);\n  }\n}\n\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n  // Bail if tree isn't mounted or parent is not a container.\n  // This ensures that the tree is finalized and React won't discard results to Suspense\n  const state = child.root.getState();\n  if (!parent.parent && parent.object !== state.scene) return;\n\n  // Create & link object on first run\n  if (!child.object) {\n    var _child$props$object, _child$props$args;\n    // Get target from catalogue\n    const name = `${child.type[0].toUpperCase()}${child.type.slice(1)}`;\n    const target = catalogue[name];\n\n    // Create object\n    child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...((_child$props$args = child.props.args) != null ? _child$props$args : []));\n    child.object.__r3f = child;\n\n    // Set initial props\n    applyProps(child.object, child.props);\n  }\n\n  // Append instance\n  if (child.props.attach) {\n    attach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n    if (beforeChild && childIndex !== -1) {\n      child.object.parent = parent.object;\n      parent.object.children.splice(childIndex, 0, child.object);\n      child.object.dispatchEvent({\n        type: 'added'\n      });\n      parent.object.dispatchEvent({\n        type: 'childadded',\n        child: child.object\n      });\n    } else {\n      parent.object.add(child.object);\n    }\n  }\n\n  // Link subtree\n  for (const childInstance of child.children) handleContainerEffects(child, childInstance);\n\n  // Tree was updated, request a frame\n  invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n  if (!child) return;\n\n  // Link instances\n  child.parent = parent;\n  parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n  if (!child || !beforeChild) return;\n\n  // Link instances\n  child.parent = parent;\n  const childIndex = parent.children.indexOf(beforeChild);\n  if (childIndex !== -1) parent.children.splice(childIndex, 0, child);else parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n  if (typeof object.dispose === 'function') {\n    const handleDispose = () => {\n      try {\n        object.dispose();\n      } catch {\n        // no-op\n      }\n    };\n\n    // In a testing environment, cleanup immediately\n    if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n    // Otherwise, using a real GPU so schedule cleanup to prevent stalls\n    else unstable_scheduleCallback(unstable_IdlePriority, handleDispose);\n  }\n}\nfunction removeChild(parent, child, dispose) {\n  if (!child) return;\n\n  // Unlink instances\n  child.parent = null;\n  const childIndex = parent.children.indexOf(child);\n  if (childIndex !== -1) parent.children.splice(childIndex, 1);\n\n  // Eagerly tear down tree\n  if (child.props.attach) {\n    detach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    parent.object.remove(child.object);\n    removeInteractivity(findInitialRoot(child), child.object);\n  }\n\n  // Allow objects to bail out of unmount disposal with dispose={null}\n  const shouldDispose = child.props.dispose !== null && dispose !== false;\n\n  // Recursively remove instance children\n  for (let i = child.children.length - 1; i >= 0; i--) {\n    const node = child.children[i];\n    removeChild(child, node, shouldDispose);\n  }\n  child.children.length = 0;\n\n  // Unlink instance object\n  delete child.object.__r3f;\n\n  // Dispose object whenever the reconciler feels like it.\n  // Never dispose of primitives because their state may be kept outside of React!\n  // In order for an object to be able to dispose it\n  //   - has a dispose method\n  //   - cannot be a <primitive object={...} />\n  //   - cannot be a THREE.Scene, because three has broken its own API\n  if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n    disposeOnIdle(child.object);\n  }\n\n  // Tree was updated, request a frame for top-level instance\n  if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n  for (const _fiber of [fiber, fiber.alternate]) {\n    if (_fiber !== null) {\n      if (typeof _fiber.ref === 'function') {\n        _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n        const cleanup = _fiber.ref(publicInstance);\n        if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n      } else if (_fiber.ref) {\n        _fiber.ref.current = publicInstance;\n      }\n    }\n  }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n  // Detach instance\n  for (const [instance] of reconstructed) {\n    const parent = instance.parent;\n    if (parent) {\n      if (instance.props.attach) {\n        detach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.remove(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          detach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.remove(child.object);\n        }\n      }\n    }\n\n    // If the old instance is hidden, we need to unhide it.\n    // React assumes it can discard instances since they're pure for DOM.\n    // This isn't true for us since our lifetimes are impure and longliving.\n    // So, we manually check if an instance was hidden and unhide it.\n    if (instance.isHidden) unhideInstance(instance);\n\n    // Dispose of old object if able\n    if (instance.object.__r3f) delete instance.object.__r3f;\n    if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n  }\n\n  // Update instance\n  for (const [instance, props, fiber] of reconstructed) {\n    instance.props = props;\n    const parent = instance.parent;\n    if (parent) {\n      var _instance$props$objec, _instance$props$args;\n      // Get target from catalogue\n      const name = `${instance.type[0].toUpperCase()}${instance.type.slice(1)}`;\n      const target = catalogue[name];\n\n      // Create object\n      instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...((_instance$props$args = instance.props.args) != null ? _instance$props$args : []));\n      instance.object.__r3f = instance;\n      setFiberRef(fiber, instance.object);\n\n      // Set initial props\n      applyProps(instance.object, instance.props);\n      if (instance.props.attach) {\n        attach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.add(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          attach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.add(child.object);\n        }\n      }\n\n      // Tree was updated, request a frame\n      invalidateInstance(instance);\n    }\n  }\n  reconstructed.length = 0;\n}\n\n// Don't handle text instances, warn on undefined behavior\nconst handleTextInstance = () => console.warn('R3F: Text is not allowed in JSX! This could be stray whitespace or characters.');\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = createReconciler({\n  isPrimaryRenderer: false,\n  warnsIfNotActing: false,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  createInstance,\n  removeChild,\n  appendChild,\n  appendInitialChild: appendChild,\n  insertBefore,\n  appendChildToContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    appendChild(scene, child);\n  },\n  removeChildFromContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    removeChild(scene, child);\n  },\n  insertInContainerBefore(container, child, beforeChild) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !beforeChild || !scene) return;\n    insertBefore(scene, child, beforeChild);\n  },\n  getRootHostContext: () => NO_CONTEXT,\n  getChildHostContext: () => NO_CONTEXT,\n  commitUpdate(instance, type, oldProps, newProps, fiber) {\n    var _newProps$args, _oldProps$args, _newProps$args2;\n    validateInstance(type, newProps);\n    let reconstruct = false;\n\n    // Reconstruct primitives if object prop changes\n    if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n    // Reconstruct instance if args were added or removed\n    else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n    // Reconstruct instance if args were changed\n    else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index) => {\n      var _oldProps$args2;\n      return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n    })) reconstruct = true;\n\n    // Reconstruct when args or <primitive object={...} have changes\n    if (reconstruct) {\n      reconstructed.push([instance, {\n        ...newProps\n      }, fiber]);\n    } else {\n      // Create a diff-set, flag if there are any changes\n      const changedProps = diffProps(instance, newProps);\n      if (Object.keys(changedProps).length) {\n        Object.assign(instance.props, changedProps);\n        applyProps(instance.object, changedProps);\n      }\n    }\n\n    // Flush reconstructed siblings when we hit the last updated child in a sequence\n    const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n    if (isTailSibling) swapInstances();\n  },\n  finalizeInitialChildren: () => false,\n  commitMount() {},\n  getPublicInstance: instance => instance == null ? void 0 : instance.object,\n  prepareForCommit: () => null,\n  preparePortalMount: container => prepare(container.getState().scene, container, '', {}),\n  resetAfterCommit: () => {},\n  shouldSetTextContent: () => false,\n  clearContainer: () => false,\n  hideInstance,\n  unhideInstance,\n  createTextInstance: handleTextInstance,\n  hideTextInstance: handleTextInstance,\n  unhideTextInstance: handleTextInstance,\n  scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n  cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n  noTimeout: -1,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur() {},\n  afterActiveInstanceBlur() {},\n  detachDeletedInstance() {},\n  prepareScopeUpdate() {},\n  getInstanceFromScope: () => null,\n  shouldAttemptEagerTransition() {\n    return false;\n  },\n  requestPostPaintCallback() {},\n  maySuspendCommit() {\n    return false;\n  },\n  preloadInstance() {\n    return true; // true indicates already loaded\n  },\n  startSuspendingCommit() {},\n  suspendInstance() {},\n  waitForCommitToBeReady() {\n    return null;\n  },\n  NotPendingTransition: null,\n  setCurrentUpdatePriority(newPriority) {\n    currentUpdatePriority = newPriority;\n  },\n  getCurrentUpdatePriority() {\n    return currentUpdatePriority;\n  },\n  resolveUpdatePriority() {\n    var _window$event;\n    if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n    switch (typeof window !== 'undefined' && ((_window$event = window.event) == null ? void 0 : _window$event.type)) {\n      case 'click':\n      case 'contextmenu':\n      case 'dblclick':\n      case 'pointercancel':\n      case 'pointerdown':\n      case 'pointerup':\n        return DiscreteEventPriority;\n      case 'pointermove':\n      case 'pointerout':\n      case 'pointerover':\n      case 'pointerenter':\n      case 'pointerleave':\n      case 'wheel':\n        return ContinuousEventPriority;\n      default:\n        return DefaultEventPriority;\n    }\n  },\n  resetFormInstance() {}\n});\nvar _window$document, _window$navigator;\n/**\r\n * Returns the instance's initial (outmost) root.\r\n */\nfunction findInitialRoot(instance) {\n  let root = instance.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\n/**\r\n * Safely flush async effects when testing, simulating a legacy root.\r\n */\nconst act = React.act;\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\n/**\r\n * Bridges renderer Context and StrictMode from a primary renderer.\r\n */\nfunction useBridge() {\n  const fiber = useFiber();\n  const ContextBridge = useContextBridge();\n  return React.useMemo(() => ({\n    children\n  }) => {\n    const strict = !!traverseFiber(fiber, true, node => node.type === React.StrictMode);\n    const Root = strict ? React.StrictMode : React.Fragment;\n    return /*#__PURE__*/jsx(Root, {\n      children: /*#__PURE__*/jsx(ContextBridge, {\n        children: children\n      })\n    });\n  }, [fiber, ContextBridge]);\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nfunction getRootState(obj) {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n  for (const p in obj) {\n    const prop = obj[p];\n    if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n  }\n}\nconst REACT_INTERNAL_PROPS = ['children', 'key', 'ref'];\n\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n  const props = {};\n  for (const key in queue) {\n    if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n  }\n  return props;\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n  const object = target;\n\n  // Create instance descriptor\n  let instance = object == null ? void 0 : object.__r3f;\n  if (!instance) {\n    instance = {\n      root,\n      type,\n      parent: null,\n      children: [],\n      props: getInstanceProps(props),\n      object,\n      eventCount: 0,\n      handlers: {},\n      isHidden: false\n    };\n    if (object) {\n      object.__r3f = instance;\n      if (type) applyProps(object, instance.props);\n    }\n  }\n  return instance;\n}\nfunction resolve(root, key) {\n  var _target;\n  let target = root[key];\n  if (!key.includes('-')) return {\n    root,\n    key,\n    target\n  };\n\n  // Resolve pierced target\n  const chain = key.split('-');\n  target = chain.reduce((acc, key) => acc[key], root);\n  key = chain.pop();\n\n  // Switch root if atomic\n  if (!((_target = target) != null && _target.set)) root = chain.reduce((acc, key) => acc[key], root);\n  return {\n    root,\n    key,\n    target\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n  if (is.str(child.props.attach)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(child.props.attach)) {\n      const index = child.props.attach.replace(INDEX_REGEX, '');\n      const {\n        root,\n        key\n      } = resolve(parent.object, index);\n      if (!Array.isArray(root[key])) root[key] = [];\n    }\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    child.previousAttach = root[key];\n    root[key] = child.object;\n  } else if (is.fun(child.props.attach)) {\n    child.previousAttach = child.props.attach(parent.object, child.object);\n  }\n}\nfunction detach(parent, child) {\n  if (is.str(child.props.attach)) {\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    const previous = child.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete root[key];\n    // Otherwise set the previous value\n    else root[key] = previous;\n  } else {\n    child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n  }\n  delete child.previousAttach;\n}\nconst RESERVED_PROPS = [...REACT_INTERNAL_PROPS,\n// Instance props\n'args', 'dispose', 'attach', 'object', 'onUpdate',\n// Behavior flags\n'dispose'];\nconst MEMOIZED_PROTOTYPES = new Map();\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n  const changedProps = {};\n\n  // Sort through props\n  for (const prop in newProps) {\n    // Skip reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n    // Skip if props match\n    if (is.equ(newProps[prop], instance.props[prop])) continue;\n\n    // Props changed, add them\n    changedProps[prop] = newProps[prop];\n\n    // Reset pierced props\n    for (const other in newProps) {\n      if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n    }\n  }\n\n  // Reset removed props for HMR\n  for (const prop in instance.props) {\n    if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n    const {\n      root,\n      key\n    } = resolve(instance.object, prop);\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (root.constructor && root.constructor.length === 0) {\n      // create a blank slate of the instance and copy the particular parameter.\n      let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n      if (!ctor) {\n        ctor = new root.constructor();\n        MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n      }\n      changedProps[key] = ctor[key];\n    } else {\n      // instance does not have constructor, just set it to 0\n      changedProps[key] = 0;\n    }\n  }\n  return changedProps;\n}\nconst __DEV__ = typeof process !== 'undefined' && process.env.NODE_ENV !== 'production';\n\n// const LinearEncoding = 3000\nconst sRGBEncoding = 3001;\nconst SRGBColorSpace = 'srgb';\nconst LinearSRGBColorSpace = 'srgb-linear';\n\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = ['map', 'emissiveMap', 'sheenTintMap',\n// <r134\n'sheenColorMap', 'specularTintMap',\n// <r134\n'specularColorMap', 'envMap'];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n  const instance = object.__r3f;\n  const rootState = instance && findInitialRoot(instance).getState();\n  const prevHandlers = instance == null ? void 0 : instance.eventCount;\n  for (const prop in props) {\n    let value = props[prop];\n\n    // Don't mutate reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n\n    // Deal with pointer events, including removing them if undefined\n    if (instance && EVENT_REGEX.test(prop)) {\n      if (typeof value === 'function') instance.handlers[prop] = value;else delete instance.handlers[prop];\n      instance.eventCount = Object.keys(instance.handlers).length;\n    }\n\n    // Ignore setting undefined props\n    // https://github.com/pmndrs/react-three-fiber/issues/274\n    if (value === undefined) continue;\n    let {\n      root,\n      key,\n      target\n    } = resolve(object, prop);\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(root)) {\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n\n    // Copy if properties match signatures\n    if (target != null && target.copy && (\n    // Some environments may break strict identity checks by duplicating versions of three.js.\n    // Loosen to unminified names, ignoring descendents.\n    // https://github.com/pmndrs/react-three-fiber/issues/2856\n    // TODO: fix upstream and remove in v9\n    __DEV__ ? target.constructor.name === value.constructor.name : target.constructor === value.constructor)) {\n      target.copy(value);\n    }\n    // Layers have no copy function, we must therefore copy the mask property\n    else if (target instanceof THREE.Layers && value instanceof THREE.Layers) {\n      target.mask = value.mask;\n    }\n    // Set array types\n    else if (target != null && target.set && Array.isArray(value)) {\n      if (target.fromArray) target.fromArray(value);else target.set(...value);\n    }\n    // Set literal types\n    else if (target != null && target.set && typeof value !== 'object') {\n      const isColor = target instanceof THREE.Color;\n      // Allow setting array scalars\n      if (!isColor && target.setScalar && typeof value === 'number') target.setScalar(value);\n      // Otherwise just set single value\n      else target.set(value);\n\n      // Emulate THREE.ColorManagement for older three.js versions\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if (!getColorManagement() && !(rootState != null && rootState.linear) && isColor) target.convertSRGBToLinear();\n    }\n    // Else, just overwrite the value\n    else {\n      root[key] = value;\n\n      // Auto-convert sRGB texture parameters for built-in materials\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      // https://github.com/mrdoob/three.js/pull/25857\n      if (rootState && !rootState.linear && colorMaps.includes(key) && root[key] instanceof THREE.Texture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      root[key].format === THREE.RGBAFormat && root[key].type === THREE.UnsignedByteType) {\n        // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n        if (hasColorSpace(root[key])) root[key].colorSpace = 'srgb';else root[key].encoding = sRGBEncoding;\n      }\n    }\n  }\n\n  // Register event handlers\n  if (instance != null && instance.parent && rootState != null && rootState.internal && instance.object instanceof THREE.Object3D && prevHandlers !== instance.eventCount) {\n    // Pre-emptively remove the instance from the interaction manager\n    const index = rootState.internal.interaction.indexOf(instance.object);\n    if (index > -1) rootState.internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (instance.eventCount && instance.object.raycast !== null && instance.object instanceof THREE.Object3D) {\n      rootState.internal.interaction.push(instance.object);\n    }\n  }\n\n  // Auto-attach geometries and materials\n  if (instance && instance.props.attach === undefined) {\n    if (instance.object instanceof THREE.BufferGeometry) instance.props.attach = 'geometry';else if (instance.object instanceof THREE.Material) instance.props.attach = 'material';\n  }\n\n  // Instance was updated, request a frame\n  if (instance) invalidateInstance(instance);\n  return object;\n}\nfunction invalidateInstance(instance) {\n  var _instance$root;\n  if (!instance.parent) return;\n  instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n  const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n  // Do not mess with the camera if it belongs to the user\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  if (camera.manual) return;\n  if (isOrthographicCamera(camera)) {\n    camera.left = size.width / -2;\n    camera.right = size.width / 2;\n    camera.top = size.height / 2;\n    camera.bottom = size.height / -2;\n  } else {\n    camera.aspect = size.width / size.height;\n  }\n  camera.updateProjectionMatrix();\n}\nconst isObject3D = object => object == null ? void 0 : object.isObject3D;\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object);\n        if (state) {\n          const {\n            raycaster,\n            pointer,\n            camera,\n            internal\n          } = state;\n          const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n          const hasPointerCapture = id => {\n            var _internal$capturedMap, _internal$capturedMap2;\n            return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n          };\n          const setPointerCapture = id => {\n            const captureData = {\n              intersection: hit,\n              target: event.target\n            };\n            if (internal.capturedMap.has(id)) {\n              // if the pointerId was previously captured, we add the hit to the\n              // event capturedMap.\n              internal.capturedMap.get(id).set(hit.eventObject, captureData);\n            } else {\n              // if the pointerId was not previously captured, we create a map\n              // containing the hitObject, and the hit. hitObject is used for\n              // faster access.\n              internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n            }\n            event.target.setPointerCapture(id);\n          };\n          const releasePointerCapture = id => {\n            const captures = internal.capturedMap.get(id);\n            if (captures) {\n              releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n            }\n          };\n\n          // Add native event props\n          let extractEventProps = {};\n          // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n          for (let prop in event) {\n            let property = event[prop];\n            // Only copy over atomics, leave functions alone as these should be\n            // called as event.nativeEvent.fn()\n            if (typeof property !== 'function') extractEventProps[prop] = property;\n          }\n          let raycastEvent = {\n            ...hit,\n            ...extractEventProps,\n            pointer,\n            intersections,\n            stopped: localState.stopped,\n            delta,\n            unprojectedPoint,\n            ray: raycaster.ray,\n            camera: camera,\n            // Hijack stopPropagation, which just sets a flag\n            stopPropagation() {\n              // https://github.com/pmndrs/react-three-fiber/issues/596\n              // Events are not allowed to stop propagation if the pointer has been captured\n              const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n              // We only authorize stopPropagation...\n              if (\n              // ...if this pointer hasn't been captured\n              !capturesForPointer ||\n              // ... or if the hit object is capturing the pointer\n              capturesForPointer.has(hit.eventObject)) {\n                raycastEvent.stopped = localState.stopped = true;\n                // Propagation is stopped, remove all other hover records\n                // An event handler is only allowed to flush other handlers if it is hovered itself\n                if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                  // Objects cannot flush out higher up objects that have already caught the event\n                  const higher = intersections.slice(0, intersections.indexOf(hit));\n                  cancelPointer([...higher, hit]);\n                }\n              }\n            },\n            // there should be a distinction between target and currentTarget\n            target: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            currentTarget: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            nativeEvent: event\n          };\n\n          // Call subscribers\n          callback(raycastEvent);\n          // Event bubbling may be interrupted by stopPropagation\n          if (localState.stopped === true) break;\n        }\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          const handlers = instance.handlers;\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n        const handlers = instance.handlers;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootStore = createWithEqualityFn((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target instanceof THREE.Vector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      scene: null,\n      xr: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, top = 0, left = 0) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top,\n          left\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        // Events\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        lastEvent: /*#__PURE__*/React.createRef(),\n        // Updates\n        active: false,\n        frames: 0,\n        priority: 0,\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootStore.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootStore.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootStore.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootStore.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootStore;\n};\n\n/**\r\n * Exposes an object's {@link Instance}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useinstancehandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  React.useImperativeHandle(instance, () => ref.current.__r3f, [ref]);\n  return instance;\n}\n\n/**\r\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\r\n */\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor = value => {\n  var _value$prototype;\n  return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n  return async function (Proto, ...input) {\n    let loader;\n\n    // Construct and cache loader if constructor was passed\n    if (isConstructor(Proto)) {\n      loader = memoizedLoaders.get(Proto);\n      if (!loader) {\n        loader = new Proto();\n        memoizedLoaders.set(Proto, loader);\n      }\n    } else {\n      loader = Proto;\n    }\n\n    // Apply loader extensions\n    if (extensions) extensions(loader);\n\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => res(isObject3D(data == null ? void 0 : data.scene) ? Object.assign(data, buildGraph(data.scene)) : data), onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(loader, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [loader, ...keys], {\n    equal: is.equ\n  });\n  // Return the object(s)\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (loader, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [loader, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (loader, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([loader, ...keys]);\n};\nconst _roots = new Map();\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;\n  return new THREE.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, size) {\n  if (!size && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    ...size\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = _roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store,\n  // container\n  ConcurrentRoot,\n  // tag\n  null,\n  // hydration callbacks\n  false,\n  // isStrictMode\n  null,\n  // concurrentUpdatesByDefaultOverride\n  '',\n  // identifierPrefix\n  logRecoverableError,\n  // onUncaughtError\n  logRecoverableError,\n  // onCaughtError\n  logRecoverableError,\n  // onRecoverableError\n  null // transitionCallbacks\n  );\n  // Map it\n  if (!prevRoot) _roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof THREE.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if (!camera.manual) {\n              if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                camera.manual = true;\n                camera.updateProjectionMatrix();\n              }\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions instanceof THREE.Scene) {\n          scene = sceneOptions;\n          prepare(scene, store, '', {});\n        } else {\n          scene = new THREE.Scene();\n          prepare(scene, store, '', {});\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (gl.xr) xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n\n      // Set color space and tonemapping preferences\n      if (!configured) {\n        const LinearEncoding = 3000;\n        const sRGBEncoding = 3001;\n        applyProps(gl, {\n          outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n          toneMapping: flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping\n        });\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer(/*#__PURE__*/jsx(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = _roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state.scene);\n            _roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = useMutableCallback((rootState, injectState) => {\n    let viewport = undefined;\n    if (injectState.camera && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...rootState,\n      ...injectState,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...injectState.events,\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      // Layers are allowed to override events\n      setEvents: events => injectState.set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    };\n  });\n  const usePortalStore = React.useMemo(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const store = createWithEqualityFn((set, get) => ({\n      ...rest,\n      set,\n      get\n    }));\n\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const onMutate = prev => store.setState(state => inject.current(prev, state));\n    onMutate(previousRoot.getState());\n    previousRoot.subscribe(onMutate);\n    return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [previousRoot, container]);\n  return /*#__PURE__*/jsx(Fragment, {\n    children: reconciler.createPortal(/*#__PURE__*/jsx(context.Provider, {\n      value: usePortalStore,\n      children: children\n    }), usePortalStore, null)\n  });\n}\nreconciler.injectIntoDevTools({\n  bundleType: process.env.NODE_ENV === 'production' ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: React.version\n});\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (let i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n  frame = requestAnimationFrame(loop);\n  running = true;\n  repeat = 0;\n\n  // Run effects\n  flushGlobalEffects('before', timestamp);\n\n  // Render all roots\n  useFrameInProgress = true;\n  for (const root of _roots.values()) {\n    var _state$gl$xr;\n    state = root.store.getState();\n\n    // If the frameloop is invalidated, do not run another frame\n    if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n      repeat += update(timestamp, state);\n    }\n  }\n  useFrameInProgress = true;\n\n  // Run after-effects\n  flushGlobalEffects('after', timestamp);\n\n  // Stop the loop if nothing invalidates it\n  if (repeat === 0) {\n    // Tail call effects, they are called when rendering stops\n    flushGlobalEffects('tail', timestamp);\n\n    // Flag end of operation\n    running = false;\n    return cancelAnimationFrame(frame);\n  }\n}\n\n/**\r\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\r\n */\nfunction invalidate(state, frames = 1) {\n  var _state$gl$xr2;\n  if (!state) return _roots.forEach(root => invalidate(root.store.getState(), frames));\n  if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n  if (frames > 1) {\n    // legacy support for people using frames parameters\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n  } else {\n    if (useFrameInProgress) {\n      //called from within a useFrame, it means the user wants an additional frame\n      state.internal.frames = 2;\n    } else {\n      //the user need a new frame, no need to increment further than 1\n      state.internal.frames = 1;\n    }\n  }\n\n  // If the render-loop isn't active, start it\n  if (!running) {\n    running = true;\n    requestAnimationFrame(loop);\n  }\n}\n\n/**\r\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\r\n */\nfunction advance(timestamp, runGlobalEffects = true, state, frame) {\n  if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n  if (!state) for (const root of _roots.values()) update(timestamp, root.store.getState());else update(timestamp, state, frame);\n  if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\nexport { useThree as A, Block as B, useFrame as C, useGraph as D, ErrorBoundary as E, useLoader as F, _roots as _, useMutableCallback as a, useIsomorphicLayoutEffect as b, createEvents as c, createRoot as d, extend as e, unmountComponentAtNode as f, flushGlobalEffects as g, addEffect as h, isRef as i, addAfterEffect as j, addTail as k, invalidate as l, advance as m, render as n, createPortal as o, context as p, applyProps as q, reconciler as r, getRootState as s, threeTypes as t, useBridge as u, dispose as v, act as w, buildGraph as x, useInstanceHandle as y, useStore as z };", "map": {"version": 3, "names": ["THREE", "React", "NoEventPriority", "DefaultEventPriority", "ContinuousEventPriority", "DiscreteEventPriority", "ConcurrentRoot", "createWithEqualityFn", "useFiber", "useContextBridge", "traverseFiber", "<PERSON><PERSON><PERSON><PERSON>", "unstable_scheduleCallback", "unstable_IdlePriority", "jsx", "Fragment", "suspend", "preload", "clear", "threeTypes", "Object", "freeze", "__proto__", "createReconciler", "catalogue", "i", "isConstructor$1", "object", "extend", "objects", "Component", "assign", "validateInstance", "type", "props", "name", "toUpperCase", "slice", "target", "Error", "args", "undefined", "Array", "isArray", "createInstance", "root", "_props$object", "__r3f", "prepare", "hideInstance", "instance", "isHidden", "_instance$parent", "attach", "parent", "detach", "isObject3D", "visible", "invalidateInstance", "unhideInstance", "_instance$parent2", "handleContainerEffects", "child", "<PERSON><PERSON><PERSON><PERSON>", "state", "getState", "scene", "_child$props$object", "_child$props$args", "applyProps", "childIndex", "children", "indexOf", "splice", "dispatchEvent", "add", "childInstance", "append<PERSON><PERSON><PERSON>", "push", "insertBefore", "disposeOnIdle", "dispose", "handleDispose", "IS_REACT_ACT_ENVIRONMENT", "<PERSON><PERSON><PERSON><PERSON>", "remove", "removeInteractivity", "findInitialRoot", "shouldDispose", "length", "node", "setFiberRef", "fiber", "publicInstance", "_fiber", "alternate", "ref", "refCleanup", "cleanup", "current", "reconstructed", "swapInstances", "_instance$props$objec", "_instance$props$args", "handleTextInstance", "console", "warn", "NO_CONTEXT", "currentUpdatePriority", "NoFlags", "Update", "reconciler", "isPrimary<PERSON><PERSON><PERSON>", "warnsIfNotActing", "supportsMutation", "supportsPersistence", "supportsHydration", "appendInitialChild", "append<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "container", "<PERSON><PERSON><PERSON><PERSON><PERSON>rom<PERSON><PERSON><PERSON>", "insertInContainerBefore", "getRootHostContext", "getChildHostContext", "commitUpdate", "oldProps", "newProps", "_newProps$args", "_oldProps$args", "_newProps$args2", "reconstruct", "some", "value", "index", "_oldProps$args2", "changedProps", "diffProps", "keys", "isTailSibling", "sibling", "flags", "finalizeInitialChildren", "commitMount", "getPublicInstance", "prepareForCommit", "preparePortalMount", "resetAfterCommit", "shouldSetTextContent", "clearContainer", "createTextInstance", "hideTextInstance", "unhideTextInstance", "scheduleTimeout", "setTimeout", "cancelTimeout", "clearTimeout", "noTimeout", "getInstanceFromNode", "beforeActiveInstanceBlur", "afterActiveInstanceBlur", "detachDeletedInstance", "prepareScopeUpdate", "getInstanceFromScope", "shouldAttemptEagerTransition", "requestPostPaintCallback", "maySuspendCommit", "preloadInstance", "startSuspendingCommit", "suspendInstance", "waitForCommitToBeReady", "NotPendingTransition", "setCurrentUpdatePriority", "newPriority", "getCurrentUpdatePriority", "resolveUpdatePriority", "_window$event", "window", "event", "resetFormInstance", "_window$document", "_window$navigator", "previousRoot", "hasColorSpace", "getColorManagement", "_ColorManagement", "ColorManagement", "act", "isOrthographicCamera", "def", "isRef", "obj", "hasOwnProperty", "useIsomorphicLayoutEffect", "document", "createElement", "navigator", "product", "useLayoutEffect", "useEffect", "useMutableCallback", "fn", "useRef", "useBridge", "ContextBridge", "useMemo", "strict", "StrictMode", "Root", "Block", "set", "Promise", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "error", "componentDidCatch", "err", "render", "getDerivedStateFromError", "calculateDpr", "dpr", "_window$devicePixelRa", "devicePixelRatio", "Math", "min", "max", "getRootState", "_r3f", "is", "a", "arr", "fun", "str", "num", "boo", "und", "equ", "b", "arrays", "isObj", "isArr", "buildGraph", "data", "nodes", "materials", "traverse", "material", "p", "prop", "REACT_INTERNAL_PROPS", "getInstanceProps", "queue", "key", "includes", "eventCount", "handlers", "resolve", "_target", "chain", "split", "reduce", "acc", "pop", "INDEX_REGEX", "test", "replace", "previousAttach", "previous", "RESERVED_PROPS", "MEMOIZED_PROTOTYPES", "Map", "other", "startsWith", "ctor", "get", "__DEV__", "process", "env", "NODE_ENV", "sRGBEncoding", "SRGBColorSpace", "LinearSRGBColorSpace", "colorMaps", "EVENT_REGEX", "rootState", "prevHandlers", "copy", "Layers", "mask", "fromArray", "isColor", "Color", "setScalar", "linear", "convertSRGBToLinear", "Texture", "format", "RGBAFormat", "UnsignedByteType", "colorSpace", "encoding", "internal", "Object3D", "interaction", "raycast", "BufferGeometry", "Material", "_instance$root", "onUpdate", "frames", "invalidate", "updateCamera", "camera", "size", "manual", "left", "width", "right", "top", "height", "bottom", "aspect", "updateProjectionMatrix", "makeId", "eventObject", "uuid", "instanceId", "releaseInternalPointerCapture", "capturedMap", "captures", "pointerId", "captureData", "delete", "releasePointerCapture", "store", "filter", "o", "initialHits", "hovered", "for<PERSON>ach", "createEvents", "calculateDistance", "dx", "offsetX", "initialClick", "dy", "offsetY", "round", "sqrt", "filterPointerEvents", "intersect", "duplicates", "Set", "intersections", "eventsObjects", "raycaster", "events", "compute", "handleRaycast", "enabled", "_state$previousRoot", "intersectObject", "hits", "flatMap", "sort", "aState", "bState", "distance", "priority", "item", "id", "has", "hit", "_r3f2", "values", "intersection", "handleIntersects", "delta", "callback", "localState", "stopped", "pointer", "unprojectedPoint", "Vector3", "x", "y", "unproject", "hasPointerCapture", "_internal$capturedMap", "_internal$capturedMap2", "setPointerCapture", "extractEventProps", "property", "raycastEvent", "ray", "stopPropagation", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "find", "higher", "cancelPointer", "currentTarget", "nativeEvent", "hovered<PERSON>bj", "onPointerOut", "onPointerLeave", "pointerMissed", "onPointerMissed", "handlePointer", "requestAnimationFrame", "handleEvent", "lastEvent", "isPointerMove", "isClickEvent", "map", "onIntersect", "onPointerOver", "onPointerEnter", "hoveredItem", "onPointerMove", "handler", "<PERSON><PERSON><PERSON><PERSON>", "context", "createContext", "createStore", "advance", "rootStore", "position", "defaultTarget", "tempTarget", "getCurrentViewport", "getWorldPosition", "distanceTo", "zoom", "factor", "fov", "PI", "h", "tan", "w", "performanceTimeout", "setPerformanceCurrent", "performance", "Vector2", "gl", "connected", "xr", "timestamp", "runGlobalEffects", "legacy", "flat", "controls", "clock", "Clock", "mouse", "frameloop", "debounce", "regress", "viewport", "initialDpr", "setEvents", "setSize", "setDpr", "resolved", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stop", "elapsedTime", "start", "subscribers", "createRef", "active", "subscribe", "s", "oldSize", "oldDpr", "oldCamera", "setPixelRatio", "updateStyle", "HTMLCanvasElement", "dom<PERSON>lement", "useInstanceHandle", "useImperativeHandle", "useStore", "useContext", "useThree", "selector", "equalityFn", "useFrame", "renderPriority", "useGraph", "memoizedLoaders", "WeakMap", "isConstructor", "_value$prototype", "prototype", "loadingFn", "extensions", "onProgress", "Proto", "input", "loader", "all", "res", "reject", "load", "message", "useLoader", "results", "equal", "_roots", "shallowLoose", "createRendererInstance", "canvas", "customRenderer", "WebGLRenderer", "powerPreference", "antialias", "alpha", "computeInitialSize", "parentElement", "getBoundingClientRect", "OffscreenCanvas", "createRoot", "prevRoot", "prevFiber", "prevStore", "logRecoverableError", "reportError", "createContainer", "onCreated", "configured", "lastCamera", "configure", "glConfig", "propsSize", "sceneOptions", "onCreatedCallback", "shadows", "orthographic", "raycastOptions", "cameraOptions", "Raycaster", "params", "options", "isCamera", "Camera", "OrthographicCamera", "PerspectiveCamera", "z", "rotation", "lookAt", "Scene", "handleXRFrame", "frame", "handleSessionChange", "isPresenting", "setAnimationLoop", "connect", "addEventListener", "disconnect", "removeEventListener", "shadowMap", "oldEnabled", "oldType", "PCFSoftShadowMap", "_types$shadows", "types", "basic", "BasicShadowMap", "percentage", "PCFShadowMap", "soft", "variance", "VSMShadowMap", "needsUpdate", "legacyMode", "LinearEncoding", "outputEncoding", "toneMapping", "NoToneMapping", "ACESFilmicToneMapping", "updateContainer", "Provider", "rootElement", "unmount", "unmountComponentAtNode", "config", "_state$gl", "_state$gl$renderLists", "_state$gl2", "_state$gl3", "renderLists", "forceContextLoss", "e", "createPortal", "Portal", "rest", "useState", "inject", "injectState", "usePortalStore", "onMutate", "prev", "setState", "injectIntoDevTools", "bundleType", "rendererPackageName", "version", "createSubs", "subs", "sub", "globalEffects", "globalAfterEffects", "globalTailEffects", "addEffect", "addAfterEffect", "addTail", "run", "effects", "flushGlobalEffects", "subscription", "update", "<PERSON><PERSON><PERSON><PERSON>", "oldTime", "running", "useFrameInProgress", "repeat", "loop", "_state$gl$xr", "cancelAnimationFrame", "_state$gl$xr2", "A", "B", "C", "D", "E", "F", "_", "c", "d", "f", "g", "j", "k", "l", "m", "n", "q", "r", "t", "u", "v"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/@react-three/fiber/dist/loop-ed5edcdb.esm.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { NoEventPriority, DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport { createWithEqualityFn } from 'zustand/traditional';\nimport { useFiber, useContextBridge, traverseFiber } from 'its-fine';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport { suspend, preload, clear } from 'suspend-react';\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\n// TODO: upstream to DefinitelyTyped for React 19\n// https://github.com/facebook/react/issues/28956\n\nconst createReconciler = Reconciler;\n\n// TODO: handle constructor overloads\n// https://github.com/pmndrs/react-three-fiber/pull/2931\n// https://github.com/microsoft/TypeScript/issues/37079\n\nconst catalogue = {};\nlet i = 0;\nconst isConstructor$1 = object => typeof object === 'function';\nfunction extend(objects) {\n  if (isConstructor$1(objects)) {\n    const Component = `${i++}`;\n    catalogue[Component] = objects;\n    return Component;\n  } else {\n    Object.assign(catalogue, objects);\n  }\n}\nfunction validateInstance(type, props) {\n  // Get target from catalogue\n  const name = `${type[0].toUpperCase()}${type.slice(1)}`;\n  const target = catalogue[name];\n\n  // Validate element target\n  if (type !== 'primitive' && !target) throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n\n  // Validate primitives\n  if (type === 'primitive' && !props.object) throw new Error(`R3F: Primitives without 'object' are invalid!`);\n\n  // Throw if an object or literal was passed for args\n  if (props.args !== undefined && !Array.isArray(props.args)) throw new Error('R3F: The args prop must be an array!');\n}\nfunction createInstance(type, props, root) {\n  var _props$object;\n  validateInstance(type, props);\n\n  // Regenerate the R3F instance for primitives to simulate a new object\n  if (type === 'primitive' && (_props$object = props.object) != null && _props$object.__r3f) delete props.object.__r3f;\n  return prepare(props.object, root, type, props);\n}\nfunction hideInstance(instance) {\n  if (!instance.isHidden) {\n    var _instance$parent;\n    if (instance.props.attach && (_instance$parent = instance.parent) != null && _instance$parent.object) {\n      detach(instance.parent, instance);\n    } else if (isObject3D(instance.object)) {\n      instance.object.visible = false;\n    }\n    instance.isHidden = true;\n    invalidateInstance(instance);\n  }\n}\nfunction unhideInstance(instance) {\n  if (instance.isHidden) {\n    var _instance$parent2;\n    if (instance.props.attach && (_instance$parent2 = instance.parent) != null && _instance$parent2.object) {\n      attach(instance.parent, instance);\n    } else if (isObject3D(instance.object) && instance.props.visible !== false) {\n      instance.object.visible = true;\n    }\n    instance.isHidden = false;\n    invalidateInstance(instance);\n  }\n}\n\n// https://github.com/facebook/react/issues/20271\n// This will make sure events and attach are only handled once when trees are complete\nfunction handleContainerEffects(parent, child, beforeChild) {\n  // Bail if tree isn't mounted or parent is not a container.\n  // This ensures that the tree is finalized and React won't discard results to Suspense\n  const state = child.root.getState();\n  if (!parent.parent && parent.object !== state.scene) return;\n\n  // Create & link object on first run\n  if (!child.object) {\n    var _child$props$object, _child$props$args;\n    // Get target from catalogue\n    const name = `${child.type[0].toUpperCase()}${child.type.slice(1)}`;\n    const target = catalogue[name];\n\n    // Create object\n    child.object = (_child$props$object = child.props.object) != null ? _child$props$object : new target(...((_child$props$args = child.props.args) != null ? _child$props$args : []));\n    child.object.__r3f = child;\n\n    // Set initial props\n    applyProps(child.object, child.props);\n  }\n\n  // Append instance\n  if (child.props.attach) {\n    attach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    const childIndex = parent.object.children.indexOf(beforeChild == null ? void 0 : beforeChild.object);\n    if (beforeChild && childIndex !== -1) {\n      child.object.parent = parent.object;\n      parent.object.children.splice(childIndex, 0, child.object);\n      child.object.dispatchEvent({\n        type: 'added'\n      });\n      parent.object.dispatchEvent({\n        type: 'childadded',\n        child: child.object\n      });\n    } else {\n      parent.object.add(child.object);\n    }\n  }\n\n  // Link subtree\n  for (const childInstance of child.children) handleContainerEffects(child, childInstance);\n\n  // Tree was updated, request a frame\n  invalidateInstance(child);\n}\nfunction appendChild(parent, child) {\n  if (!child) return;\n\n  // Link instances\n  child.parent = parent;\n  parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child);\n}\nfunction insertBefore(parent, child, beforeChild) {\n  if (!child || !beforeChild) return;\n\n  // Link instances\n  child.parent = parent;\n  const childIndex = parent.children.indexOf(beforeChild);\n  if (childIndex !== -1) parent.children.splice(childIndex, 0, child);else parent.children.push(child);\n\n  // Attach tree once complete\n  handleContainerEffects(parent, child, beforeChild);\n}\nfunction disposeOnIdle(object) {\n  if (typeof object.dispose === 'function') {\n    const handleDispose = () => {\n      try {\n        object.dispose();\n      } catch {\n        // no-op\n      }\n    };\n\n    // In a testing environment, cleanup immediately\n    if (typeof IS_REACT_ACT_ENVIRONMENT !== 'undefined') handleDispose();\n    // Otherwise, using a real GPU so schedule cleanup to prevent stalls\n    else unstable_scheduleCallback(unstable_IdlePriority, handleDispose);\n  }\n}\nfunction removeChild(parent, child, dispose) {\n  if (!child) return;\n\n  // Unlink instances\n  child.parent = null;\n  const childIndex = parent.children.indexOf(child);\n  if (childIndex !== -1) parent.children.splice(childIndex, 1);\n\n  // Eagerly tear down tree\n  if (child.props.attach) {\n    detach(parent, child);\n  } else if (isObject3D(child.object) && isObject3D(parent.object)) {\n    parent.object.remove(child.object);\n    removeInteractivity(findInitialRoot(child), child.object);\n  }\n\n  // Allow objects to bail out of unmount disposal with dispose={null}\n  const shouldDispose = child.props.dispose !== null && dispose !== false;\n\n  // Recursively remove instance children\n  for (let i = child.children.length - 1; i >= 0; i--) {\n    const node = child.children[i];\n    removeChild(child, node, shouldDispose);\n  }\n  child.children.length = 0;\n\n  // Unlink instance object\n  delete child.object.__r3f;\n\n  // Dispose object whenever the reconciler feels like it.\n  // Never dispose of primitives because their state may be kept outside of React!\n  // In order for an object to be able to dispose it\n  //   - has a dispose method\n  //   - cannot be a <primitive object={...} />\n  //   - cannot be a THREE.Scene, because three has broken its own API\n  if (shouldDispose && child.type !== 'primitive' && child.object.type !== 'Scene') {\n    disposeOnIdle(child.object);\n  }\n\n  // Tree was updated, request a frame for top-level instance\n  if (dispose === undefined) invalidateInstance(child);\n}\nfunction setFiberRef(fiber, publicInstance) {\n  for (const _fiber of [fiber, fiber.alternate]) {\n    if (_fiber !== null) {\n      if (typeof _fiber.ref === 'function') {\n        _fiber.refCleanup == null ? void 0 : _fiber.refCleanup();\n        const cleanup = _fiber.ref(publicInstance);\n        if (typeof cleanup === 'function') _fiber.refCleanup = cleanup;\n      } else if (_fiber.ref) {\n        _fiber.ref.current = publicInstance;\n      }\n    }\n  }\n}\nconst reconstructed = [];\nfunction swapInstances() {\n  // Detach instance\n  for (const [instance] of reconstructed) {\n    const parent = instance.parent;\n    if (parent) {\n      if (instance.props.attach) {\n        detach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.remove(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          detach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.remove(child.object);\n        }\n      }\n    }\n\n    // If the old instance is hidden, we need to unhide it.\n    // React assumes it can discard instances since they're pure for DOM.\n    // This isn't true for us since our lifetimes are impure and longliving.\n    // So, we manually check if an instance was hidden and unhide it.\n    if (instance.isHidden) unhideInstance(instance);\n\n    // Dispose of old object if able\n    if (instance.object.__r3f) delete instance.object.__r3f;\n    if (instance.type !== 'primitive') disposeOnIdle(instance.object);\n  }\n\n  // Update instance\n  for (const [instance, props, fiber] of reconstructed) {\n    instance.props = props;\n    const parent = instance.parent;\n    if (parent) {\n      var _instance$props$objec, _instance$props$args;\n      // Get target from catalogue\n      const name = `${instance.type[0].toUpperCase()}${instance.type.slice(1)}`;\n      const target = catalogue[name];\n\n      // Create object\n      instance.object = (_instance$props$objec = instance.props.object) != null ? _instance$props$objec : new target(...((_instance$props$args = instance.props.args) != null ? _instance$props$args : []));\n      instance.object.__r3f = instance;\n      setFiberRef(fiber, instance.object);\n\n      // Set initial props\n      applyProps(instance.object, instance.props);\n      if (instance.props.attach) {\n        attach(parent, instance);\n      } else if (isObject3D(instance.object) && isObject3D(parent.object)) {\n        parent.object.add(instance.object);\n      }\n      for (const child of instance.children) {\n        if (child.props.attach) {\n          attach(instance, child);\n        } else if (isObject3D(child.object) && isObject3D(instance.object)) {\n          instance.object.add(child.object);\n        }\n      }\n\n      // Tree was updated, request a frame\n      invalidateInstance(instance);\n    }\n  }\n  reconstructed.length = 0;\n}\n\n// Don't handle text instances, warn on undefined behavior\nconst handleTextInstance = () => console.warn('R3F: Text is not allowed in JSX! This could be stray whitespace or characters.');\nconst NO_CONTEXT = {};\nlet currentUpdatePriority = NoEventPriority;\n\n// https://github.com/facebook/react/blob/main/packages/react-reconciler/src/ReactFiberFlags.js\nconst NoFlags = 0;\nconst Update = 4;\nconst reconciler = createReconciler({\n  isPrimaryRenderer: false,\n  warnsIfNotActing: false,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  createInstance,\n  removeChild,\n  appendChild,\n  appendInitialChild: appendChild,\n  insertBefore,\n  appendChildToContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    appendChild(scene, child);\n  },\n  removeChildFromContainer(container, child) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !scene) return;\n    removeChild(scene, child);\n  },\n  insertInContainerBefore(container, child, beforeChild) {\n    const scene = container.getState().scene.__r3f;\n    if (!child || !beforeChild || !scene) return;\n    insertBefore(scene, child, beforeChild);\n  },\n  getRootHostContext: () => NO_CONTEXT,\n  getChildHostContext: () => NO_CONTEXT,\n  commitUpdate(instance, type, oldProps, newProps, fiber) {\n    var _newProps$args, _oldProps$args, _newProps$args2;\n    validateInstance(type, newProps);\n    let reconstruct = false;\n\n    // Reconstruct primitives if object prop changes\n    if (instance.type === 'primitive' && oldProps.object !== newProps.object) reconstruct = true;\n    // Reconstruct instance if args were added or removed\n    else if (((_newProps$args = newProps.args) == null ? void 0 : _newProps$args.length) !== ((_oldProps$args = oldProps.args) == null ? void 0 : _oldProps$args.length)) reconstruct = true;\n    // Reconstruct instance if args were changed\n    else if ((_newProps$args2 = newProps.args) != null && _newProps$args2.some((value, index) => {\n      var _oldProps$args2;\n      return value !== ((_oldProps$args2 = oldProps.args) == null ? void 0 : _oldProps$args2[index]);\n    })) reconstruct = true;\n\n    // Reconstruct when args or <primitive object={...} have changes\n    if (reconstruct) {\n      reconstructed.push([instance, {\n        ...newProps\n      }, fiber]);\n    } else {\n      // Create a diff-set, flag if there are any changes\n      const changedProps = diffProps(instance, newProps);\n      if (Object.keys(changedProps).length) {\n        Object.assign(instance.props, changedProps);\n        applyProps(instance.object, changedProps);\n      }\n    }\n\n    // Flush reconstructed siblings when we hit the last updated child in a sequence\n    const isTailSibling = fiber.sibling === null || (fiber.flags & Update) === NoFlags;\n    if (isTailSibling) swapInstances();\n  },\n  finalizeInitialChildren: () => false,\n  commitMount() {},\n  getPublicInstance: instance => instance == null ? void 0 : instance.object,\n  prepareForCommit: () => null,\n  preparePortalMount: container => prepare(container.getState().scene, container, '', {}),\n  resetAfterCommit: () => {},\n  shouldSetTextContent: () => false,\n  clearContainer: () => false,\n  hideInstance,\n  unhideInstance,\n  createTextInstance: handleTextInstance,\n  hideTextInstance: handleTextInstance,\n  unhideTextInstance: handleTextInstance,\n  scheduleTimeout: typeof setTimeout === 'function' ? setTimeout : undefined,\n  cancelTimeout: typeof clearTimeout === 'function' ? clearTimeout : undefined,\n  noTimeout: -1,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur() {},\n  afterActiveInstanceBlur() {},\n  detachDeletedInstance() {},\n  prepareScopeUpdate() {},\n  getInstanceFromScope: () => null,\n  shouldAttemptEagerTransition() {\n    return false;\n  },\n  requestPostPaintCallback() {},\n  maySuspendCommit() {\n    return false;\n  },\n  preloadInstance() {\n    return true; // true indicates already loaded\n  },\n  startSuspendingCommit() {},\n  suspendInstance() {},\n  waitForCommitToBeReady() {\n    return null;\n  },\n  NotPendingTransition: null,\n  setCurrentUpdatePriority(newPriority) {\n    currentUpdatePriority = newPriority;\n  },\n  getCurrentUpdatePriority() {\n    return currentUpdatePriority;\n  },\n  resolveUpdatePriority() {\n    var _window$event;\n    if (currentUpdatePriority !== NoEventPriority) return currentUpdatePriority;\n    switch (typeof window !== 'undefined' && ((_window$event = window.event) == null ? void 0 : _window$event.type)) {\n      case 'click':\n      case 'contextmenu':\n      case 'dblclick':\n      case 'pointercancel':\n      case 'pointerdown':\n      case 'pointerup':\n        return DiscreteEventPriority;\n      case 'pointermove':\n      case 'pointerout':\n      case 'pointerover':\n      case 'pointerenter':\n      case 'pointerleave':\n      case 'wheel':\n        return ContinuousEventPriority;\n      default:\n        return DefaultEventPriority;\n    }\n  },\n  resetFormInstance() {}\n});\n\nvar _window$document, _window$navigator;\n/**\r\n * Returns the instance's initial (outmost) root.\r\n */\nfunction findInitialRoot(instance) {\n  let root = instance.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\n/**\r\n * Safely flush async effects when testing, simulating a legacy root.\r\n */\nconst act = React.act;\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\n/**\r\n * Bridges renderer Context and StrictMode from a primary renderer.\r\n */\nfunction useBridge() {\n  const fiber = useFiber();\n  const ContextBridge = useContextBridge();\n  return React.useMemo(() => ({\n    children\n  }) => {\n    const strict = !!traverseFiber(fiber, true, node => node.type === React.StrictMode);\n    const Root = strict ? React.StrictMode : React.Fragment;\n    return /*#__PURE__*/jsx(Root, {\n      children: /*#__PURE__*/jsx(ContextBridge, {\n        children: children\n      })\n    });\n  }, [fiber, ContextBridge]);\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nfunction getRootState(obj) {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n// Collects nodes and materials from a THREE.Object3D\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.type !== 'Scene') obj.dispose == null ? void 0 : obj.dispose();\n  for (const p in obj) {\n    const prop = obj[p];\n    if ((prop == null ? void 0 : prop.type) !== 'Scene') prop == null ? void 0 : prop.dispose == null ? void 0 : prop.dispose();\n  }\n}\nconst REACT_INTERNAL_PROPS = ['children', 'key', 'ref'];\n\n// Gets only instance props from reconciler fibers\nfunction getInstanceProps(queue) {\n  const props = {};\n  for (const key in queue) {\n    if (!REACT_INTERNAL_PROPS.includes(key)) props[key] = queue[key];\n  }\n  return props;\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(target, root, type, props) {\n  const object = target;\n\n  // Create instance descriptor\n  let instance = object == null ? void 0 : object.__r3f;\n  if (!instance) {\n    instance = {\n      root,\n      type,\n      parent: null,\n      children: [],\n      props: getInstanceProps(props),\n      object,\n      eventCount: 0,\n      handlers: {},\n      isHidden: false\n    };\n    if (object) {\n      object.__r3f = instance;\n      if (type) applyProps(object, instance.props);\n    }\n  }\n  return instance;\n}\nfunction resolve(root, key) {\n  var _target;\n  let target = root[key];\n  if (!key.includes('-')) return {\n    root,\n    key,\n    target\n  };\n\n  // Resolve pierced target\n  const chain = key.split('-');\n  target = chain.reduce((acc, key) => acc[key], root);\n  key = chain.pop();\n\n  // Switch root if atomic\n  if (!((_target = target) != null && _target.set)) root = chain.reduce((acc, key) => acc[key], root);\n  return {\n    root,\n    key,\n    target\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child) {\n  if (is.str(child.props.attach)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(child.props.attach)) {\n      const index = child.props.attach.replace(INDEX_REGEX, '');\n      const {\n        root,\n        key\n      } = resolve(parent.object, index);\n      if (!Array.isArray(root[key])) root[key] = [];\n    }\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    child.previousAttach = root[key];\n    root[key] = child.object;\n  } else if (is.fun(child.props.attach)) {\n    child.previousAttach = child.props.attach(parent.object, child.object);\n  }\n}\nfunction detach(parent, child) {\n  if (is.str(child.props.attach)) {\n    const {\n      root,\n      key\n    } = resolve(parent.object, child.props.attach);\n    const previous = child.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete root[key];\n    // Otherwise set the previous value\n    else root[key] = previous;\n  } else {\n    child.previousAttach == null ? void 0 : child.previousAttach(parent.object, child.object);\n  }\n  delete child.previousAttach;\n}\nconst RESERVED_PROPS = [...REACT_INTERNAL_PROPS,\n// Instance props\n'args', 'dispose', 'attach', 'object', 'onUpdate',\n// Behavior flags\n'dispose'];\nconst MEMOIZED_PROTOTYPES = new Map();\n\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, newProps) {\n  const changedProps = {};\n\n  // Sort through props\n  for (const prop in newProps) {\n    // Skip reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n    // Skip if props match\n    if (is.equ(newProps[prop], instance.props[prop])) continue;\n\n    // Props changed, add them\n    changedProps[prop] = newProps[prop];\n\n    // Reset pierced props\n    for (const other in newProps) {\n      if (other.startsWith(`${prop}-`)) changedProps[other] = newProps[other];\n    }\n  }\n\n  // Reset removed props for HMR\n  for (const prop in instance.props) {\n    if (RESERVED_PROPS.includes(prop) || newProps.hasOwnProperty(prop)) continue;\n    const {\n      root,\n      key\n    } = resolve(instance.object, prop);\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (root.constructor && root.constructor.length === 0) {\n      // create a blank slate of the instance and copy the particular parameter.\n      let ctor = MEMOIZED_PROTOTYPES.get(root.constructor);\n      if (!ctor) {\n        ctor = new root.constructor();\n        MEMOIZED_PROTOTYPES.set(root.constructor, ctor);\n      }\n      changedProps[key] = ctor[key];\n    } else {\n      // instance does not have constructor, just set it to 0\n      changedProps[key] = 0;\n    }\n  }\n  return changedProps;\n}\nconst __DEV__ = typeof process !== 'undefined' && process.env.NODE_ENV !== 'production';\n\n// const LinearEncoding = 3000\nconst sRGBEncoding = 3001;\nconst SRGBColorSpace = 'srgb';\nconst LinearSRGBColorSpace = 'srgb-linear';\n\n// https://github.com/mrdoob/three.js/pull/27042\n// https://github.com/mrdoob/three.js/pull/22748\nconst colorMaps = ['map', 'emissiveMap', 'sheenTintMap',\n// <r134\n'sheenColorMap', 'specularTintMap',\n// <r134\n'specularColorMap', 'envMap'];\nconst EVENT_REGEX = /^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;\n\n// This function applies a set of changes to the instance\nfunction applyProps(object, props) {\n  const instance = object.__r3f;\n  const rootState = instance && findInitialRoot(instance).getState();\n  const prevHandlers = instance == null ? void 0 : instance.eventCount;\n  for (const prop in props) {\n    let value = props[prop];\n\n    // Don't mutate reserved keys\n    if (RESERVED_PROPS.includes(prop)) continue;\n\n    // Deal with pointer events, including removing them if undefined\n    if (instance && EVENT_REGEX.test(prop)) {\n      if (typeof value === 'function') instance.handlers[prop] = value;else delete instance.handlers[prop];\n      instance.eventCount = Object.keys(instance.handlers).length;\n    }\n\n    // Ignore setting undefined props\n    // https://github.com/pmndrs/react-three-fiber/issues/274\n    if (value === undefined) continue;\n    let {\n      root,\n      key,\n      target\n    } = resolve(object, prop);\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(root)) {\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n\n    // Copy if properties match signatures\n    if (target != null && target.copy && (\n    // Some environments may break strict identity checks by duplicating versions of three.js.\n    // Loosen to unminified names, ignoring descendents.\n    // https://github.com/pmndrs/react-three-fiber/issues/2856\n    // TODO: fix upstream and remove in v9\n    __DEV__ ? target.constructor.name === value.constructor.name : target.constructor === value.constructor)) {\n      target.copy(value);\n    }\n    // Layers have no copy function, we must therefore copy the mask property\n    else if (target instanceof THREE.Layers && value instanceof THREE.Layers) {\n      target.mask = value.mask;\n    }\n    // Set array types\n    else if (target != null && target.set && Array.isArray(value)) {\n      if (target.fromArray) target.fromArray(value);else target.set(...value);\n    }\n    // Set literal types\n    else if (target != null && target.set && typeof value !== 'object') {\n      const isColor = target instanceof THREE.Color;\n      // Allow setting array scalars\n      if (!isColor && target.setScalar && typeof value === 'number') target.setScalar(value);\n      // Otherwise just set single value\n      else target.set(value);\n\n      // Emulate THREE.ColorManagement for older three.js versions\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if (!getColorManagement() && !(rootState != null && rootState.linear) && isColor) target.convertSRGBToLinear();\n    }\n    // Else, just overwrite the value\n    else {\n      root[key] = value;\n\n      // Auto-convert sRGB texture parameters for built-in materials\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      // https://github.com/mrdoob/three.js/pull/25857\n      if (rootState && !rootState.linear && colorMaps.includes(key) && root[key] instanceof THREE.Texture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      root[key].format === THREE.RGBAFormat && root[key].type === THREE.UnsignedByteType) {\n        // NOTE: this cannot be set from the renderer (e.g. sRGB source textures rendered to P3)\n        if (hasColorSpace(root[key])) root[key].colorSpace = 'srgb';else root[key].encoding = sRGBEncoding;\n      }\n    }\n  }\n\n  // Register event handlers\n  if (instance != null && instance.parent && rootState != null && rootState.internal && instance.object instanceof THREE.Object3D && prevHandlers !== instance.eventCount) {\n    // Pre-emptively remove the instance from the interaction manager\n    const index = rootState.internal.interaction.indexOf(instance.object);\n    if (index > -1) rootState.internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (instance.eventCount && instance.object.raycast !== null && instance.object instanceof THREE.Object3D) {\n      rootState.internal.interaction.push(instance.object);\n    }\n  }\n\n  // Auto-attach geometries and materials\n  if (instance && instance.props.attach === undefined) {\n    if (instance.object instanceof THREE.BufferGeometry) instance.props.attach = 'geometry';else if (instance.object instanceof THREE.Material) instance.props.attach = 'material';\n  }\n\n  // Instance was updated, request a frame\n  if (instance) invalidateInstance(instance);\n  return object;\n}\nfunction invalidateInstance(instance) {\n  var _instance$root;\n  if (!instance.parent) return;\n  instance.props.onUpdate == null ? void 0 : instance.props.onUpdate(instance.object);\n  const state = (_instance$root = instance.root) == null ? void 0 : _instance$root.getState == null ? void 0 : _instance$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateCamera(camera, size) {\n  // Do not mess with the camera if it belongs to the user\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  if (camera.manual) return;\n  if (isOrthographicCamera(camera)) {\n    camera.left = size.width / -2;\n    camera.right = size.width / 2;\n    camera.top = size.height / 2;\n    camera.bottom = size.height / -2;\n  } else {\n    camera.aspect = size.width / size.height;\n  }\n  camera.updateProjectionMatrix();\n}\nconst isObject3D = object => object == null ? void 0 : object.isObject3D;\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object);\n        if (state) {\n          const {\n            raycaster,\n            pointer,\n            camera,\n            internal\n          } = state;\n          const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n          const hasPointerCapture = id => {\n            var _internal$capturedMap, _internal$capturedMap2;\n            return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n          };\n          const setPointerCapture = id => {\n            const captureData = {\n              intersection: hit,\n              target: event.target\n            };\n            if (internal.capturedMap.has(id)) {\n              // if the pointerId was previously captured, we add the hit to the\n              // event capturedMap.\n              internal.capturedMap.get(id).set(hit.eventObject, captureData);\n            } else {\n              // if the pointerId was not previously captured, we create a map\n              // containing the hitObject, and the hit. hitObject is used for\n              // faster access.\n              internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n            }\n            event.target.setPointerCapture(id);\n          };\n          const releasePointerCapture = id => {\n            const captures = internal.capturedMap.get(id);\n            if (captures) {\n              releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n            }\n          };\n\n          // Add native event props\n          let extractEventProps = {};\n          // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n          for (let prop in event) {\n            let property = event[prop];\n            // Only copy over atomics, leave functions alone as these should be\n            // called as event.nativeEvent.fn()\n            if (typeof property !== 'function') extractEventProps[prop] = property;\n          }\n          let raycastEvent = {\n            ...hit,\n            ...extractEventProps,\n            pointer,\n            intersections,\n            stopped: localState.stopped,\n            delta,\n            unprojectedPoint,\n            ray: raycaster.ray,\n            camera: camera,\n            // Hijack stopPropagation, which just sets a flag\n            stopPropagation() {\n              // https://github.com/pmndrs/react-three-fiber/issues/596\n              // Events are not allowed to stop propagation if the pointer has been captured\n              const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n              // We only authorize stopPropagation...\n              if (\n              // ...if this pointer hasn't been captured\n              !capturesForPointer ||\n              // ... or if the hit object is capturing the pointer\n              capturesForPointer.has(hit.eventObject)) {\n                raycastEvent.stopped = localState.stopped = true;\n                // Propagation is stopped, remove all other hover records\n                // An event handler is only allowed to flush other handlers if it is hovered itself\n                if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                  // Objects cannot flush out higher up objects that have already caught the event\n                  const higher = intersections.slice(0, intersections.indexOf(hit));\n                  cancelPointer([...higher, hit]);\n                }\n              }\n            },\n            // there should be a distinction between target and currentTarget\n            target: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            currentTarget: {\n              hasPointerCapture,\n              setPointerCapture,\n              releasePointerCapture\n            },\n            nativeEvent: event\n          };\n\n          // Call subscribers\n          callback(raycastEvent);\n          // Event bubbling may be interrupted by stopPropagation\n          if (localState.stopped === true) break;\n        }\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          const handlers = instance.handlers;\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n        const handlers = instance.handlers;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootStore = createWithEqualityFn((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target instanceof THREE.Vector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      scene: null,\n      xr: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, top = 0, left = 0) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top,\n          left\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        // Events\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        lastEvent: /*#__PURE__*/React.createRef(),\n        // Updates\n        active: false,\n        frames: 0,\n        priority: 0,\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootStore.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootStore.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootStore.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootStore.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootStore;\n};\n\n/**\r\n * Exposes an object's {@link Instance}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useinstancehandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  React.useImperativeHandle(instance, () => ref.current.__r3f, [ref]);\n  return instance;\n}\n\n/**\r\n * Returns the R3F Canvas' Zustand store. Useful for [transient updates](https://github.com/pmndrs/zustand#transient-updates-for-often-occurring-state-changes).\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usestore\r\n */\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nconst isConstructor = value => {\n  var _value$prototype;\n  return typeof value === 'function' && (value == null ? void 0 : (_value$prototype = value.prototype) == null ? void 0 : _value$prototype.constructor) === value;\n};\nfunction loadingFn(extensions, onProgress) {\n  return async function (Proto, ...input) {\n    let loader;\n\n    // Construct and cache loader if constructor was passed\n    if (isConstructor(Proto)) {\n      loader = memoizedLoaders.get(Proto);\n      if (!loader) {\n        loader = new Proto();\n        memoizedLoaders.set(Proto, loader);\n      }\n    } else {\n      loader = Proto;\n    }\n\n    // Apply loader extensions\n    if (extensions) extensions(loader);\n\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => res(isObject3D(data == null ? void 0 : data.scene) ? Object.assign(data, buildGraph(data.scene)) : data), onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(loader, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [loader, ...keys], {\n    equal: is.equ\n  });\n  // Return the object(s)\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (loader, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [loader, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (loader, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([loader, ...keys]);\n};\n\nconst _roots = new Map();\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;\n  return new THREE.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, size) {\n  if (!size && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left\n    };\n  } else if (!size && typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    ...size\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = _roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store,\n  // container\n  ConcurrentRoot,\n  // tag\n  null,\n  // hydration callbacks\n  false,\n  // isStrictMode\n  null,\n  // concurrentUpdatesByDefaultOverride\n  '',\n  // identifierPrefix\n  logRecoverableError,\n  // onUncaughtError\n  logRecoverableError,\n  // onCaughtError\n  logRecoverableError,\n  // onRecoverableError\n  null // transitionCallbacks\n  );\n  // Map it\n  if (!prevRoot) _roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof THREE.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if (!camera.manual) {\n              if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n                camera.manual = true;\n                camera.updateProjectionMatrix();\n              }\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions instanceof THREE.Scene) {\n          scene = sceneOptions;\n          prepare(scene, store, '', {});\n        } else {\n          scene = new THREE.Scene();\n          prepare(scene, store, '', {});\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (gl.xr) xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n\n      // Set color space and tonemapping preferences\n      if (!configured) {\n        const LinearEncoding = 3000;\n        const sRGBEncoding = 3001;\n        applyProps(gl, {\n          outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n          toneMapping: flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping\n        });\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer( /*#__PURE__*/jsx(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = _roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state.scene);\n            _roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  });\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = useMutableCallback((rootState, injectState) => {\n    let viewport = undefined;\n    if (injectState.camera && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...rootState,\n      ...injectState,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...injectState.events,\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      // Layers are allowed to override events\n      setEvents: events => injectState.set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    };\n  });\n  const usePortalStore = React.useMemo(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const store = createWithEqualityFn((set, get) => ({\n      ...rest,\n      set,\n      get\n    }));\n\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const onMutate = prev => store.setState(state => inject.current(prev, state));\n    onMutate(previousRoot.getState());\n    previousRoot.subscribe(onMutate);\n    return store;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [previousRoot, container]);\n  return /*#__PURE__*/jsx(Fragment, {\n    children: reconciler.createPortal( /*#__PURE__*/jsx(context.Provider, {\n      value: usePortalStore,\n      children: children\n    }), usePortalStore, null)\n  });\n}\nreconciler.injectIntoDevTools({\n  bundleType: process.env.NODE_ENV === 'production' ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: React.version\n});\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nconst globalEffects = new Set();\nconst globalAfterEffects = new Set();\nconst globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction update(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (let i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nlet running = false;\nlet useFrameInProgress = false;\nlet repeat;\nlet frame;\nlet state;\nfunction loop(timestamp) {\n  frame = requestAnimationFrame(loop);\n  running = true;\n  repeat = 0;\n\n  // Run effects\n  flushGlobalEffects('before', timestamp);\n\n  // Render all roots\n  useFrameInProgress = true;\n  for (const root of _roots.values()) {\n    var _state$gl$xr;\n    state = root.store.getState();\n\n    // If the frameloop is invalidated, do not run another frame\n    if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n      repeat += update(timestamp, state);\n    }\n  }\n  useFrameInProgress = true;\n\n  // Run after-effects\n  flushGlobalEffects('after', timestamp);\n\n  // Stop the loop if nothing invalidates it\n  if (repeat === 0) {\n    // Tail call effects, they are called when rendering stops\n    flushGlobalEffects('tail', timestamp);\n\n    // Flag end of operation\n    running = false;\n    return cancelAnimationFrame(frame);\n  }\n}\n\n/**\r\n * Invalidates the view, requesting a frame to be rendered. Will globally invalidate unless passed a root's state.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#invalidate\r\n */\nfunction invalidate(state, frames = 1) {\n  var _state$gl$xr2;\n  if (!state) return _roots.forEach(root => invalidate(root.store.getState(), frames));\n  if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n  if (frames > 1) {\n    // legacy support for people using frames parameters\n    // Increase frames, do not go higher than 60\n    state.internal.frames = Math.min(60, state.internal.frames + frames);\n  } else {\n    if (useFrameInProgress) {\n      //called from within a useFrame, it means the user wants an additional frame\n      state.internal.frames = 2;\n    } else {\n      //the user need a new frame, no need to increment further than 1\n      state.internal.frames = 1;\n    }\n  }\n\n  // If the render-loop isn't active, start it\n  if (!running) {\n    running = true;\n    requestAnimationFrame(loop);\n  }\n}\n\n/**\r\n * Advances the frameloop and runs render effects, useful for when manually rendering via `frameloop=\"never\"`.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#advance\r\n */\nfunction advance(timestamp, runGlobalEffects = true, state, frame) {\n  if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n  if (!state) for (const root of _roots.values()) update(timestamp, root.store.getState());else update(timestamp, state, frame);\n  if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n}\n\nexport { useThree as A, Block as B, useFrame as C, useGraph as D, ErrorBoundary as E, useLoader as F, _roots as _, useMutableCallback as a, useIsomorphicLayoutEffect as b, createEvents as c, createRoot as d, extend as e, unmountComponentAtNode as f, flushGlobalEffects as g, addEffect as h, isRef as i, addAfterEffect as j, addTail as k, invalidate as l, advance as m, render as n, createPortal as o, context as p, applyProps as q, reconciler as r, getRootState as s, threeTypes as t, useBridge as u, dispose as v, act as w, buildGraph as x, useInstanceHandle as y, useStore as z };\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,EAAEC,oBAAoB,EAAEC,uBAAuB,EAAEC,qBAAqB,EAAEC,cAAc,QAAQ,4BAA4B;AAClJ,SAASC,oBAAoB,QAAQ,qBAAqB;AAC1D,SAASC,QAAQ,EAAEC,gBAAgB,EAAEC,aAAa,QAAQ,UAAU;AACpE,OAAOC,UAAU,MAAM,kBAAkB;AACzC,SAASC,yBAAyB,EAAEC,qBAAqB,QAAQ,WAAW;AAC5E,SAASC,GAAG,EAAEC,QAAQ,QAAQ,mBAAmB;AACjD,SAASC,OAAO,EAAEC,OAAO,EAAEC,KAAK,QAAQ,eAAe;AAEvD,IAAIC,UAAU,GAAG,aAAaC,MAAM,CAACC,MAAM,CAAC;EAC1CC,SAAS,EAAE;AACb,CAAC,CAAC;;AAEF;AACA;;AAEA,MAAMC,gBAAgB,GAAGZ,UAAU;;AAEnC;AACA;AACA;;AAEA,MAAMa,SAAS,GAAG,CAAC,CAAC;AACpB,IAAIC,CAAC,GAAG,CAAC;AACT,MAAMC,eAAe,GAAGC,MAAM,IAAI,OAAOA,MAAM,KAAK,UAAU;AAC9D,SAASC,MAAMA,CAACC,OAAO,EAAE;EACvB,IAAIH,eAAe,CAACG,OAAO,CAAC,EAAE;IAC5B,MAAMC,SAAS,GAAG,GAAGL,CAAC,EAAE,EAAE;IAC1BD,SAAS,CAACM,SAAS,CAAC,GAAGD,OAAO;IAC9B,OAAOC,SAAS;EAClB,CAAC,MAAM;IACLV,MAAM,CAACW,MAAM,CAACP,SAAS,EAAEK,OAAO,CAAC;EACnC;AACF;AACA,SAASG,gBAAgBA,CAACC,IAAI,EAAEC,KAAK,EAAE;EACrC;EACA,MAAMC,IAAI,GAAG,GAAGF,IAAI,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,GAAGH,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;EACvD,MAAMC,MAAM,GAAGd,SAAS,CAACW,IAAI,CAAC;;EAE9B;EACA,IAAIF,IAAI,KAAK,WAAW,IAAI,CAACK,MAAM,EAAE,MAAM,IAAIC,KAAK,CAAC,QAAQJ,IAAI,8JAA8J,CAAC;;EAEhO;EACA,IAAIF,IAAI,KAAK,WAAW,IAAI,CAACC,KAAK,CAACP,MAAM,EAAE,MAAM,IAAIY,KAAK,CAAC,+CAA+C,CAAC;;EAE3G;EACA,IAAIL,KAAK,CAACM,IAAI,KAAKC,SAAS,IAAI,CAACC,KAAK,CAACC,OAAO,CAACT,KAAK,CAACM,IAAI,CAAC,EAAE,MAAM,IAAID,KAAK,CAAC,sCAAsC,CAAC;AACrH;AACA,SAASK,cAAcA,CAACX,IAAI,EAAEC,KAAK,EAAEW,IAAI,EAAE;EACzC,IAAIC,aAAa;EACjBd,gBAAgB,CAACC,IAAI,EAAEC,KAAK,CAAC;;EAE7B;EACA,IAAID,IAAI,KAAK,WAAW,IAAI,CAACa,aAAa,GAAGZ,KAAK,CAACP,MAAM,KAAK,IAAI,IAAImB,aAAa,CAACC,KAAK,EAAE,OAAOb,KAAK,CAACP,MAAM,CAACoB,KAAK;EACpH,OAAOC,OAAO,CAACd,KAAK,CAACP,MAAM,EAAEkB,IAAI,EAAEZ,IAAI,EAAEC,KAAK,CAAC;AACjD;AACA,SAASe,YAAYA,CAACC,QAAQ,EAAE;EAC9B,IAAI,CAACA,QAAQ,CAACC,QAAQ,EAAE;IACtB,IAAIC,gBAAgB;IACpB,IAAIF,QAAQ,CAAChB,KAAK,CAACmB,MAAM,IAAI,CAACD,gBAAgB,GAAGF,QAAQ,CAACI,MAAM,KAAK,IAAI,IAAIF,gBAAgB,CAACzB,MAAM,EAAE;MACpG4B,MAAM,CAACL,QAAQ,CAACI,MAAM,EAAEJ,QAAQ,CAAC;IACnC,CAAC,MAAM,IAAIM,UAAU,CAACN,QAAQ,CAACvB,MAAM,CAAC,EAAE;MACtCuB,QAAQ,CAACvB,MAAM,CAAC8B,OAAO,GAAG,KAAK;IACjC;IACAP,QAAQ,CAACC,QAAQ,GAAG,IAAI;IACxBO,kBAAkB,CAACR,QAAQ,CAAC;EAC9B;AACF;AACA,SAASS,cAAcA,CAACT,QAAQ,EAAE;EAChC,IAAIA,QAAQ,CAACC,QAAQ,EAAE;IACrB,IAAIS,iBAAiB;IACrB,IAAIV,QAAQ,CAAChB,KAAK,CAACmB,MAAM,IAAI,CAACO,iBAAiB,GAAGV,QAAQ,CAACI,MAAM,KAAK,IAAI,IAAIM,iBAAiB,CAACjC,MAAM,EAAE;MACtG0B,MAAM,CAACH,QAAQ,CAACI,MAAM,EAAEJ,QAAQ,CAAC;IACnC,CAAC,MAAM,IAAIM,UAAU,CAACN,QAAQ,CAACvB,MAAM,CAAC,IAAIuB,QAAQ,CAAChB,KAAK,CAACuB,OAAO,KAAK,KAAK,EAAE;MAC1EP,QAAQ,CAACvB,MAAM,CAAC8B,OAAO,GAAG,IAAI;IAChC;IACAP,QAAQ,CAACC,QAAQ,GAAG,KAAK;IACzBO,kBAAkB,CAACR,QAAQ,CAAC;EAC9B;AACF;;AAEA;AACA;AACA,SAASW,sBAAsBA,CAACP,MAAM,EAAEQ,KAAK,EAAEC,WAAW,EAAE;EAC1D;EACA;EACA,MAAMC,KAAK,GAAGF,KAAK,CAACjB,IAAI,CAACoB,QAAQ,CAAC,CAAC;EACnC,IAAI,CAACX,MAAM,CAACA,MAAM,IAAIA,MAAM,CAAC3B,MAAM,KAAKqC,KAAK,CAACE,KAAK,EAAE;;EAErD;EACA,IAAI,CAACJ,KAAK,CAACnC,MAAM,EAAE;IACjB,IAAIwC,mBAAmB,EAAEC,iBAAiB;IAC1C;IACA,MAAMjC,IAAI,GAAG,GAAG2B,KAAK,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,GAAG0B,KAAK,CAAC7B,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;IACnE,MAAMC,MAAM,GAAGd,SAAS,CAACW,IAAI,CAAC;;IAE9B;IACA2B,KAAK,CAACnC,MAAM,GAAG,CAACwC,mBAAmB,GAAGL,KAAK,CAAC5B,KAAK,CAACP,MAAM,KAAK,IAAI,GAAGwC,mBAAmB,GAAG,IAAI7B,MAAM,CAAC,IAAI,CAAC8B,iBAAiB,GAAGN,KAAK,CAAC5B,KAAK,CAACM,IAAI,KAAK,IAAI,GAAG4B,iBAAiB,GAAG,EAAE,CAAC,CAAC;IAClLN,KAAK,CAACnC,MAAM,CAACoB,KAAK,GAAGe,KAAK;;IAE1B;IACAO,UAAU,CAACP,KAAK,CAACnC,MAAM,EAAEmC,KAAK,CAAC5B,KAAK,CAAC;EACvC;;EAEA;EACA,IAAI4B,KAAK,CAAC5B,KAAK,CAACmB,MAAM,EAAE;IACtBA,MAAM,CAACC,MAAM,EAAEQ,KAAK,CAAC;EACvB,CAAC,MAAM,IAAIN,UAAU,CAACM,KAAK,CAACnC,MAAM,CAAC,IAAI6B,UAAU,CAACF,MAAM,CAAC3B,MAAM,CAAC,EAAE;IAChE,MAAM2C,UAAU,GAAGhB,MAAM,CAAC3B,MAAM,CAAC4C,QAAQ,CAACC,OAAO,CAACT,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACpC,MAAM,CAAC;IACpG,IAAIoC,WAAW,IAAIO,UAAU,KAAK,CAAC,CAAC,EAAE;MACpCR,KAAK,CAACnC,MAAM,CAAC2B,MAAM,GAAGA,MAAM,CAAC3B,MAAM;MACnC2B,MAAM,CAAC3B,MAAM,CAAC4C,QAAQ,CAACE,MAAM,CAACH,UAAU,EAAE,CAAC,EAAER,KAAK,CAACnC,MAAM,CAAC;MAC1DmC,KAAK,CAACnC,MAAM,CAAC+C,aAAa,CAAC;QACzBzC,IAAI,EAAE;MACR,CAAC,CAAC;MACFqB,MAAM,CAAC3B,MAAM,CAAC+C,aAAa,CAAC;QAC1BzC,IAAI,EAAE,YAAY;QAClB6B,KAAK,EAAEA,KAAK,CAACnC;MACf,CAAC,CAAC;IACJ,CAAC,MAAM;MACL2B,MAAM,CAAC3B,MAAM,CAACgD,GAAG,CAACb,KAAK,CAACnC,MAAM,CAAC;IACjC;EACF;;EAEA;EACA,KAAK,MAAMiD,aAAa,IAAId,KAAK,CAACS,QAAQ,EAAEV,sBAAsB,CAACC,KAAK,EAAEc,aAAa,CAAC;;EAExF;EACAlB,kBAAkB,CAACI,KAAK,CAAC;AAC3B;AACA,SAASe,WAAWA,CAACvB,MAAM,EAAEQ,KAAK,EAAE;EAClC,IAAI,CAACA,KAAK,EAAE;;EAEZ;EACAA,KAAK,CAACR,MAAM,GAAGA,MAAM;EACrBA,MAAM,CAACiB,QAAQ,CAACO,IAAI,CAAChB,KAAK,CAAC;;EAE3B;EACAD,sBAAsB,CAACP,MAAM,EAAEQ,KAAK,CAAC;AACvC;AACA,SAASiB,YAAYA,CAACzB,MAAM,EAAEQ,KAAK,EAAEC,WAAW,EAAE;EAChD,IAAI,CAACD,KAAK,IAAI,CAACC,WAAW,EAAE;;EAE5B;EACAD,KAAK,CAACR,MAAM,GAAGA,MAAM;EACrB,MAAMgB,UAAU,GAAGhB,MAAM,CAACiB,QAAQ,CAACC,OAAO,CAACT,WAAW,CAAC;EACvD,IAAIO,UAAU,KAAK,CAAC,CAAC,EAAEhB,MAAM,CAACiB,QAAQ,CAACE,MAAM,CAACH,UAAU,EAAE,CAAC,EAAER,KAAK,CAAC,CAAC,KAAKR,MAAM,CAACiB,QAAQ,CAACO,IAAI,CAAChB,KAAK,CAAC;;EAEpG;EACAD,sBAAsB,CAACP,MAAM,EAAEQ,KAAK,EAAEC,WAAW,CAAC;AACpD;AACA,SAASiB,aAAaA,CAACrD,MAAM,EAAE;EAC7B,IAAI,OAAOA,MAAM,CAACsD,OAAO,KAAK,UAAU,EAAE;IACxC,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI;QACFvD,MAAM,CAACsD,OAAO,CAAC,CAAC;MAClB,CAAC,CAAC,MAAM;QACN;MAAA;IAEJ,CAAC;;IAED;IACA,IAAI,OAAOE,wBAAwB,KAAK,WAAW,EAAED,aAAa,CAAC,CAAC;IACpE;IAAA,KACKtE,yBAAyB,CAACC,qBAAqB,EAAEqE,aAAa,CAAC;EACtE;AACF;AACA,SAASE,WAAWA,CAAC9B,MAAM,EAAEQ,KAAK,EAAEmB,OAAO,EAAE;EAC3C,IAAI,CAACnB,KAAK,EAAE;;EAEZ;EACAA,KAAK,CAACR,MAAM,GAAG,IAAI;EACnB,MAAMgB,UAAU,GAAGhB,MAAM,CAACiB,QAAQ,CAACC,OAAO,CAACV,KAAK,CAAC;EACjD,IAAIQ,UAAU,KAAK,CAAC,CAAC,EAAEhB,MAAM,CAACiB,QAAQ,CAACE,MAAM,CAACH,UAAU,EAAE,CAAC,CAAC;;EAE5D;EACA,IAAIR,KAAK,CAAC5B,KAAK,CAACmB,MAAM,EAAE;IACtBE,MAAM,CAACD,MAAM,EAAEQ,KAAK,CAAC;EACvB,CAAC,MAAM,IAAIN,UAAU,CAACM,KAAK,CAACnC,MAAM,CAAC,IAAI6B,UAAU,CAACF,MAAM,CAAC3B,MAAM,CAAC,EAAE;IAChE2B,MAAM,CAAC3B,MAAM,CAAC0D,MAAM,CAACvB,KAAK,CAACnC,MAAM,CAAC;IAClC2D,mBAAmB,CAACC,eAAe,CAACzB,KAAK,CAAC,EAAEA,KAAK,CAACnC,MAAM,CAAC;EAC3D;;EAEA;EACA,MAAM6D,aAAa,GAAG1B,KAAK,CAAC5B,KAAK,CAAC+C,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK;;EAEvE;EACA,KAAK,IAAIxD,CAAC,GAAGqC,KAAK,CAACS,QAAQ,CAACkB,MAAM,GAAG,CAAC,EAAEhE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnD,MAAMiE,IAAI,GAAG5B,KAAK,CAACS,QAAQ,CAAC9C,CAAC,CAAC;IAC9B2D,WAAW,CAACtB,KAAK,EAAE4B,IAAI,EAAEF,aAAa,CAAC;EACzC;EACA1B,KAAK,CAACS,QAAQ,CAACkB,MAAM,GAAG,CAAC;;EAEzB;EACA,OAAO3B,KAAK,CAACnC,MAAM,CAACoB,KAAK;;EAEzB;EACA;EACA;EACA;EACA;EACA;EACA,IAAIyC,aAAa,IAAI1B,KAAK,CAAC7B,IAAI,KAAK,WAAW,IAAI6B,KAAK,CAACnC,MAAM,CAACM,IAAI,KAAK,OAAO,EAAE;IAChF+C,aAAa,CAAClB,KAAK,CAACnC,MAAM,CAAC;EAC7B;;EAEA;EACA,IAAIsD,OAAO,KAAKxC,SAAS,EAAEiB,kBAAkB,CAACI,KAAK,CAAC;AACtD;AACA,SAAS6B,WAAWA,CAACC,KAAK,EAAEC,cAAc,EAAE;EAC1C,KAAK,MAAMC,MAAM,IAAI,CAACF,KAAK,EAAEA,KAAK,CAACG,SAAS,CAAC,EAAE;IAC7C,IAAID,MAAM,KAAK,IAAI,EAAE;MACnB,IAAI,OAAOA,MAAM,CAACE,GAAG,KAAK,UAAU,EAAE;QACpCF,MAAM,CAACG,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGH,MAAM,CAACG,UAAU,CAAC,CAAC;QACxD,MAAMC,OAAO,GAAGJ,MAAM,CAACE,GAAG,CAACH,cAAc,CAAC;QAC1C,IAAI,OAAOK,OAAO,KAAK,UAAU,EAAEJ,MAAM,CAACG,UAAU,GAAGC,OAAO;MAChE,CAAC,MAAM,IAAIJ,MAAM,CAACE,GAAG,EAAE;QACrBF,MAAM,CAACE,GAAG,CAACG,OAAO,GAAGN,cAAc;MACrC;IACF;EACF;AACF;AACA,MAAMO,aAAa,GAAG,EAAE;AACxB,SAASC,aAAaA,CAAA,EAAG;EACvB;EACA,KAAK,MAAM,CAACnD,QAAQ,CAAC,IAAIkD,aAAa,EAAE;IACtC,MAAM9C,MAAM,GAAGJ,QAAQ,CAACI,MAAM;IAC9B,IAAIA,MAAM,EAAE;MACV,IAAIJ,QAAQ,CAAChB,KAAK,CAACmB,MAAM,EAAE;QACzBE,MAAM,CAACD,MAAM,EAAEJ,QAAQ,CAAC;MAC1B,CAAC,MAAM,IAAIM,UAAU,CAACN,QAAQ,CAACvB,MAAM,CAAC,IAAI6B,UAAU,CAACF,MAAM,CAAC3B,MAAM,CAAC,EAAE;QACnE2B,MAAM,CAAC3B,MAAM,CAAC0D,MAAM,CAACnC,QAAQ,CAACvB,MAAM,CAAC;MACvC;MACA,KAAK,MAAMmC,KAAK,IAAIZ,QAAQ,CAACqB,QAAQ,EAAE;QACrC,IAAIT,KAAK,CAAC5B,KAAK,CAACmB,MAAM,EAAE;UACtBE,MAAM,CAACL,QAAQ,EAAEY,KAAK,CAAC;QACzB,CAAC,MAAM,IAAIN,UAAU,CAACM,KAAK,CAACnC,MAAM,CAAC,IAAI6B,UAAU,CAACN,QAAQ,CAACvB,MAAM,CAAC,EAAE;UAClEuB,QAAQ,CAACvB,MAAM,CAAC0D,MAAM,CAACvB,KAAK,CAACnC,MAAM,CAAC;QACtC;MACF;IACF;;IAEA;IACA;IACA;IACA;IACA,IAAIuB,QAAQ,CAACC,QAAQ,EAAEQ,cAAc,CAACT,QAAQ,CAAC;;IAE/C;IACA,IAAIA,QAAQ,CAACvB,MAAM,CAACoB,KAAK,EAAE,OAAOG,QAAQ,CAACvB,MAAM,CAACoB,KAAK;IACvD,IAAIG,QAAQ,CAACjB,IAAI,KAAK,WAAW,EAAE+C,aAAa,CAAC9B,QAAQ,CAACvB,MAAM,CAAC;EACnE;;EAEA;EACA,KAAK,MAAM,CAACuB,QAAQ,EAAEhB,KAAK,EAAE0D,KAAK,CAAC,IAAIQ,aAAa,EAAE;IACpDlD,QAAQ,CAAChB,KAAK,GAAGA,KAAK;IACtB,MAAMoB,MAAM,GAAGJ,QAAQ,CAACI,MAAM;IAC9B,IAAIA,MAAM,EAAE;MACV,IAAIgD,qBAAqB,EAAEC,oBAAoB;MAC/C;MACA,MAAMpE,IAAI,GAAG,GAAGe,QAAQ,CAACjB,IAAI,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,GAAGc,QAAQ,CAACjB,IAAI,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;MACzE,MAAMC,MAAM,GAAGd,SAAS,CAACW,IAAI,CAAC;;MAE9B;MACAe,QAAQ,CAACvB,MAAM,GAAG,CAAC2E,qBAAqB,GAAGpD,QAAQ,CAAChB,KAAK,CAACP,MAAM,KAAK,IAAI,GAAG2E,qBAAqB,GAAG,IAAIhE,MAAM,CAAC,IAAI,CAACiE,oBAAoB,GAAGrD,QAAQ,CAAChB,KAAK,CAACM,IAAI,KAAK,IAAI,GAAG+D,oBAAoB,GAAG,EAAE,CAAC,CAAC;MACrMrD,QAAQ,CAACvB,MAAM,CAACoB,KAAK,GAAGG,QAAQ;MAChCyC,WAAW,CAACC,KAAK,EAAE1C,QAAQ,CAACvB,MAAM,CAAC;;MAEnC;MACA0C,UAAU,CAACnB,QAAQ,CAACvB,MAAM,EAAEuB,QAAQ,CAAChB,KAAK,CAAC;MAC3C,IAAIgB,QAAQ,CAAChB,KAAK,CAACmB,MAAM,EAAE;QACzBA,MAAM,CAACC,MAAM,EAAEJ,QAAQ,CAAC;MAC1B,CAAC,MAAM,IAAIM,UAAU,CAACN,QAAQ,CAACvB,MAAM,CAAC,IAAI6B,UAAU,CAACF,MAAM,CAAC3B,MAAM,CAAC,EAAE;QACnE2B,MAAM,CAAC3B,MAAM,CAACgD,GAAG,CAACzB,QAAQ,CAACvB,MAAM,CAAC;MACpC;MACA,KAAK,MAAMmC,KAAK,IAAIZ,QAAQ,CAACqB,QAAQ,EAAE;QACrC,IAAIT,KAAK,CAAC5B,KAAK,CAACmB,MAAM,EAAE;UACtBA,MAAM,CAACH,QAAQ,EAAEY,KAAK,CAAC;QACzB,CAAC,MAAM,IAAIN,UAAU,CAACM,KAAK,CAACnC,MAAM,CAAC,IAAI6B,UAAU,CAACN,QAAQ,CAACvB,MAAM,CAAC,EAAE;UAClEuB,QAAQ,CAACvB,MAAM,CAACgD,GAAG,CAACb,KAAK,CAACnC,MAAM,CAAC;QACnC;MACF;;MAEA;MACA+B,kBAAkB,CAACR,QAAQ,CAAC;IAC9B;EACF;EACAkD,aAAa,CAACX,MAAM,GAAG,CAAC;AAC1B;;AAEA;AACA,MAAMe,kBAAkB,GAAGA,CAAA,KAAMC,OAAO,CAACC,IAAI,CAAC,gFAAgF,CAAC;AAC/H,MAAMC,UAAU,GAAG,CAAC,CAAC;AACrB,IAAIC,qBAAqB,GAAG1G,eAAe;;AAE3C;AACA,MAAM2G,OAAO,GAAG,CAAC;AACjB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,UAAU,GAAGxF,gBAAgB,CAAC;EAClCyF,iBAAiB,EAAE,KAAK;EACxBC,gBAAgB,EAAE,KAAK;EACvBC,gBAAgB,EAAE,IAAI;EACtBC,mBAAmB,EAAE,KAAK;EAC1BC,iBAAiB,EAAE,KAAK;EACxBxE,cAAc;EACdwC,WAAW;EACXP,WAAW;EACXwC,kBAAkB,EAAExC,WAAW;EAC/BE,YAAY;EACZuC,sBAAsBA,CAACC,SAAS,EAAEzD,KAAK,EAAE;IACvC,MAAMI,KAAK,GAAGqD,SAAS,CAACtD,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACnB,KAAK;IAC9C,IAAI,CAACe,KAAK,IAAI,CAACI,KAAK,EAAE;IACtBW,WAAW,CAACX,KAAK,EAAEJ,KAAK,CAAC;EAC3B,CAAC;EACD0D,wBAAwBA,CAACD,SAAS,EAAEzD,KAAK,EAAE;IACzC,MAAMI,KAAK,GAAGqD,SAAS,CAACtD,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACnB,KAAK;IAC9C,IAAI,CAACe,KAAK,IAAI,CAACI,KAAK,EAAE;IACtBkB,WAAW,CAAClB,KAAK,EAAEJ,KAAK,CAAC;EAC3B,CAAC;EACD2D,uBAAuBA,CAACF,SAAS,EAAEzD,KAAK,EAAEC,WAAW,EAAE;IACrD,MAAMG,KAAK,GAAGqD,SAAS,CAACtD,QAAQ,CAAC,CAAC,CAACC,KAAK,CAACnB,KAAK;IAC9C,IAAI,CAACe,KAAK,IAAI,CAACC,WAAW,IAAI,CAACG,KAAK,EAAE;IACtCa,YAAY,CAACb,KAAK,EAAEJ,KAAK,EAAEC,WAAW,CAAC;EACzC,CAAC;EACD2D,kBAAkB,EAAEA,CAAA,KAAMf,UAAU;EACpCgB,mBAAmB,EAAEA,CAAA,KAAMhB,UAAU;EACrCiB,YAAYA,CAAC1E,QAAQ,EAAEjB,IAAI,EAAE4F,QAAQ,EAAEC,QAAQ,EAAElC,KAAK,EAAE;IACtD,IAAImC,cAAc,EAAEC,cAAc,EAAEC,eAAe;IACnDjG,gBAAgB,CAACC,IAAI,EAAE6F,QAAQ,CAAC;IAChC,IAAII,WAAW,GAAG,KAAK;;IAEvB;IACA,IAAIhF,QAAQ,CAACjB,IAAI,KAAK,WAAW,IAAI4F,QAAQ,CAAClG,MAAM,KAAKmG,QAAQ,CAACnG,MAAM,EAAEuG,WAAW,GAAG,IAAI;IAC5F;IAAA,KACK,IAAI,CAAC,CAACH,cAAc,GAAGD,QAAQ,CAACtF,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuF,cAAc,CAACtC,MAAM,OAAO,CAACuC,cAAc,GAAGH,QAAQ,CAACrF,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGwF,cAAc,CAACvC,MAAM,CAAC,EAAEyC,WAAW,GAAG,IAAI;IACxL;IAAA,KACK,IAAI,CAACD,eAAe,GAAGH,QAAQ,CAACtF,IAAI,KAAK,IAAI,IAAIyF,eAAe,CAACE,IAAI,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MAC3F,IAAIC,eAAe;MACnB,OAAOF,KAAK,MAAM,CAACE,eAAe,GAAGT,QAAQ,CAACrF,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8F,eAAe,CAACD,KAAK,CAAC,CAAC;IAChG,CAAC,CAAC,EAAEH,WAAW,GAAG,IAAI;;IAEtB;IACA,IAAIA,WAAW,EAAE;MACf9B,aAAa,CAACtB,IAAI,CAAC,CAAC5B,QAAQ,EAAE;QAC5B,GAAG4E;MACL,CAAC,EAAElC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACA,MAAM2C,YAAY,GAAGC,SAAS,CAACtF,QAAQ,EAAE4E,QAAQ,CAAC;MAClD,IAAI1G,MAAM,CAACqH,IAAI,CAACF,YAAY,CAAC,CAAC9C,MAAM,EAAE;QACpCrE,MAAM,CAACW,MAAM,CAACmB,QAAQ,CAAChB,KAAK,EAAEqG,YAAY,CAAC;QAC3ClE,UAAU,CAACnB,QAAQ,CAACvB,MAAM,EAAE4G,YAAY,CAAC;MAC3C;IACF;;IAEA;IACA,MAAMG,aAAa,GAAG9C,KAAK,CAAC+C,OAAO,KAAK,IAAI,IAAI,CAAC/C,KAAK,CAACgD,KAAK,GAAG9B,MAAM,MAAMD,OAAO;IAClF,IAAI6B,aAAa,EAAErC,aAAa,CAAC,CAAC;EACpC,CAAC;EACDwC,uBAAuB,EAAEA,CAAA,KAAM,KAAK;EACpCC,WAAWA,CAAA,EAAG,CAAC,CAAC;EAChBC,iBAAiB,EAAE7F,QAAQ,IAAIA,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACvB,MAAM;EAC1EqH,gBAAgB,EAAEA,CAAA,KAAM,IAAI;EAC5BC,kBAAkB,EAAE1B,SAAS,IAAIvE,OAAO,CAACuE,SAAS,CAACtD,QAAQ,CAAC,CAAC,CAACC,KAAK,EAAEqD,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACvF2B,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;EAC1BC,oBAAoB,EAAEA,CAAA,KAAM,KAAK;EACjCC,cAAc,EAAEA,CAAA,KAAM,KAAK;EAC3BnG,YAAY;EACZU,cAAc;EACd0F,kBAAkB,EAAE7C,kBAAkB;EACtC8C,gBAAgB,EAAE9C,kBAAkB;EACpC+C,kBAAkB,EAAE/C,kBAAkB;EACtCgD,eAAe,EAAE,OAAOC,UAAU,KAAK,UAAU,GAAGA,UAAU,GAAGhH,SAAS;EAC1EiH,aAAa,EAAE,OAAOC,YAAY,KAAK,UAAU,GAAGA,YAAY,GAAGlH,SAAS;EAC5EmH,SAAS,EAAE,CAAC,CAAC;EACbC,mBAAmB,EAAEA,CAAA,KAAM,IAAI;EAC/BC,wBAAwBA,CAAA,EAAG,CAAC,CAAC;EAC7BC,uBAAuBA,CAAA,EAAG,CAAC,CAAC;EAC5BC,qBAAqBA,CAAA,EAAG,CAAC,CAAC;EAC1BC,kBAAkBA,CAAA,EAAG,CAAC,CAAC;EACvBC,oBAAoB,EAAEA,CAAA,KAAM,IAAI;EAChCC,4BAA4BA,CAAA,EAAG;IAC7B,OAAO,KAAK;EACd,CAAC;EACDC,wBAAwBA,CAAA,EAAG,CAAC,CAAC;EAC7BC,gBAAgBA,CAAA,EAAG;IACjB,OAAO,KAAK;EACd,CAAC;EACDC,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC,CAAC;EACf,CAAC;EACDC,qBAAqBA,CAAA,EAAG,CAAC,CAAC;EAC1BC,eAAeA,CAAA,EAAG,CAAC,CAAC;EACpBC,sBAAsBA,CAAA,EAAG;IACvB,OAAO,IAAI;EACb,CAAC;EACDC,oBAAoB,EAAE,IAAI;EAC1BC,wBAAwBA,CAACC,WAAW,EAAE;IACpChE,qBAAqB,GAAGgE,WAAW;EACrC,CAAC;EACDC,wBAAwBA,CAAA,EAAG;IACzB,OAAOjE,qBAAqB;EAC9B,CAAC;EACDkE,qBAAqBA,CAAA,EAAG;IACtB,IAAIC,aAAa;IACjB,IAAInE,qBAAqB,KAAK1G,eAAe,EAAE,OAAO0G,qBAAqB;IAC3E,QAAQ,OAAOoE,MAAM,KAAK,WAAW,KAAK,CAACD,aAAa,GAAGC,MAAM,CAACC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,aAAa,CAAC9I,IAAI,CAAC;MAC7G,KAAK,OAAO;MACZ,KAAK,aAAa;MAClB,KAAK,UAAU;MACf,KAAK,eAAe;MACpB,KAAK,aAAa;MAClB,KAAK,WAAW;QACd,OAAO5B,qBAAqB;MAC9B,KAAK,aAAa;MAClB,KAAK,YAAY;MACjB,KAAK,aAAa;MAClB,KAAK,cAAc;MACnB,KAAK,cAAc;MACnB,KAAK,OAAO;QACV,OAAOD,uBAAuB;MAChC;QACE,OAAOD,oBAAoB;IAC/B;EACF,CAAC;EACD+K,iBAAiBA,CAAA,EAAG,CAAC;AACvB,CAAC,CAAC;AAEF,IAAIC,gBAAgB,EAAEC,iBAAiB;AACvC;AACA;AACA;AACA,SAAS7F,eAAeA,CAACrC,QAAQ,EAAE;EACjC,IAAIL,IAAI,GAAGK,QAAQ,CAACL,IAAI;EACxB,OAAOA,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAACoH,YAAY,EAAExI,IAAI,GAAGA,IAAI,CAACoB,QAAQ,CAAC,CAAC,CAACoH,YAAY;EACxE,OAAOxI,IAAI;AACb;;AAEA;AACA;AACA;AACA,MAAMyI,aAAa,GAAG3J,MAAM,IAAI,YAAY,IAAIA,MAAM,IAAI,kBAAkB,IAAIA,MAAM;AACtF;AACA;AACA;AACA,MAAM4J,kBAAkB,GAAGA,CAAA,KAAM;EAC/B,IAAIC,gBAAgB;EACpB,OAAO,CAACA,gBAAgB,GAAGhK,SAAS,CAACiK,eAAe,KAAK,IAAI,GAAGD,gBAAgB,GAAG,IAAI;AACzF,CAAC;AACD;AACA;AACA;AACA,MAAME,GAAG,GAAGzL,KAAK,CAACyL,GAAG;AACrB,MAAMC,oBAAoB,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACD,oBAAoB;AACnE,MAAME,KAAK,GAAGC,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAACC,cAAc,CAAC,SAAS,CAAC;;AAEzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,OAAOhB,MAAM,KAAK,WAAW,KAAK,CAACG,gBAAgB,GAAGH,MAAM,CAACiB,QAAQ,KAAK,IAAI,IAAId,gBAAgB,CAACe,aAAa,IAAI,CAAC,CAACd,iBAAiB,GAAGJ,MAAM,CAACmB,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGf,iBAAiB,CAACgB,OAAO,MAAM,aAAa,CAAC,GAAGnM,KAAK,CAACoM,eAAe,GAAGpM,KAAK,CAACqM,SAAS;AACxS,SAASC,kBAAkBA,CAACC,EAAE,EAAE;EAC9B,MAAMxG,GAAG,GAAG/F,KAAK,CAACwM,MAAM,CAACD,EAAE,CAAC;EAC5BR,yBAAyB,CAAC,MAAM,MAAMhG,GAAG,CAACG,OAAO,GAAGqG,EAAE,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAC9D,OAAOxG,GAAG;AACZ;AACA;AACA;AACA;AACA,SAAS0G,SAASA,CAAA,EAAG;EACnB,MAAM9G,KAAK,GAAGpF,QAAQ,CAAC,CAAC;EACxB,MAAMmM,aAAa,GAAGlM,gBAAgB,CAAC,CAAC;EACxC,OAAOR,KAAK,CAAC2M,OAAO,CAAC,MAAM,CAAC;IAC1BrI;EACF,CAAC,KAAK;IACJ,MAAMsI,MAAM,GAAG,CAAC,CAACnM,aAAa,CAACkF,KAAK,EAAE,IAAI,EAAEF,IAAI,IAAIA,IAAI,CAACzD,IAAI,KAAKhC,KAAK,CAAC6M,UAAU,CAAC;IACnF,MAAMC,IAAI,GAAGF,MAAM,GAAG5M,KAAK,CAAC6M,UAAU,GAAG7M,KAAK,CAACc,QAAQ;IACvD,OAAO,aAAaD,GAAG,CAACiM,IAAI,EAAE;MAC5BxI,QAAQ,EAAE,aAAazD,GAAG,CAAC6L,aAAa,EAAE;QACxCpI,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACqB,KAAK,EAAE+G,aAAa,CAAC,CAAC;AAC5B;AACA,SAASK,KAAKA,CAAC;EACbC;AACF,CAAC,EAAE;EACDjB,yBAAyB,CAAC,MAAM;IAC9BiB,GAAG,CAAC,IAAIC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;IAC5B,OAAO,MAAMD,GAAG,CAAC,KAAK,CAAC;EACzB,CAAC,EAAE,CAACA,GAAG,CAAC,CAAC;EACT,OAAO,IAAI;AACb;AACA,MAAME,aAAa,SAASlN,KAAK,CAAC6B,SAAS,CAAC;EAC1CsL,WAAWA,CAAC,GAAG5K,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd,IAAI,CAACwB,KAAK,GAAG;MACXqJ,KAAK,EAAE;IACT,CAAC;EACH;EACAC,iBAAiBA,CAACC,GAAG,EAAE;IACrB,IAAI,CAACrL,KAAK,CAAC+K,GAAG,CAACM,GAAG,CAAC;EACrB;EACAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxJ,KAAK,CAACqJ,KAAK,GAAG,IAAI,GAAG,IAAI,CAACnL,KAAK,CAACqC,QAAQ;EACtD;AACF;AACA4I,aAAa,CAACM,wBAAwB,GAAG,OAAO;EAC9CJ,KAAK,EAAE;AACT,CAAC,CAAC;AACF,SAASK,YAAYA,CAACC,GAAG,EAAE;EACzB,IAAIC,qBAAqB;EACzB;EACA;EACA,MAAMtL,MAAM,GAAG,OAAO0I,MAAM,KAAK,WAAW,GAAG,CAAC4C,qBAAqB,GAAG5C,MAAM,CAAC6C,gBAAgB,KAAK,IAAI,GAAGD,qBAAqB,GAAG,CAAC,GAAG,CAAC;EACxI,OAAOlL,KAAK,CAACC,OAAO,CAACgL,GAAG,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACL,GAAG,CAAC,CAAC,CAAC,EAAErL,MAAM,CAAC,EAAEqL,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGA,GAAG;AAC9E;;AAEA;AACA;AACA;AACA,SAASM,YAAYA,CAACnC,GAAG,EAAE;EACzB,IAAIoC,IAAI;EACR,OAAO,CAACA,IAAI,GAAGpC,GAAG,CAAC/I,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmL,IAAI,CAACrL,IAAI,CAACoB,QAAQ,CAAC,CAAC;AACnE;AACA;AACA,MAAMkK,EAAE,GAAG;EACTrC,GAAG,EAAEsC,CAAC,IAAIA,CAAC,KAAKhN,MAAM,CAACgN,CAAC,CAAC,IAAI,CAACD,EAAE,CAACE,GAAG,CAACD,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU;EAClEE,GAAG,EAAEF,CAAC,IAAI,OAAOA,CAAC,KAAK,UAAU;EACjCG,GAAG,EAAEH,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ;EAC/BI,GAAG,EAAEJ,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ;EAC/BK,GAAG,EAAEL,CAAC,IAAI,OAAOA,CAAC,KAAK,SAAS;EAChCM,GAAG,EAAEN,CAAC,IAAIA,CAAC,KAAK,KAAK,CAAC;EACtBC,GAAG,EAAED,CAAC,IAAI1L,KAAK,CAACC,OAAO,CAACyL,CAAC,CAAC;EAC1BO,GAAGA,CAACP,CAAC,EAAEQ,CAAC,EAAE;IACRC,MAAM,GAAG,SAAS;IAClBhN,OAAO,GAAG,WAAW;IACrBgL,MAAM,GAAG;EACX,CAAC,GAAG,CAAC,CAAC,EAAE;IACN;IACA,IAAI,OAAOuB,CAAC,KAAK,OAAOQ,CAAC,IAAI,CAAC,CAACR,CAAC,KAAK,CAAC,CAACQ,CAAC,EAAE,OAAO,KAAK;IACtD;IACA,IAAIT,EAAE,CAACI,GAAG,CAACH,CAAC,CAAC,IAAID,EAAE,CAACK,GAAG,CAACJ,CAAC,CAAC,EAAE,OAAOA,CAAC,KAAKQ,CAAC;IAC1C,MAAME,KAAK,GAAGX,EAAE,CAACrC,GAAG,CAACsC,CAAC,CAAC;IACvB,IAAIU,KAAK,IAAIjN,OAAO,KAAK,WAAW,EAAE,OAAOuM,CAAC,KAAKQ,CAAC;IACpD,MAAMG,KAAK,GAAGZ,EAAE,CAACE,GAAG,CAACD,CAAC,CAAC;IACvB,IAAIW,KAAK,IAAIF,MAAM,KAAK,WAAW,EAAE,OAAOT,CAAC,KAAKQ,CAAC;IACnD;IACA,IAAI,CAACG,KAAK,IAAID,KAAK,KAAKV,CAAC,KAAKQ,CAAC,EAAE,OAAO,IAAI;IAC5C;IACA,IAAInN,CAAC;IACL;IACA,KAAKA,CAAC,IAAI2M,CAAC,EAAE,IAAI,EAAE3M,CAAC,IAAImN,CAAC,CAAC,EAAE,OAAO,KAAK;IACxC;IACA,IAAIE,KAAK,IAAID,MAAM,KAAK,SAAS,IAAIhN,OAAO,KAAK,SAAS,EAAE;MAC1D,KAAKJ,CAAC,IAAIoL,MAAM,GAAG+B,CAAC,GAAGR,CAAC,EAAE,IAAI,CAACD,EAAE,CAACQ,GAAG,CAACP,CAAC,CAAC3M,CAAC,CAAC,EAAEmN,CAAC,CAACnN,CAAC,CAAC,EAAE;QAChDoL,MAAM;QACNhL,OAAO,EAAE;MACX,CAAC,CAAC,EAAE,OAAO,KAAK;IAClB,CAAC,MAAM;MACL,KAAKJ,CAAC,IAAIoL,MAAM,GAAG+B,CAAC,GAAGR,CAAC,EAAE,IAAIA,CAAC,CAAC3M,CAAC,CAAC,KAAKmN,CAAC,CAACnN,CAAC,CAAC,EAAE,OAAO,KAAK;IAC3D;IACA;IACA,IAAI0M,EAAE,CAACO,GAAG,CAACjN,CAAC,CAAC,EAAE;MACb;MACA,IAAIsN,KAAK,IAAIX,CAAC,CAAC3I,MAAM,KAAK,CAAC,IAAImJ,CAAC,CAACnJ,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC1D;MACA,IAAIqJ,KAAK,IAAI1N,MAAM,CAACqH,IAAI,CAAC2F,CAAC,CAAC,CAAC3I,MAAM,KAAK,CAAC,IAAIrE,MAAM,CAACqH,IAAI,CAACmG,CAAC,CAAC,CAACnJ,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MACpF;MACA,IAAI2I,CAAC,KAAKQ,CAAC,EAAE,OAAO,KAAK;IAC3B;IACA,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,SAASI,UAAUA,CAACrN,MAAM,EAAE;EAC1B,MAAMsN,IAAI,GAAG;IACXC,KAAK,EAAE,CAAC,CAAC;IACTC,SAAS,EAAE,CAAC;EACd,CAAC;EACD,IAAIxN,MAAM,EAAE;IACVA,MAAM,CAACyN,QAAQ,CAACtD,GAAG,IAAI;MACrB,IAAIA,GAAG,CAAC3J,IAAI,EAAE8M,IAAI,CAACC,KAAK,CAACpD,GAAG,CAAC3J,IAAI,CAAC,GAAG2J,GAAG;MACxC,IAAIA,GAAG,CAACuD,QAAQ,IAAI,CAACJ,IAAI,CAACE,SAAS,CAACrD,GAAG,CAACuD,QAAQ,CAAClN,IAAI,CAAC,EAAE8M,IAAI,CAACE,SAAS,CAACrD,GAAG,CAACuD,QAAQ,CAAClN,IAAI,CAAC,GAAG2J,GAAG,CAACuD,QAAQ;IAC1G,CAAC,CAAC;EACJ;EACA,OAAOJ,IAAI;AACb;AACA;AACA,SAAShK,OAAOA,CAAC6G,GAAG,EAAE;EACpB,IAAIA,GAAG,CAAC7J,IAAI,KAAK,OAAO,EAAE6J,GAAG,CAAC7G,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG6G,GAAG,CAAC7G,OAAO,CAAC,CAAC;EACtE,KAAK,MAAMqK,CAAC,IAAIxD,GAAG,EAAE;IACnB,MAAMyD,IAAI,GAAGzD,GAAG,CAACwD,CAAC,CAAC;IACnB,IAAI,CAACC,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACtN,IAAI,MAAM,OAAO,EAAEsN,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACtK,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGsK,IAAI,CAACtK,OAAO,CAAC,CAAC;EAC7H;AACF;AACA,MAAMuK,oBAAoB,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC;;AAEvD;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC/B,MAAMxN,KAAK,GAAG,CAAC,CAAC;EAChB,KAAK,MAAMyN,GAAG,IAAID,KAAK,EAAE;IACvB,IAAI,CAACF,oBAAoB,CAACI,QAAQ,CAACD,GAAG,CAAC,EAAEzN,KAAK,CAACyN,GAAG,CAAC,GAAGD,KAAK,CAACC,GAAG,CAAC;EAClE;EACA,OAAOzN,KAAK;AACd;;AAEA;AACA,SAASc,OAAOA,CAACV,MAAM,EAAEO,IAAI,EAAEZ,IAAI,EAAEC,KAAK,EAAE;EAC1C,MAAMP,MAAM,GAAGW,MAAM;;EAErB;EACA,IAAIY,QAAQ,GAAGvB,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACoB,KAAK;EACrD,IAAI,CAACG,QAAQ,EAAE;IACbA,QAAQ,GAAG;MACTL,IAAI;MACJZ,IAAI;MACJqB,MAAM,EAAE,IAAI;MACZiB,QAAQ,EAAE,EAAE;MACZrC,KAAK,EAAEuN,gBAAgB,CAACvN,KAAK,CAAC;MAC9BP,MAAM;MACNkO,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,CAAC,CAAC;MACZ3M,QAAQ,EAAE;IACZ,CAAC;IACD,IAAIxB,MAAM,EAAE;MACVA,MAAM,CAACoB,KAAK,GAAGG,QAAQ;MACvB,IAAIjB,IAAI,EAAEoC,UAAU,CAAC1C,MAAM,EAAEuB,QAAQ,CAAChB,KAAK,CAAC;IAC9C;EACF;EACA,OAAOgB,QAAQ;AACjB;AACA,SAAS6M,OAAOA,CAAClN,IAAI,EAAE8M,GAAG,EAAE;EAC1B,IAAIK,OAAO;EACX,IAAI1N,MAAM,GAAGO,IAAI,CAAC8M,GAAG,CAAC;EACtB,IAAI,CAACA,GAAG,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO;IAC7B/M,IAAI;IACJ8M,GAAG;IACHrN;EACF,CAAC;;EAED;EACA,MAAM2N,KAAK,GAAGN,GAAG,CAACO,KAAK,CAAC,GAAG,CAAC;EAC5B5N,MAAM,GAAG2N,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAET,GAAG,KAAKS,GAAG,CAACT,GAAG,CAAC,EAAE9M,IAAI,CAAC;EACnD8M,GAAG,GAAGM,KAAK,CAACI,GAAG,CAAC,CAAC;;EAEjB;EACA,IAAI,EAAE,CAACL,OAAO,GAAG1N,MAAM,KAAK,IAAI,IAAI0N,OAAO,CAAC/C,GAAG,CAAC,EAAEpK,IAAI,GAAGoN,KAAK,CAACE,MAAM,CAAC,CAACC,GAAG,EAAET,GAAG,KAAKS,GAAG,CAACT,GAAG,CAAC,EAAE9M,IAAI,CAAC;EACnG,OAAO;IACLA,IAAI;IACJ8M,GAAG;IACHrN;EACF,CAAC;AACH;;AAEA;AACA,MAAMgO,WAAW,GAAG,OAAO;AAC3B,SAASjN,MAAMA,CAACC,MAAM,EAAEQ,KAAK,EAAE;EAC7B,IAAIqK,EAAE,CAACI,GAAG,CAACzK,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAAC,EAAE;IAC9B;IACA,IAAIiN,WAAW,CAACC,IAAI,CAACzM,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAAC,EAAE;MACxC,MAAMgF,KAAK,GAAGvE,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAACmN,OAAO,CAACF,WAAW,EAAE,EAAE,CAAC;MACzD,MAAM;QACJzN,IAAI;QACJ8M;MACF,CAAC,GAAGI,OAAO,CAACzM,MAAM,CAAC3B,MAAM,EAAE0G,KAAK,CAAC;MACjC,IAAI,CAAC3F,KAAK,CAACC,OAAO,CAACE,IAAI,CAAC8M,GAAG,CAAC,CAAC,EAAE9M,IAAI,CAAC8M,GAAG,CAAC,GAAG,EAAE;IAC/C;IACA,MAAM;MACJ9M,IAAI;MACJ8M;IACF,CAAC,GAAGI,OAAO,CAACzM,MAAM,CAAC3B,MAAM,EAAEmC,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAAC;IAC9CS,KAAK,CAAC2M,cAAc,GAAG5N,IAAI,CAAC8M,GAAG,CAAC;IAChC9M,IAAI,CAAC8M,GAAG,CAAC,GAAG7L,KAAK,CAACnC,MAAM;EAC1B,CAAC,MAAM,IAAIwM,EAAE,CAACG,GAAG,CAACxK,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAAC,EAAE;IACrCS,KAAK,CAAC2M,cAAc,GAAG3M,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAACC,MAAM,CAAC3B,MAAM,EAAEmC,KAAK,CAACnC,MAAM,CAAC;EACxE;AACF;AACA,SAAS4B,MAAMA,CAACD,MAAM,EAAEQ,KAAK,EAAE;EAC7B,IAAIqK,EAAE,CAACI,GAAG,CAACzK,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAAC,EAAE;IAC9B,MAAM;MACJR,IAAI;MACJ8M;IACF,CAAC,GAAGI,OAAO,CAACzM,MAAM,CAAC3B,MAAM,EAAEmC,KAAK,CAAC5B,KAAK,CAACmB,MAAM,CAAC;IAC9C,MAAMqN,QAAQ,GAAG5M,KAAK,CAAC2M,cAAc;IACrC;IACA,IAAIC,QAAQ,KAAKjO,SAAS,EAAE,OAAOI,IAAI,CAAC8M,GAAG,CAAC;IAC5C;IAAA,KACK9M,IAAI,CAAC8M,GAAG,CAAC,GAAGe,QAAQ;EAC3B,CAAC,MAAM;IACL5M,KAAK,CAAC2M,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG3M,KAAK,CAAC2M,cAAc,CAACnN,MAAM,CAAC3B,MAAM,EAAEmC,KAAK,CAACnC,MAAM,CAAC;EAC3F;EACA,OAAOmC,KAAK,CAAC2M,cAAc;AAC7B;AACA,MAAME,cAAc,GAAG,CAAC,GAAGnB,oBAAoB;AAC/C;AACA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU;AACjD;AACA,SAAS,CAAC;AACV,MAAMoB,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAErC;AACA,SAASrI,SAASA,CAACtF,QAAQ,EAAE4E,QAAQ,EAAE;EACrC,MAAMS,YAAY,GAAG,CAAC,CAAC;;EAEvB;EACA,KAAK,MAAMgH,IAAI,IAAIzH,QAAQ,EAAE;IAC3B;IACA,IAAI6I,cAAc,CAACf,QAAQ,CAACL,IAAI,CAAC,EAAE;IACnC;IACA,IAAIpB,EAAE,CAACQ,GAAG,CAAC7G,QAAQ,CAACyH,IAAI,CAAC,EAAErM,QAAQ,CAAChB,KAAK,CAACqN,IAAI,CAAC,CAAC,EAAE;;IAElD;IACAhH,YAAY,CAACgH,IAAI,CAAC,GAAGzH,QAAQ,CAACyH,IAAI,CAAC;;IAEnC;IACA,KAAK,MAAMuB,KAAK,IAAIhJ,QAAQ,EAAE;MAC5B,IAAIgJ,KAAK,CAACC,UAAU,CAAC,GAAGxB,IAAI,GAAG,CAAC,EAAEhH,YAAY,CAACuI,KAAK,CAAC,GAAGhJ,QAAQ,CAACgJ,KAAK,CAAC;IACzE;EACF;;EAEA;EACA,KAAK,MAAMvB,IAAI,IAAIrM,QAAQ,CAAChB,KAAK,EAAE;IACjC,IAAIyO,cAAc,CAACf,QAAQ,CAACL,IAAI,CAAC,IAAIzH,QAAQ,CAACiE,cAAc,CAACwD,IAAI,CAAC,EAAE;IACpE,MAAM;MACJ1M,IAAI;MACJ8M;IACF,CAAC,GAAGI,OAAO,CAAC7M,QAAQ,CAACvB,MAAM,EAAE4N,IAAI,CAAC;;IAElC;IACA;IACA;IACA;IACA;IACA,IAAI1M,IAAI,CAACuK,WAAW,IAAIvK,IAAI,CAACuK,WAAW,CAAC3H,MAAM,KAAK,CAAC,EAAE;MACrD;MACA,IAAIuL,IAAI,GAAGJ,mBAAmB,CAACK,GAAG,CAACpO,IAAI,CAACuK,WAAW,CAAC;MACpD,IAAI,CAAC4D,IAAI,EAAE;QACTA,IAAI,GAAG,IAAInO,IAAI,CAACuK,WAAW,CAAC,CAAC;QAC7BwD,mBAAmB,CAAC3D,GAAG,CAACpK,IAAI,CAACuK,WAAW,EAAE4D,IAAI,CAAC;MACjD;MACAzI,YAAY,CAACoH,GAAG,CAAC,GAAGqB,IAAI,CAACrB,GAAG,CAAC;IAC/B,CAAC,MAAM;MACL;MACApH,YAAY,CAACoH,GAAG,CAAC,GAAG,CAAC;IACvB;EACF;EACA,OAAOpH,YAAY;AACrB;AACA,MAAM2I,OAAO,GAAG,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;;AAEvF;AACA,MAAMC,YAAY,GAAG,IAAI;AACzB,MAAMC,cAAc,GAAG,MAAM;AAC7B,MAAMC,oBAAoB,GAAG,aAAa;;AAE1C;AACA;AACA,MAAMC,SAAS,GAAG,CAAC,KAAK,EAAE,aAAa,EAAE,cAAc;AACvD;AACA,eAAe,EAAE,iBAAiB;AAClC;AACA,kBAAkB,EAAE,QAAQ,CAAC;AAC7B,MAAMC,WAAW,GAAG,kDAAkD;;AAEtE;AACA,SAASrN,UAAUA,CAAC1C,MAAM,EAAEO,KAAK,EAAE;EACjC,MAAMgB,QAAQ,GAAGvB,MAAM,CAACoB,KAAK;EAC7B,MAAM4O,SAAS,GAAGzO,QAAQ,IAAIqC,eAAe,CAACrC,QAAQ,CAAC,CAACe,QAAQ,CAAC,CAAC;EAClE,MAAM2N,YAAY,GAAG1O,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC2M,UAAU;EACpE,KAAK,MAAMN,IAAI,IAAIrN,KAAK,EAAE;IACxB,IAAIkG,KAAK,GAAGlG,KAAK,CAACqN,IAAI,CAAC;;IAEvB;IACA,IAAIoB,cAAc,CAACf,QAAQ,CAACL,IAAI,CAAC,EAAE;;IAEnC;IACA,IAAIrM,QAAQ,IAAIwO,WAAW,CAACnB,IAAI,CAAChB,IAAI,CAAC,EAAE;MACtC,IAAI,OAAOnH,KAAK,KAAK,UAAU,EAAElF,QAAQ,CAAC4M,QAAQ,CAACP,IAAI,CAAC,GAAGnH,KAAK,CAAC,KAAK,OAAOlF,QAAQ,CAAC4M,QAAQ,CAACP,IAAI,CAAC;MACpGrM,QAAQ,CAAC2M,UAAU,GAAGzO,MAAM,CAACqH,IAAI,CAACvF,QAAQ,CAAC4M,QAAQ,CAAC,CAACrK,MAAM;IAC7D;;IAEA;IACA;IACA,IAAI2C,KAAK,KAAK3F,SAAS,EAAE;IACzB,IAAI;MACFI,IAAI;MACJ8M,GAAG;MACHrN;IACF,CAAC,GAAGyN,OAAO,CAACpO,MAAM,EAAE4N,IAAI,CAAC;;IAEzB;IACA;IACA,IAAIjE,aAAa,CAACzI,IAAI,CAAC,EAAE;MACvB,IAAI8M,GAAG,KAAK,UAAU,EAAE;QACtBA,GAAG,GAAG,YAAY;QAClBvH,KAAK,GAAGA,KAAK,KAAKkJ,YAAY,GAAGC,cAAc,GAAGC,oBAAoB;MACxE,CAAC,MAAM,IAAI7B,GAAG,KAAK,gBAAgB,EAAE;QACnCA,GAAG,GAAG,kBAAkB;QACxBvH,KAAK,GAAGA,KAAK,KAAKkJ,YAAY,GAAGC,cAAc,GAAGC,oBAAoB;MACxE;IACF;;IAEA;IACA,IAAIlP,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACuP,IAAI;IACjC;IACA;IACA;IACA;IACAX,OAAO,GAAG5O,MAAM,CAAC8K,WAAW,CAACjL,IAAI,KAAKiG,KAAK,CAACgF,WAAW,CAACjL,IAAI,GAAGG,MAAM,CAAC8K,WAAW,KAAKhF,KAAK,CAACgF,WAAW,CAAC,EAAE;MACxG9K,MAAM,CAACuP,IAAI,CAACzJ,KAAK,CAAC;IACpB;IACA;IAAA,KACK,IAAI9F,MAAM,YAAYtC,KAAK,CAAC8R,MAAM,IAAI1J,KAAK,YAAYpI,KAAK,CAAC8R,MAAM,EAAE;MACxExP,MAAM,CAACyP,IAAI,GAAG3J,KAAK,CAAC2J,IAAI;IAC1B;IACA;IAAA,KACK,IAAIzP,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC2K,GAAG,IAAIvK,KAAK,CAACC,OAAO,CAACyF,KAAK,CAAC,EAAE;MAC7D,IAAI9F,MAAM,CAAC0P,SAAS,EAAE1P,MAAM,CAAC0P,SAAS,CAAC5J,KAAK,CAAC,CAAC,KAAK9F,MAAM,CAAC2K,GAAG,CAAC,GAAG7E,KAAK,CAAC;IACzE;IACA;IAAA,KACK,IAAI9F,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC2K,GAAG,IAAI,OAAO7E,KAAK,KAAK,QAAQ,EAAE;MAClE,MAAM6J,OAAO,GAAG3P,MAAM,YAAYtC,KAAK,CAACkS,KAAK;MAC7C;MACA,IAAI,CAACD,OAAO,IAAI3P,MAAM,CAAC6P,SAAS,IAAI,OAAO/J,KAAK,KAAK,QAAQ,EAAE9F,MAAM,CAAC6P,SAAS,CAAC/J,KAAK,CAAC;MACtF;MAAA,KACK9F,MAAM,CAAC2K,GAAG,CAAC7E,KAAK,CAAC;;MAEtB;MACA;MACA,IAAI,CAACmD,kBAAkB,CAAC,CAAC,IAAI,EAAEoG,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACS,MAAM,CAAC,IAAIH,OAAO,EAAE3P,MAAM,CAAC+P,mBAAmB,CAAC,CAAC;IAChH;IACA;IAAA,KACK;MACHxP,IAAI,CAAC8M,GAAG,CAAC,GAAGvH,KAAK;;MAEjB;MACA;MACA;MACA,IAAIuJ,SAAS,IAAI,CAACA,SAAS,CAACS,MAAM,IAAIX,SAAS,CAAC7B,QAAQ,CAACD,GAAG,CAAC,IAAI9M,IAAI,CAAC8M,GAAG,CAAC,YAAY3P,KAAK,CAACsS,OAAO;MACnG;MACAzP,IAAI,CAAC8M,GAAG,CAAC,CAAC4C,MAAM,KAAKvS,KAAK,CAACwS,UAAU,IAAI3P,IAAI,CAAC8M,GAAG,CAAC,CAAC1N,IAAI,KAAKjC,KAAK,CAACyS,gBAAgB,EAAE;QAClF;QACA,IAAInH,aAAa,CAACzI,IAAI,CAAC8M,GAAG,CAAC,CAAC,EAAE9M,IAAI,CAAC8M,GAAG,CAAC,CAAC+C,UAAU,GAAG,MAAM,CAAC,KAAK7P,IAAI,CAAC8M,GAAG,CAAC,CAACgD,QAAQ,GAAGrB,YAAY;MACpG;IACF;EACF;;EAEA;EACA,IAAIpO,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACI,MAAM,IAAIqO,SAAS,IAAI,IAAI,IAAIA,SAAS,CAACiB,QAAQ,IAAI1P,QAAQ,CAACvB,MAAM,YAAY3B,KAAK,CAAC6S,QAAQ,IAAIjB,YAAY,KAAK1O,QAAQ,CAAC2M,UAAU,EAAE;IACvK;IACA,MAAMxH,KAAK,GAAGsJ,SAAS,CAACiB,QAAQ,CAACE,WAAW,CAACtO,OAAO,CAACtB,QAAQ,CAACvB,MAAM,CAAC;IACrE,IAAI0G,KAAK,GAAG,CAAC,CAAC,EAAEsJ,SAAS,CAACiB,QAAQ,CAACE,WAAW,CAACrO,MAAM,CAAC4D,KAAK,EAAE,CAAC,CAAC;IAC/D;IACA,IAAInF,QAAQ,CAAC2M,UAAU,IAAI3M,QAAQ,CAACvB,MAAM,CAACoR,OAAO,KAAK,IAAI,IAAI7P,QAAQ,CAACvB,MAAM,YAAY3B,KAAK,CAAC6S,QAAQ,EAAE;MACxGlB,SAAS,CAACiB,QAAQ,CAACE,WAAW,CAAChO,IAAI,CAAC5B,QAAQ,CAACvB,MAAM,CAAC;IACtD;EACF;;EAEA;EACA,IAAIuB,QAAQ,IAAIA,QAAQ,CAAChB,KAAK,CAACmB,MAAM,KAAKZ,SAAS,EAAE;IACnD,IAAIS,QAAQ,CAACvB,MAAM,YAAY3B,KAAK,CAACgT,cAAc,EAAE9P,QAAQ,CAAChB,KAAK,CAACmB,MAAM,GAAG,UAAU,CAAC,KAAK,IAAIH,QAAQ,CAACvB,MAAM,YAAY3B,KAAK,CAACiT,QAAQ,EAAE/P,QAAQ,CAAChB,KAAK,CAACmB,MAAM,GAAG,UAAU;EAChL;;EAEA;EACA,IAAIH,QAAQ,EAAEQ,kBAAkB,CAACR,QAAQ,CAAC;EAC1C,OAAOvB,MAAM;AACf;AACA,SAAS+B,kBAAkBA,CAACR,QAAQ,EAAE;EACpC,IAAIgQ,cAAc;EAClB,IAAI,CAAChQ,QAAQ,CAACI,MAAM,EAAE;EACtBJ,QAAQ,CAAChB,KAAK,CAACiR,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjQ,QAAQ,CAAChB,KAAK,CAACiR,QAAQ,CAACjQ,QAAQ,CAACvB,MAAM,CAAC;EACnF,MAAMqC,KAAK,GAAG,CAACkP,cAAc,GAAGhQ,QAAQ,CAACL,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqQ,cAAc,CAACjP,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGiP,cAAc,CAACjP,QAAQ,CAAC,CAAC;EACtI,IAAID,KAAK,IAAIA,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,KAAK,CAAC,EAAEpP,KAAK,CAACqP,UAAU,CAAC,CAAC;AAC9D;AACA,SAASC,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAClC;EACA;EACA,IAAID,MAAM,CAACE,MAAM,EAAE;EACnB,IAAI9H,oBAAoB,CAAC4H,MAAM,CAAC,EAAE;IAChCA,MAAM,CAACG,IAAI,GAAGF,IAAI,CAACG,KAAK,GAAG,CAAC,CAAC;IAC7BJ,MAAM,CAACK,KAAK,GAAGJ,IAAI,CAACG,KAAK,GAAG,CAAC;IAC7BJ,MAAM,CAACM,GAAG,GAAGL,IAAI,CAACM,MAAM,GAAG,CAAC;IAC5BP,MAAM,CAACQ,MAAM,GAAGP,IAAI,CAACM,MAAM,GAAG,CAAC,CAAC;EAClC,CAAC,MAAM;IACLP,MAAM,CAACS,MAAM,GAAGR,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACM,MAAM;EAC1C;EACAP,MAAM,CAACU,sBAAsB,CAAC,CAAC;AACjC;AACA,MAAMzQ,UAAU,GAAG7B,MAAM,IAAIA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC6B,UAAU;AAExE,SAAS0Q,MAAMA,CAACjJ,KAAK,EAAE;EACrB,OAAO,CAACA,KAAK,CAACkJ,WAAW,IAAIlJ,KAAK,CAACtJ,MAAM,EAAEyS,IAAI,GAAG,GAAG,GAAGnJ,KAAK,CAAC5C,KAAK,GAAG4C,KAAK,CAACoJ,UAAU;AACxF;;AAEA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACC,WAAW,EAAEzI,GAAG,EAAE0I,QAAQ,EAAEC,SAAS,EAAE;EAC5E,MAAMC,WAAW,GAAGF,QAAQ,CAACvD,GAAG,CAACnF,GAAG,CAAC;EACrC,IAAI4I,WAAW,EAAE;IACfF,QAAQ,CAACG,MAAM,CAAC7I,GAAG,CAAC;IACpB;IACA,IAAI0I,QAAQ,CAAChB,IAAI,KAAK,CAAC,EAAE;MACvBe,WAAW,CAACI,MAAM,CAACF,SAAS,CAAC;MAC7BC,WAAW,CAACpS,MAAM,CAACsS,qBAAqB,CAACH,SAAS,CAAC;IACrD;EACF;AACF;AACA,SAASnP,mBAAmBA,CAACuP,KAAK,EAAElT,MAAM,EAAE;EAC1C,MAAM;IACJiR;EACF,CAAC,GAAGiC,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;EACpB;EACA2O,QAAQ,CAACE,WAAW,GAAGF,QAAQ,CAACE,WAAW,CAACgC,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKpT,MAAM,CAAC;EACrEiR,QAAQ,CAACoC,WAAW,GAAGpC,QAAQ,CAACoC,WAAW,CAACF,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKpT,MAAM,CAAC;EACrEiR,QAAQ,CAACqC,OAAO,CAACC,OAAO,CAAC,CAAC9M,KAAK,EAAEuH,GAAG,KAAK;IACvC,IAAIvH,KAAK,CAAC+L,WAAW,KAAKxS,MAAM,IAAIyG,KAAK,CAACzG,MAAM,KAAKA,MAAM,EAAE;MAC3D;MACAiR,QAAQ,CAACqC,OAAO,CAACN,MAAM,CAAChF,GAAG,CAAC;IAC9B;EACF,CAAC,CAAC;EACFiD,QAAQ,CAAC2B,WAAW,CAACW,OAAO,CAAC,CAACV,QAAQ,EAAEC,SAAS,KAAK;IACpDH,6BAA6B,CAAC1B,QAAQ,CAAC2B,WAAW,EAAE5S,MAAM,EAAE6S,QAAQ,EAAEC,SAAS,CAAC;EAClF,CAAC,CAAC;AACJ;AACA,SAASU,YAAYA,CAACN,KAAK,EAAE;EAC3B;EACA,SAASO,iBAAiBA,CAACnK,KAAK,EAAE;IAChC,MAAM;MACJ2H;IACF,CAAC,GAAGiC,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;IACpB,MAAMoR,EAAE,GAAGpK,KAAK,CAACqK,OAAO,GAAG1C,QAAQ,CAAC2C,YAAY,CAAC,CAAC,CAAC;IACnD,MAAMC,EAAE,GAAGvK,KAAK,CAACwK,OAAO,GAAG7C,QAAQ,CAAC2C,YAAY,CAAC,CAAC,CAAC;IACnD,OAAOzH,IAAI,CAAC4H,KAAK,CAAC5H,IAAI,CAAC6H,IAAI,CAACN,EAAE,GAAGA,EAAE,GAAGG,EAAE,GAAGA,EAAE,CAAC,CAAC;EACjD;;EAEA;EACA,SAASI,mBAAmBA,CAAC/T,OAAO,EAAE;IACpC,OAAOA,OAAO,CAACiT,MAAM,CAAChJ,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC3D,IAAI,CAAChG,IAAI,IAAI;MAClF,IAAI+L,IAAI;MACR,OAAO,CAACA,IAAI,GAAGpC,GAAG,CAAC/I,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmL,IAAI,CAAC4B,QAAQ,CAAC,WAAW,GAAG3N,IAAI,CAAC;IAChF,CAAC,CAAC,CAAC;EACL;EACA,SAAS0T,SAASA,CAAC5K,KAAK,EAAE6J,MAAM,EAAE;IAChC,MAAM9Q,KAAK,GAAG6Q,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;IAC9B,MAAM6R,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,MAAMC,aAAa,GAAG,EAAE;IACxB;IACA,MAAMC,aAAa,GAAGnB,MAAM,GAAGA,MAAM,CAAC9Q,KAAK,CAAC4O,QAAQ,CAACE,WAAW,CAAC,GAAG9O,KAAK,CAAC4O,QAAQ,CAACE,WAAW;IAC9F;IACA,KAAK,IAAIrR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwU,aAAa,CAACxQ,MAAM,EAAEhE,CAAC,EAAE,EAAE;MAC7C,MAAMuC,KAAK,GAAGiK,YAAY,CAACgI,aAAa,CAACxU,CAAC,CAAC,CAAC;MAC5C,IAAIuC,KAAK,EAAE;QACTA,KAAK,CAACkS,SAAS,CAAC3C,MAAM,GAAG9Q,SAAS;MACpC;IACF;IACA,IAAI,CAACuB,KAAK,CAACqH,YAAY,EAAE;MACvB;MACArH,KAAK,CAACmS,MAAM,CAACC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGpS,KAAK,CAACmS,MAAM,CAACC,OAAO,CAACnL,KAAK,EAAEjH,KAAK,CAAC;IAC5E;IACA,SAASqS,aAAaA,CAACvK,GAAG,EAAE;MAC1B,MAAM9H,KAAK,GAAGiK,YAAY,CAACnC,GAAG,CAAC;MAC/B;MACA,IAAI,CAAC9H,KAAK,IAAI,CAACA,KAAK,CAACmS,MAAM,CAACG,OAAO,IAAItS,KAAK,CAACkS,SAAS,CAAC3C,MAAM,KAAK,IAAI,EAAE,OAAO,EAAE;;MAEjF;MACA,IAAIvP,KAAK,CAACkS,SAAS,CAAC3C,MAAM,KAAK9Q,SAAS,EAAE;QACxC,IAAI8T,mBAAmB;QACvBvS,KAAK,CAACmS,MAAM,CAACC,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGpS,KAAK,CAACmS,MAAM,CAACC,OAAO,CAACnL,KAAK,EAAEjH,KAAK,EAAE,CAACuS,mBAAmB,GAAGvS,KAAK,CAACqH,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkL,mBAAmB,CAACtS,QAAQ,CAAC,CAAC,CAAC;QACxK;QACA,IAAID,KAAK,CAACkS,SAAS,CAAC3C,MAAM,KAAK9Q,SAAS,EAAEuB,KAAK,CAACkS,SAAS,CAAC3C,MAAM,GAAG,IAAI;MACzE;;MAEA;MACA,OAAOvP,KAAK,CAACkS,SAAS,CAAC3C,MAAM,GAAGvP,KAAK,CAACkS,SAAS,CAACM,eAAe,CAAC1K,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE;IACjF;;IAEA;IACA,IAAI2K,IAAI,GAAGR;IACX;IAAA,CACCS,OAAO,CAACL,aAAa;IACtB;IAAA,CACCM,IAAI,CAAC,CAACvI,CAAC,EAAEQ,CAAC,KAAK;MACd,MAAMgI,MAAM,GAAG3I,YAAY,CAACG,CAAC,CAACzM,MAAM,CAAC;MACrC,MAAMkV,MAAM,GAAG5I,YAAY,CAACW,CAAC,CAACjN,MAAM,CAAC;MACrC,IAAI,CAACiV,MAAM,IAAI,CAACC,MAAM,EAAE,OAAOzI,CAAC,CAAC0I,QAAQ,GAAGlI,CAAC,CAACkI,QAAQ;MACtD,OAAOD,MAAM,CAACV,MAAM,CAACY,QAAQ,GAAGH,MAAM,CAACT,MAAM,CAACY,QAAQ,IAAI3I,CAAC,CAAC0I,QAAQ,GAAGlI,CAAC,CAACkI,QAAQ;IACnF,CAAC;IACD;IAAA,CACChC,MAAM,CAACkC,IAAI,IAAI;MACd,MAAMC,EAAE,GAAG/C,MAAM,CAAC8C,IAAI,CAAC;MACvB,IAAIlB,UAAU,CAACoB,GAAG,CAACD,EAAE,CAAC,EAAE,OAAO,KAAK;MACpCnB,UAAU,CAACnR,GAAG,CAACsS,EAAE,CAAC;MAClB,OAAO,IAAI;IACb,CAAC,CAAC;;IAEF;IACA;IACA,IAAIjT,KAAK,CAACmS,MAAM,CAACrB,MAAM,EAAE2B,IAAI,GAAGzS,KAAK,CAACmS,MAAM,CAACrB,MAAM,CAAC2B,IAAI,EAAEzS,KAAK,CAAC;;IAEhE;IACA,KAAK,MAAMmT,GAAG,IAAIV,IAAI,EAAE;MACtB,IAAItC,WAAW,GAAGgD,GAAG,CAACxV,MAAM;MAC5B;MACA,OAAOwS,WAAW,EAAE;QAClB,IAAIiD,KAAK;QACT,IAAI,CAACA,KAAK,GAAGjD,WAAW,CAACpR,KAAK,KAAK,IAAI,IAAIqU,KAAK,CAACvH,UAAU,EAAEmG,aAAa,CAAClR,IAAI,CAAC;UAC9E,GAAGqS,GAAG;UACNhD;QACF,CAAC,CAAC;QACFA,WAAW,GAAGA,WAAW,CAAC7Q,MAAM;MAClC;IACF;;IAEA;IACA,IAAI,WAAW,IAAI2H,KAAK,IAAIjH,KAAK,CAAC4O,QAAQ,CAAC2B,WAAW,CAAC2C,GAAG,CAACjM,KAAK,CAACwJ,SAAS,CAAC,EAAE;MAC3E,KAAK,IAAIC,WAAW,IAAI1Q,KAAK,CAAC4O,QAAQ,CAAC2B,WAAW,CAACtD,GAAG,CAAChG,KAAK,CAACwJ,SAAS,CAAC,CAAC4C,MAAM,CAAC,CAAC,EAAE;QAChF,IAAI,CAACvB,UAAU,CAACoB,GAAG,CAAChD,MAAM,CAACQ,WAAW,CAAC4C,YAAY,CAAC,CAAC,EAAEtB,aAAa,CAAClR,IAAI,CAAC4P,WAAW,CAAC4C,YAAY,CAAC;MACrG;IACF;IACA,OAAOtB,aAAa;EACtB;;EAEA;EACA,SAASuB,gBAAgBA,CAACvB,aAAa,EAAE/K,KAAK,EAAEuM,KAAK,EAAEC,QAAQ,EAAE;IAC/D;IACA,IAAIzB,aAAa,CAACvQ,MAAM,EAAE;MACxB,MAAMiS,UAAU,GAAG;QACjBC,OAAO,EAAE;MACX,CAAC;MACD,KAAK,MAAMR,GAAG,IAAInB,aAAa,EAAE;QAC/B,MAAMhS,KAAK,GAAGiK,YAAY,CAACkJ,GAAG,CAACxV,MAAM,CAAC;QACtC,IAAIqC,KAAK,EAAE;UACT,MAAM;YACJkS,SAAS;YACT0B,OAAO;YACPrE,MAAM;YACNX;UACF,CAAC,GAAG5O,KAAK;UACT,MAAM6T,gBAAgB,GAAG,IAAI7X,KAAK,CAAC8X,OAAO,CAACF,OAAO,CAACG,CAAC,EAAEH,OAAO,CAACI,CAAC,EAAE,CAAC,CAAC,CAACC,SAAS,CAAC1E,MAAM,CAAC;UACrF,MAAM2E,iBAAiB,GAAGjB,EAAE,IAAI;YAC9B,IAAIkB,qBAAqB,EAAEC,sBAAsB;YACjD,OAAO,CAACD,qBAAqB,GAAG,CAACC,sBAAsB,GAAGxF,QAAQ,CAAC2B,WAAW,CAACtD,GAAG,CAACgG,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,sBAAsB,CAAClB,GAAG,CAACC,GAAG,CAAChD,WAAW,CAAC,KAAK,IAAI,GAAGgE,qBAAqB,GAAG,KAAK;UACjM,CAAC;UACD,MAAME,iBAAiB,GAAGpB,EAAE,IAAI;YAC9B,MAAMvC,WAAW,GAAG;cAClB4C,YAAY,EAAEH,GAAG;cACjB7U,MAAM,EAAE2I,KAAK,CAAC3I;YAChB,CAAC;YACD,IAAIsQ,QAAQ,CAAC2B,WAAW,CAAC2C,GAAG,CAACD,EAAE,CAAC,EAAE;cAChC;cACA;cACArE,QAAQ,CAAC2B,WAAW,CAACtD,GAAG,CAACgG,EAAE,CAAC,CAAChK,GAAG,CAACkK,GAAG,CAAChD,WAAW,EAAEO,WAAW,CAAC;YAChE,CAAC,MAAM;cACL;cACA;cACA;cACA9B,QAAQ,CAAC2B,WAAW,CAACtH,GAAG,CAACgK,EAAE,EAAE,IAAIpG,GAAG,CAAC,CAAC,CAACsG,GAAG,CAAChD,WAAW,EAAEO,WAAW,CAAC,CAAC,CAAC,CAAC;YACzE;YACAzJ,KAAK,CAAC3I,MAAM,CAAC+V,iBAAiB,CAACpB,EAAE,CAAC;UACpC,CAAC;UACD,MAAMrC,qBAAqB,GAAGqC,EAAE,IAAI;YAClC,MAAMzC,QAAQ,GAAG5B,QAAQ,CAAC2B,WAAW,CAACtD,GAAG,CAACgG,EAAE,CAAC;YAC7C,IAAIzC,QAAQ,EAAE;cACZF,6BAA6B,CAAC1B,QAAQ,CAAC2B,WAAW,EAAE4C,GAAG,CAAChD,WAAW,EAAEK,QAAQ,EAAEyC,EAAE,CAAC;YACpF;UACF,CAAC;;UAED;UACA,IAAIqB,iBAAiB,GAAG,CAAC,CAAC;UAC1B;UACA,KAAK,IAAI/I,IAAI,IAAItE,KAAK,EAAE;YACtB,IAAIsN,QAAQ,GAAGtN,KAAK,CAACsE,IAAI,CAAC;YAC1B;YACA;YACA,IAAI,OAAOgJ,QAAQ,KAAK,UAAU,EAAED,iBAAiB,CAAC/I,IAAI,CAAC,GAAGgJ,QAAQ;UACxE;UACA,IAAIC,YAAY,GAAG;YACjB,GAAGrB,GAAG;YACN,GAAGmB,iBAAiB;YACpBV,OAAO;YACP5B,aAAa;YACb2B,OAAO,EAAED,UAAU,CAACC,OAAO;YAC3BH,KAAK;YACLK,gBAAgB;YAChBY,GAAG,EAAEvC,SAAS,CAACuC,GAAG;YAClBlF,MAAM,EAAEA,MAAM;YACd;YACAmF,eAAeA,CAAA,EAAG;cAChB;cACA;cACA,MAAMC,kBAAkB,GAAG,WAAW,IAAI1N,KAAK,IAAI2H,QAAQ,CAAC2B,WAAW,CAACtD,GAAG,CAAChG,KAAK,CAACwJ,SAAS,CAAC;;cAE5F;cACA;cACA;cACA,CAACkE,kBAAkB;cACnB;cACAA,kBAAkB,CAACzB,GAAG,CAACC,GAAG,CAAChD,WAAW,CAAC,EAAE;gBACvCqE,YAAY,CAACb,OAAO,GAAGD,UAAU,CAACC,OAAO,GAAG,IAAI;gBAChD;gBACA;gBACA,IAAI/E,QAAQ,CAACqC,OAAO,CAACzB,IAAI,IAAI9Q,KAAK,CAACkW,IAAI,CAAChG,QAAQ,CAACqC,OAAO,CAACoC,MAAM,CAAC,CAAC,CAAC,CAACwB,IAAI,CAACpX,CAAC,IAAIA,CAAC,CAAC0S,WAAW,KAAKgD,GAAG,CAAChD,WAAW,CAAC,EAAE;kBAC/G;kBACA,MAAM2E,MAAM,GAAG9C,aAAa,CAAC3T,KAAK,CAAC,CAAC,EAAE2T,aAAa,CAACxR,OAAO,CAAC2S,GAAG,CAAC,CAAC;kBACjE4B,aAAa,CAAC,CAAC,GAAGD,MAAM,EAAE3B,GAAG,CAAC,CAAC;gBACjC;cACF;YACF,CAAC;YACD;YACA7U,MAAM,EAAE;cACN4V,iBAAiB;cACjBG,iBAAiB;cACjBzD;YACF,CAAC;YACDoE,aAAa,EAAE;cACbd,iBAAiB;cACjBG,iBAAiB;cACjBzD;YACF,CAAC;YACDqE,WAAW,EAAEhO;UACf,CAAC;;UAED;UACAwM,QAAQ,CAACe,YAAY,CAAC;UACtB;UACA,IAAId,UAAU,CAACC,OAAO,KAAK,IAAI,EAAE;QACnC;MACF;IACF;IACA,OAAO3B,aAAa;EACtB;EACA,SAAS+C,aAAaA,CAAC/C,aAAa,EAAE;IACpC,MAAM;MACJpD;IACF,CAAC,GAAGiC,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;IACpB,KAAK,MAAMiV,UAAU,IAAItG,QAAQ,CAACqC,OAAO,CAACoC,MAAM,CAAC,CAAC,EAAE;MAClD;MACA;MACA,IAAI,CAACrB,aAAa,CAACvQ,MAAM,IAAI,CAACuQ,aAAa,CAAC6C,IAAI,CAAC1B,GAAG,IAAIA,GAAG,CAACxV,MAAM,KAAKuX,UAAU,CAACvX,MAAM,IAAIwV,GAAG,CAAC9O,KAAK,KAAK6Q,UAAU,CAAC7Q,KAAK,IAAI8O,GAAG,CAAC9C,UAAU,KAAK6E,UAAU,CAAC7E,UAAU,CAAC,EAAE;QACvK,MAAMF,WAAW,GAAG+E,UAAU,CAAC/E,WAAW;QAC1C,MAAMjR,QAAQ,GAAGiR,WAAW,CAACpR,KAAK;QAClC6P,QAAQ,CAACqC,OAAO,CAACN,MAAM,CAACT,MAAM,CAACgF,UAAU,CAAC,CAAC;QAC3C,IAAIhW,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC2M,UAAU,EAAE;UAC3C,MAAMC,QAAQ,GAAG5M,QAAQ,CAAC4M,QAAQ;UAClC;UACA,MAAMb,IAAI,GAAG;YACX,GAAGiK,UAAU;YACblD;UACF,CAAC;UACDlG,QAAQ,CAACqJ,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGrJ,QAAQ,CAACqJ,YAAY,CAAClK,IAAI,CAAC;UACpEa,QAAQ,CAACsJ,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGtJ,QAAQ,CAACsJ,cAAc,CAACnK,IAAI,CAAC;QAC1E;MACF;IACF;EACF;EACA,SAASoK,aAAaA,CAACpO,KAAK,EAAEpJ,OAAO,EAAE;IACrC,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,OAAO,CAAC4D,MAAM,EAAEhE,CAAC,EAAE,EAAE;MACvC,MAAMyB,QAAQ,GAAGrB,OAAO,CAACJ,CAAC,CAAC,CAACsB,KAAK;MACjCG,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC4M,QAAQ,CAACwJ,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGpW,QAAQ,CAAC4M,QAAQ,CAACwJ,eAAe,CAACrO,KAAK,CAAC;IAC3H;EACF;EACA,SAASsO,aAAaA,CAACpX,IAAI,EAAE;IAC3B;IACA,QAAQA,IAAI;MACV,KAAK,gBAAgB;MACrB,KAAK,iBAAiB;QACpB,OAAO,MAAM4W,aAAa,CAAC,EAAE,CAAC;MAChC,KAAK,sBAAsB;QACzB,OAAO9N,KAAK,IAAI;UACd,MAAM;YACJ2H;UACF,CAAC,GAAGiC,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;UACpB,IAAI,WAAW,IAAIgH,KAAK,IAAI2H,QAAQ,CAAC2B,WAAW,CAAC2C,GAAG,CAACjM,KAAK,CAACwJ,SAAS,CAAC,EAAE;YACrE;YACA;YACA;YACA;YACA+E,qBAAqB,CAAC,MAAM;cAC1B;cACA,IAAI5G,QAAQ,CAAC2B,WAAW,CAAC2C,GAAG,CAACjM,KAAK,CAACwJ,SAAS,CAAC,EAAE;gBAC7C7B,QAAQ,CAAC2B,WAAW,CAACI,MAAM,CAAC1J,KAAK,CAACwJ,SAAS,CAAC;gBAC5CsE,aAAa,CAAC,EAAE,CAAC;cACnB;YACF,CAAC,CAAC;UACJ;QACF,CAAC;IACL;;IAEA;IACA,OAAO,SAASU,WAAWA,CAACxO,KAAK,EAAE;MACjC,MAAM;QACJqO,eAAe;QACf1G;MACF,CAAC,GAAGiC,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;;MAEpB;MACA2O,QAAQ,CAAC8G,SAAS,CAACvT,OAAO,GAAG8E,KAAK;;MAElC;MACA,MAAM0O,aAAa,GAAGxX,IAAI,KAAK,eAAe;MAC9C,MAAMyX,YAAY,GAAGzX,IAAI,KAAK,SAAS,IAAIA,IAAI,KAAK,eAAe,IAAIA,IAAI,KAAK,eAAe;MAC/F,MAAM2S,MAAM,GAAG6E,aAAa,GAAG/D,mBAAmB,GAAGnT,SAAS;MAC9D,MAAMgU,IAAI,GAAGZ,SAAS,CAAC5K,KAAK,EAAE6J,MAAM,CAAC;MACrC,MAAM0C,KAAK,GAAGoC,YAAY,GAAGxE,iBAAiB,CAACnK,KAAK,CAAC,GAAG,CAAC;;MAEzD;MACA,IAAI9I,IAAI,KAAK,eAAe,EAAE;QAC5ByQ,QAAQ,CAAC2C,YAAY,GAAG,CAACtK,KAAK,CAACqK,OAAO,EAAErK,KAAK,CAACwK,OAAO,CAAC;QACtD7C,QAAQ,CAACoC,WAAW,GAAGyB,IAAI,CAACoD,GAAG,CAAC1C,GAAG,IAAIA,GAAG,CAAChD,WAAW,CAAC;MACzD;;MAEA;MACA;MACA,IAAIyF,YAAY,IAAI,CAACnD,IAAI,CAAChR,MAAM,EAAE;QAChC,IAAI+R,KAAK,IAAI,CAAC,EAAE;UACd6B,aAAa,CAACpO,KAAK,EAAE2H,QAAQ,CAACE,WAAW,CAAC;UAC1C,IAAIwG,eAAe,EAAEA,eAAe,CAACrO,KAAK,CAAC;QAC7C;MACF;MACA;MACA,IAAI0O,aAAa,EAAEZ,aAAa,CAACtC,IAAI,CAAC;MACtC,SAASqD,WAAWA,CAAC7K,IAAI,EAAE;QACzB,MAAMkF,WAAW,GAAGlF,IAAI,CAACkF,WAAW;QACpC,MAAMjR,QAAQ,GAAGiR,WAAW,CAACpR,KAAK;;QAElC;QACA,IAAI,EAAEG,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAAC2M,UAAU,CAAC,EAAE;QAChD,MAAMC,QAAQ,GAAG5M,QAAQ,CAAC4M,QAAQ;;QAElC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;QAEQ,IAAI6J,aAAa,EAAE;UACjB;UACA,IAAI7J,QAAQ,CAACiK,aAAa,IAAIjK,QAAQ,CAACkK,cAAc,IAAIlK,QAAQ,CAACqJ,YAAY,IAAIrJ,QAAQ,CAACsJ,cAAc,EAAE;YACzG;YACA,MAAMnC,EAAE,GAAG/C,MAAM,CAACjF,IAAI,CAAC;YACvB,MAAMgL,WAAW,GAAGrH,QAAQ,CAACqC,OAAO,CAAChE,GAAG,CAACgG,EAAE,CAAC;YAC5C,IAAI,CAACgD,WAAW,EAAE;cAChB;cACArH,QAAQ,CAACqC,OAAO,CAAChI,GAAG,CAACgK,EAAE,EAAEhI,IAAI,CAAC;cAC9Ba,QAAQ,CAACiK,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjK,QAAQ,CAACiK,aAAa,CAAC9K,IAAI,CAAC;cACtEa,QAAQ,CAACkK,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGlK,QAAQ,CAACkK,cAAc,CAAC/K,IAAI,CAAC;YAC1E,CAAC,MAAM,IAAIgL,WAAW,CAACtC,OAAO,EAAE;cAC9B;cACA1I,IAAI,CAACyJ,eAAe,CAAC,CAAC;YACxB;UACF;UACA;UACA5I,QAAQ,CAACoK,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGpK,QAAQ,CAACoK,aAAa,CAACjL,IAAI,CAAC;QACxE,CAAC,MAAM;UACL;UACA,MAAMkL,OAAO,GAAGrK,QAAQ,CAAC3N,IAAI,CAAC;UAC9B,IAAIgY,OAAO,EAAE;YACX;YACA;YACA,IAAI,CAACP,YAAY,IAAIhH,QAAQ,CAACoC,WAAW,CAACpF,QAAQ,CAACuE,WAAW,CAAC,EAAE;cAC/D;cACAkF,aAAa,CAACpO,KAAK,EAAE2H,QAAQ,CAACE,WAAW,CAACgC,MAAM,CAACnT,MAAM,IAAI,CAACiR,QAAQ,CAACoC,WAAW,CAACpF,QAAQ,CAACjO,MAAM,CAAC,CAAC,CAAC;cACnG;cACAwY,OAAO,CAAClL,IAAI,CAAC;YACf;UACF,CAAC,MAAM;YACL;YACA,IAAI2K,YAAY,IAAIhH,QAAQ,CAACoC,WAAW,CAACpF,QAAQ,CAACuE,WAAW,CAAC,EAAE;cAC9DkF,aAAa,CAACpO,KAAK,EAAE2H,QAAQ,CAACE,WAAW,CAACgC,MAAM,CAACnT,MAAM,IAAI,CAACiR,QAAQ,CAACoC,WAAW,CAACpF,QAAQ,CAACjO,MAAM,CAAC,CAAC,CAAC;YACrG;UACF;QACF;MACF;MACA4V,gBAAgB,CAACd,IAAI,EAAExL,KAAK,EAAEuM,KAAK,EAAEsC,WAAW,CAAC;IACnD,CAAC;EACH;EACA,OAAO;IACLP;EACF,CAAC;AACH;AAEA,MAAMa,UAAU,GAAGxO,GAAG,IAAI,CAAC,EAAEA,GAAG,IAAI,IAAI,IAAIA,GAAG,CAAC4B,MAAM,CAAC;AACvD,MAAM6M,OAAO,GAAG,aAAapa,KAAK,CAACqa,aAAa,CAAC,IAAI,CAAC;AACtD,MAAMC,WAAW,GAAGA,CAAClH,UAAU,EAAEmH,OAAO,KAAK;EAC3C,MAAMC,SAAS,GAAGla,oBAAoB,CAAC,CAAC0M,GAAG,EAAEgE,GAAG,KAAK;IACnD,MAAMyJ,QAAQ,GAAG,IAAI1a,KAAK,CAAC8X,OAAO,CAAC,CAAC;IACpC,MAAM6C,aAAa,GAAG,IAAI3a,KAAK,CAAC8X,OAAO,CAAC,CAAC;IACzC,MAAM8C,UAAU,GAAG,IAAI5a,KAAK,CAAC8X,OAAO,CAAC,CAAC;IACtC,SAAS+C,kBAAkBA,CAACtH,MAAM,GAAGtC,GAAG,CAAC,CAAC,CAACsC,MAAM,EAAEjR,MAAM,GAAGqY,aAAa,EAAEnH,IAAI,GAAGvC,GAAG,CAAC,CAAC,CAACuC,IAAI,EAAE;MAC5F,MAAM;QACJG,KAAK;QACLG,MAAM;QACND,GAAG;QACHH;MACF,CAAC,GAAGF,IAAI;MACR,MAAMQ,MAAM,GAAGL,KAAK,GAAGG,MAAM;MAC7B,IAAIxR,MAAM,YAAYtC,KAAK,CAAC8X,OAAO,EAAE8C,UAAU,CAAC/I,IAAI,CAACvP,MAAM,CAAC,CAAC,KAAKsY,UAAU,CAAC3N,GAAG,CAAC,GAAG3K,MAAM,CAAC;MAC3F,MAAMwU,QAAQ,GAAGvD,MAAM,CAACuH,gBAAgB,CAACJ,QAAQ,CAAC,CAACK,UAAU,CAACH,UAAU,CAAC;MACzE,IAAIjP,oBAAoB,CAAC4H,MAAM,CAAC,EAAE;QAChC,OAAO;UACLI,KAAK,EAAEA,KAAK,GAAGJ,MAAM,CAACyH,IAAI;UAC1BlH,MAAM,EAAEA,MAAM,GAAGP,MAAM,CAACyH,IAAI;UAC5BnH,GAAG;UACHH,IAAI;UACJuH,MAAM,EAAE,CAAC;UACTnE,QAAQ;UACR9C;QACF,CAAC;MACH,CAAC,MAAM;QACL,MAAMkH,GAAG,GAAG3H,MAAM,CAAC2H,GAAG,GAAGpN,IAAI,CAACqN,EAAE,GAAG,GAAG,CAAC,CAAC;QACxC,MAAMC,CAAC,GAAG,CAAC,GAAGtN,IAAI,CAACuN,GAAG,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGpE,QAAQ,CAAC,CAAC;QAC5C,MAAMwE,CAAC,GAAGF,CAAC,IAAIzH,KAAK,GAAGG,MAAM,CAAC;QAC9B,OAAO;UACLH,KAAK,EAAE2H,CAAC;UACRxH,MAAM,EAAEsH,CAAC;UACTvH,GAAG;UACHH,IAAI;UACJuH,MAAM,EAAEtH,KAAK,GAAG2H,CAAC;UACjBxE,QAAQ;UACR9C;QACF,CAAC;MACH;IACF;IACA,IAAIuH,kBAAkB,GAAG9Y,SAAS;IAClC,MAAM+Y,qBAAqB,GAAGrV,OAAO,IAAI8G,GAAG,CAACjJ,KAAK,KAAK;MACrDyX,WAAW,EAAE;QACX,GAAGzX,KAAK,CAACyX,WAAW;QACpBtV;MACF;IACF,CAAC,CAAC,CAAC;IACH,MAAMyR,OAAO,GAAG,IAAI5X,KAAK,CAAC0b,OAAO,CAAC,CAAC;IACnC,MAAM/J,SAAS,GAAG;MAChB1E,GAAG;MACHgE,GAAG;MACH;MACA0K,EAAE,EAAE,IAAI;MACRpI,MAAM,EAAE,IAAI;MACZ2C,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE;QACNY,QAAQ,EAAE,CAAC;QACXT,OAAO,EAAE,IAAI;QACbsF,SAAS,EAAE;MACb,CAAC;MACD1X,KAAK,EAAE,IAAI;MACX2X,EAAE,EAAE,IAAI;MACRxI,UAAU,EAAEA,CAACD,MAAM,GAAG,CAAC,KAAKC,UAAU,CAACpC,GAAG,CAAC,CAAC,EAAEmC,MAAM,CAAC;MACrDoH,OAAO,EAAEA,CAACsB,SAAS,EAAEC,gBAAgB,KAAKvB,OAAO,CAACsB,SAAS,EAAEC,gBAAgB,EAAE9K,GAAG,CAAC,CAAC,CAAC;MACrF+K,MAAM,EAAE,KAAK;MACb5J,MAAM,EAAE,KAAK;MACb6J,IAAI,EAAE,KAAK;MACXC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,IAAInc,KAAK,CAACoc,KAAK,CAAC,CAAC;MACxBxE,OAAO;MACPyE,KAAK,EAAEzE,OAAO;MACd0E,SAAS,EAAE,QAAQ;MACnBhD,eAAe,EAAE7W,SAAS;MAC1BgZ,WAAW,EAAE;QACXtV,OAAO,EAAE,CAAC;QACV4H,GAAG,EAAE,GAAG;QACRC,GAAG,EAAE,CAAC;QACNuO,QAAQ,EAAE,GAAG;QACbC,OAAO,EAAEA,CAAA,KAAM;UACb,MAAMxY,KAAK,GAAGiN,GAAG,CAAC,CAAC;UACnB;UACA,IAAIsK,kBAAkB,EAAE5R,YAAY,CAAC4R,kBAAkB,CAAC;UACxD;UACA,IAAIvX,KAAK,CAACyX,WAAW,CAACtV,OAAO,KAAKnC,KAAK,CAACyX,WAAW,CAAC1N,GAAG,EAAEyN,qBAAqB,CAACxX,KAAK,CAACyX,WAAW,CAAC1N,GAAG,CAAC;UACrG;UACAwN,kBAAkB,GAAG9R,UAAU,CAAC,MAAM+R,qBAAqB,CAACvK,GAAG,CAAC,CAAC,CAACwK,WAAW,CAACzN,GAAG,CAAC,EAAEhK,KAAK,CAACyX,WAAW,CAACc,QAAQ,CAAC;QACjH;MACF,CAAC;MACD/I,IAAI,EAAE;QACJG,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE,CAAC;QACTD,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE;MACR,CAAC;MACD+I,QAAQ,EAAE;QACRC,UAAU,EAAE,CAAC;QACb/O,GAAG,EAAE,CAAC;QACNgG,KAAK,EAAE,CAAC;QACRG,MAAM,EAAE,CAAC;QACTD,GAAG,EAAE,CAAC;QACNH,IAAI,EAAE,CAAC;QACPM,MAAM,EAAE,CAAC;QACT8C,QAAQ,EAAE,CAAC;QACXmE,MAAM,EAAE,CAAC;QACTJ;MACF,CAAC;MACD8B,SAAS,EAAExG,MAAM,IAAIlJ,GAAG,CAACjJ,KAAK,KAAK;QACjC,GAAGA,KAAK;QACRmS,MAAM,EAAE;UACN,GAAGnS,KAAK,CAACmS,MAAM;UACf,GAAGA;QACL;MACF,CAAC,CAAC,CAAC;MACHyG,OAAO,EAAEA,CAACjJ,KAAK,EAAEG,MAAM,EAAED,GAAG,GAAG,CAAC,EAAEH,IAAI,GAAG,CAAC,KAAK;QAC7C,MAAMH,MAAM,GAAGtC,GAAG,CAAC,CAAC,CAACsC,MAAM;QAC3B,MAAMC,IAAI,GAAG;UACXG,KAAK;UACLG,MAAM;UACND,GAAG;UACHH;QACF,CAAC;QACDzG,GAAG,CAACjJ,KAAK,KAAK;UACZwP,IAAI;UACJiJ,QAAQ,EAAE;YACR,GAAGzY,KAAK,CAACyY,QAAQ;YACjB,GAAG5B,kBAAkB,CAACtH,MAAM,EAAEoH,aAAa,EAAEnH,IAAI;UACnD;QACF,CAAC,CAAC,CAAC;MACL,CAAC;MACDqJ,MAAM,EAAElP,GAAG,IAAIV,GAAG,CAACjJ,KAAK,IAAI;QAC1B,MAAM8Y,QAAQ,GAAGpP,YAAY,CAACC,GAAG,CAAC;QAClC,OAAO;UACL8O,QAAQ,EAAE;YACR,GAAGzY,KAAK,CAACyY,QAAQ;YACjB9O,GAAG,EAAEmP,QAAQ;YACbJ,UAAU,EAAE1Y,KAAK,CAACyY,QAAQ,CAACC,UAAU,IAAII;UAC3C;QACF,CAAC;MACH,CAAC,CAAC;MACFC,YAAY,EAAEA,CAACT,SAAS,GAAG,QAAQ,KAAK;QACtC,MAAMH,KAAK,GAAGlL,GAAG,CAAC,CAAC,CAACkL,KAAK;;QAEzB;QACAA,KAAK,CAACa,IAAI,CAAC,CAAC;QACZb,KAAK,CAACc,WAAW,GAAG,CAAC;QACrB,IAAIX,SAAS,KAAK,OAAO,EAAE;UACzBH,KAAK,CAACe,KAAK,CAAC,CAAC;UACbf,KAAK,CAACc,WAAW,GAAG,CAAC;QACvB;QACAhQ,GAAG,CAAC,OAAO;UACTqP;QACF,CAAC,CAAC,CAAC;MACL,CAAC;MACDjR,YAAY,EAAE5I,SAAS;MACvBmQ,QAAQ,EAAE;QACR;QACAE,WAAW,EAAE,EAAE;QACfmC,OAAO,EAAE,IAAIpE,GAAG,CAAC,CAAC;QAClBsM,WAAW,EAAE,EAAE;QACf5H,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACpBP,WAAW,EAAE,EAAE;QACfT,WAAW,EAAE,IAAI1D,GAAG,CAAC,CAAC;QACtB6I,SAAS,EAAE,aAAazZ,KAAK,CAACmd,SAAS,CAAC,CAAC;QACzC;QACAC,MAAM,EAAE,KAAK;QACbjK,MAAM,EAAE,CAAC;QACT2D,QAAQ,EAAE,CAAC;QACXuG,SAAS,EAAEA,CAACtX,GAAG,EAAE+Q,QAAQ,EAAElC,KAAK,KAAK;UACnC,MAAMjC,QAAQ,GAAG3B,GAAG,CAAC,CAAC,CAAC2B,QAAQ;UAC/B;UACA;UACA;UACA;UACAA,QAAQ,CAACmE,QAAQ,GAAGnE,QAAQ,CAACmE,QAAQ,IAAIA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;UAC9DnE,QAAQ,CAACuK,WAAW,CAACrY,IAAI,CAAC;YACxBkB,GAAG;YACH+Q,QAAQ;YACRlC;UACF,CAAC,CAAC;UACF;UACA;UACAjC,QAAQ,CAACuK,WAAW,GAAGvK,QAAQ,CAACuK,WAAW,CAACxG,IAAI,CAAC,CAACvI,CAAC,EAAEQ,CAAC,KAAKR,CAAC,CAAC2I,QAAQ,GAAGnI,CAAC,CAACmI,QAAQ,CAAC;UACnF,OAAO,MAAM;YACX,MAAMnE,QAAQ,GAAG3B,GAAG,CAAC,CAAC,CAAC2B,QAAQ;YAC/B,IAAIA,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACuK,WAAW,EAAE;cAC5C;cACAvK,QAAQ,CAACmE,QAAQ,GAAGnE,QAAQ,CAACmE,QAAQ,IAAIA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;cAC9D;cACAnE,QAAQ,CAACuK,WAAW,GAAGvK,QAAQ,CAACuK,WAAW,CAACrI,MAAM,CAACyI,CAAC,IAAIA,CAAC,CAACvX,GAAG,KAAKA,GAAG,CAAC;YACxE;UACF,CAAC;QACH;MACF;IACF,CAAC;IACD,OAAO2L,SAAS;EAClB,CAAC,CAAC;EACF,MAAM3N,KAAK,GAAGyW,SAAS,CAACxW,QAAQ,CAAC,CAAC;EAClC,IAAIuZ,OAAO,GAAGxZ,KAAK,CAACwP,IAAI;EACxB,IAAIiK,MAAM,GAAGzZ,KAAK,CAACyY,QAAQ,CAAC9O,GAAG;EAC/B,IAAI+P,SAAS,GAAG1Z,KAAK,CAACuP,MAAM;EAC5BkH,SAAS,CAAC6C,SAAS,CAAC,MAAM;IACxB,MAAM;MACJ/J,MAAM;MACNC,IAAI;MACJiJ,QAAQ;MACRd,EAAE;MACF1O;IACF,CAAC,GAAGwN,SAAS,CAACxW,QAAQ,CAAC,CAAC;;IAExB;IACA,IAAIuP,IAAI,CAACG,KAAK,KAAK6J,OAAO,CAAC7J,KAAK,IAAIH,IAAI,CAACM,MAAM,KAAK0J,OAAO,CAAC1J,MAAM,IAAI2I,QAAQ,CAAC9O,GAAG,KAAK8P,MAAM,EAAE;MAC7FD,OAAO,GAAGhK,IAAI;MACdiK,MAAM,GAAGhB,QAAQ,CAAC9O,GAAG;MACrB;MACA2F,YAAY,CAACC,MAAM,EAAEC,IAAI,CAAC;MAC1BmI,EAAE,CAACgC,aAAa,CAAClB,QAAQ,CAAC9O,GAAG,CAAC;MAC9B,MAAMiQ,WAAW,GAAG,OAAOC,iBAAiB,KAAK,WAAW,IAAIlC,EAAE,CAACmC,UAAU,YAAYD,iBAAiB;MAC1GlC,EAAE,CAACiB,OAAO,CAACpJ,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACM,MAAM,EAAE8J,WAAW,CAAC;IAClD;;IAEA;IACA,IAAIrK,MAAM,KAAKmK,SAAS,EAAE;MACxBA,SAAS,GAAGnK,MAAM;MAClB;MACAtG,GAAG,CAACjJ,KAAK,KAAK;QACZyY,QAAQ,EAAE;UACR,GAAGzY,KAAK,CAACyY,QAAQ;UACjB,GAAGzY,KAAK,CAACyY,QAAQ,CAAC5B,kBAAkB,CAACtH,MAAM;QAC7C;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;;EAEF;EACAkH,SAAS,CAAC6C,SAAS,CAACtZ,KAAK,IAAIqP,UAAU,CAACrP,KAAK,CAAC,CAAC;;EAE/C;EACA,OAAOyW,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,SAASsD,iBAAiBA,CAAC/X,GAAG,EAAE;EAC9B,MAAM9C,QAAQ,GAAGjD,KAAK,CAACwM,MAAM,CAAC,IAAI,CAAC;EACnCxM,KAAK,CAAC+d,mBAAmB,CAAC9a,QAAQ,EAAE,MAAM8C,GAAG,CAACG,OAAO,CAACpD,KAAK,EAAE,CAACiD,GAAG,CAAC,CAAC;EACnE,OAAO9C,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA,SAAS+a,QAAQA,CAAA,EAAG;EAClB,MAAMpJ,KAAK,GAAG5U,KAAK,CAACie,UAAU,CAAC7D,OAAO,CAAC;EACvC,IAAI,CAACxF,KAAK,EAAE,MAAM,IAAItS,KAAK,CAAC,0DAA0D,CAAC;EACvF,OAAOsS,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA,SAASsJ,QAAQA,CAACC,QAAQ,GAAGpa,KAAK,IAAIA,KAAK,EAAEqa,UAAU,EAAE;EACvD,OAAOJ,QAAQ,CAAC,CAAC,CAACG,QAAQ,EAAEC,UAAU,CAAC;AACzC;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAC7G,QAAQ,EAAE8G,cAAc,GAAG,CAAC,EAAE;EAC9C,MAAM1J,KAAK,GAAGoJ,QAAQ,CAAC,CAAC;EACxB,MAAMX,SAAS,GAAGzI,KAAK,CAAC5Q,QAAQ,CAAC,CAAC,CAAC2O,QAAQ,CAAC0K,SAAS;EACrD;EACA,MAAMtX,GAAG,GAAGuG,kBAAkB,CAACkL,QAAQ,CAAC;EACxC;EACAzL,yBAAyB,CAAC,MAAMsR,SAAS,CAACtX,GAAG,EAAEuY,cAAc,EAAE1J,KAAK,CAAC,EAAE,CAAC0J,cAAc,EAAEjB,SAAS,EAAEzI,KAAK,CAAC,CAAC;EAC1G,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA,SAAS2J,QAAQA,CAAC7c,MAAM,EAAE;EACxB,OAAO1B,KAAK,CAAC2M,OAAO,CAAC,MAAMoC,UAAU,CAACrN,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;AAC1D;AACA,MAAM8c,eAAe,GAAG,IAAIC,OAAO,CAAC,CAAC;AACrC,MAAMC,aAAa,GAAGvW,KAAK,IAAI;EAC7B,IAAIwW,gBAAgB;EACpB,OAAO,OAAOxW,KAAK,KAAK,UAAU,IAAI,CAACA,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACwW,gBAAgB,GAAGxW,KAAK,CAACyW,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,gBAAgB,CAACxR,WAAW,MAAMhF,KAAK;AACjK,CAAC;AACD,SAAS0W,SAASA,CAACC,UAAU,EAAEC,UAAU,EAAE;EACzC,OAAO,gBAAgBC,KAAK,EAAE,GAAGC,KAAK,EAAE;IACtC,IAAIC,MAAM;;IAEV;IACA,IAAIR,aAAa,CAACM,KAAK,CAAC,EAAE;MACxBE,MAAM,GAAGV,eAAe,CAACxN,GAAG,CAACgO,KAAK,CAAC;MACnC,IAAI,CAACE,MAAM,EAAE;QACXA,MAAM,GAAG,IAAIF,KAAK,CAAC,CAAC;QACpBR,eAAe,CAACxR,GAAG,CAACgS,KAAK,EAAEE,MAAM,CAAC;MACpC;IACF,CAAC,MAAM;MACLA,MAAM,GAAGF,KAAK;IAChB;;IAEA;IACA,IAAIF,UAAU,EAAEA,UAAU,CAACI,MAAM,CAAC;;IAElC;IACA,OAAOjS,OAAO,CAACkS,GAAG,CAACF,KAAK,CAACrF,GAAG,CAACqF,KAAK,IAAI,IAAIhS,OAAO,CAAC,CAACmS,GAAG,EAAEC,MAAM,KAAKH,MAAM,CAACI,IAAI,CAACL,KAAK,EAAEjQ,IAAI,IAAIoQ,GAAG,CAAC7b,UAAU,CAACyL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC/K,KAAK,CAAC,GAAG9C,MAAM,CAACW,MAAM,CAACkN,IAAI,EAAED,UAAU,CAACC,IAAI,CAAC/K,KAAK,CAAC,CAAC,GAAG+K,IAAI,CAAC,EAAE+P,UAAU,EAAE3R,KAAK,IAAIiS,MAAM,CAAC,IAAI/c,KAAK,CAAC,kBAAkB2c,KAAK,KAAK7R,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACmS,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1T,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACN,MAAM,EAAED,KAAK,EAAEH,UAAU,EAAEC,UAAU,EAAE;EACxD;EACA,MAAMvW,IAAI,GAAG/F,KAAK,CAACC,OAAO,CAACuc,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,MAAMQ,OAAO,GAAG1e,OAAO,CAAC8d,SAAS,CAACC,UAAU,EAAEC,UAAU,CAAC,EAAE,CAACG,MAAM,EAAE,GAAG1W,IAAI,CAAC,EAAE;IAC5EkX,KAAK,EAAExR,EAAE,CAACQ;EACZ,CAAC,CAAC;EACF;EACA,OAAOjM,KAAK,CAACC,OAAO,CAACuc,KAAK,CAAC,GAAGQ,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;AACpD;;AAEA;AACA;AACA;AACAD,SAAS,CAACxe,OAAO,GAAG,UAAUke,MAAM,EAAED,KAAK,EAAEH,UAAU,EAAE;EACvD,MAAMtW,IAAI,GAAG/F,KAAK,CAACC,OAAO,CAACuc,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,OAAOje,OAAO,CAAC6d,SAAS,CAACC,UAAU,CAAC,EAAE,CAACI,MAAM,EAAE,GAAG1W,IAAI,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACAgX,SAAS,CAACve,KAAK,GAAG,UAAUie,MAAM,EAAED,KAAK,EAAE;EACzC,MAAMzW,IAAI,GAAG/F,KAAK,CAACC,OAAO,CAACuc,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC;EACnD,OAAOhe,KAAK,CAAC,CAACie,MAAM,EAAE,GAAG1W,IAAI,CAAC,CAAC;AACjC,CAAC;AAED,MAAMmX,MAAM,GAAG,IAAI/O,GAAG,CAAC,CAAC;AACxB,MAAMgP,YAAY,GAAG;EACnBhe,OAAO,EAAE,SAAS;EAClBgL,MAAM,EAAE;AACV,CAAC;AACD,MAAMiT,sBAAsB,GAAGA,CAACnE,EAAE,EAAEoE,MAAM,KAAK;EAC7C,MAAMC,cAAc,GAAG,OAAOrE,EAAE,KAAK,UAAU,GAAGA,EAAE,CAACoE,MAAM,CAAC,GAAGpE,EAAE;EACjE,IAAIvB,UAAU,CAAC4F,cAAc,CAAC,EAAE,OAAOA,cAAc;EACrD,OAAO,IAAIhgB,KAAK,CAACigB,aAAa,CAAC;IAC7BC,eAAe,EAAE,kBAAkB;IACnCH,MAAM,EAAEA,MAAM;IACdI,SAAS,EAAE,IAAI;IACfC,KAAK,EAAE,IAAI;IACX,GAAGzE;EACL,CAAC,CAAC;AACJ,CAAC;AACD,SAAS0E,kBAAkBA,CAACN,MAAM,EAAEvM,IAAI,EAAE;EACxC,IAAI,CAACA,IAAI,IAAIuM,MAAM,YAAYlC,iBAAiB,IAAIkC,MAAM,CAACO,aAAa,EAAE;IACxE,MAAM;MACJ3M,KAAK;MACLG,MAAM;MACND,GAAG;MACHH;IACF,CAAC,GAAGqM,MAAM,CAACO,aAAa,CAACC,qBAAqB,CAAC,CAAC;IAChD,OAAO;MACL5M,KAAK;MACLG,MAAM;MACND,GAAG;MACHH;IACF,CAAC;EACH,CAAC,MAAM,IAAI,CAACF,IAAI,IAAI,OAAOgN,eAAe,KAAK,WAAW,IAAIT,MAAM,YAAYS,eAAe,EAAE;IAC/F,OAAO;MACL7M,KAAK,EAAEoM,MAAM,CAACpM,KAAK;MACnBG,MAAM,EAAEiM,MAAM,CAACjM,MAAM;MACrBD,GAAG,EAAE,CAAC;MACNH,IAAI,EAAE;IACR,CAAC;EACH;EACA,OAAO;IACLC,KAAK,EAAE,CAAC;IACRG,MAAM,EAAE,CAAC;IACTD,GAAG,EAAE,CAAC;IACNH,IAAI,EAAE,CAAC;IACP,GAAGF;EACL,CAAC;AACH;AACA,SAASiN,UAAUA,CAACV,MAAM,EAAE;EAC1B;EACA,MAAMW,QAAQ,GAAGd,MAAM,CAAC3O,GAAG,CAAC8O,MAAM,CAAC;EACnC,MAAMY,SAAS,GAAGD,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC9a,KAAK;EAC5D,MAAMgb,SAAS,GAAGF,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC7L,KAAK;EAC5D,IAAI6L,QAAQ,EAAEja,OAAO,CAACC,IAAI,CAAC,4CAA4C,CAAC;;EAExE;EACA;EACA,MAAMma,mBAAmB,GAAG,OAAOC,WAAW,KAAK,UAAU;EAC7D;EACA;EACAA,WAAW;EACX;EACAra,OAAO,CAAC4G,KAAK;;EAEb;EACA,MAAMwH,KAAK,GAAG+L,SAAS,IAAIrG,WAAW,CAAClH,UAAU,EAAEmH,OAAO,CAAC;EAC3D;EACA,MAAM5U,KAAK,GAAG+a,SAAS,IAAI5Z,UAAU,CAACga,eAAe,CAAClM,KAAK;EAC3D;EACAvU,cAAc;EACd;EACA,IAAI;EACJ;EACA,KAAK;EACL;EACA,IAAI;EACJ;EACA,EAAE;EACF;EACAugB,mBAAmB;EACnB;EACAA,mBAAmB;EACnB;EACAA,mBAAmB;EACnB;EACA,IAAI,CAAC;EACL,CAAC;EACD;EACA,IAAI,CAACH,QAAQ,EAAEd,MAAM,CAAC3S,GAAG,CAAC8S,MAAM,EAAE;IAChCna,KAAK;IACLiP;EACF,CAAC,CAAC;;EAEF;EACA,IAAImM,SAAS;EACb,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,UAAU;EACd,OAAO;IACLC,SAASA,CAACjf,KAAK,GAAG,CAAC,CAAC,EAAE;MACpB,IAAI;QACFyZ,EAAE,EAAEyF,QAAQ;QACZ5N,IAAI,EAAE6N,SAAS;QACfnd,KAAK,EAAEod,YAAY;QACnBnL,MAAM;QACN6K,SAAS,EAAEO,iBAAiB;QAC5BC,OAAO,GAAG,KAAK;QACfpP,MAAM,GAAG,KAAK;QACd6J,IAAI,GAAG,KAAK;QACZD,MAAM,GAAG,KAAK;QACdyF,YAAY,GAAG,KAAK;QACpBnF,SAAS,GAAG,QAAQ;QACpB3O,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;QACZ8N,WAAW;QACXvF,SAAS,EAAEwL,cAAc;QACzBnO,MAAM,EAAEoO,aAAa;QACrBrI;MACF,CAAC,GAAGpX,KAAK;MACT,IAAI8B,KAAK,GAAG6Q,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;;MAE5B;MACA,IAAI0X,EAAE,GAAG3X,KAAK,CAAC2X,EAAE;MACjB,IAAI,CAAC3X,KAAK,CAAC2X,EAAE,EAAE3X,KAAK,CAACiJ,GAAG,CAAC;QACvB0O,EAAE,EAAEA,EAAE,GAAGmE,sBAAsB,CAACsB,QAAQ,EAAErB,MAAM;MAClD,CAAC,CAAC;;MAEF;MACA,IAAI7J,SAAS,GAAGlS,KAAK,CAACkS,SAAS;MAC/B,IAAI,CAACA,SAAS,EAAElS,KAAK,CAACiJ,GAAG,CAAC;QACxBiJ,SAAS,EAAEA,SAAS,GAAG,IAAIlW,KAAK,CAAC4hB,SAAS,CAAC;MAC7C,CAAC,CAAC;;MAEF;MACA,MAAM;QACJC,MAAM;QACN,GAAGC;MACL,CAAC,GAAGJ,cAAc,IAAI,CAAC,CAAC;MACxB,IAAI,CAACvT,EAAE,CAACQ,GAAG,CAACmT,OAAO,EAAE5L,SAAS,EAAE2J,YAAY,CAAC,EAAExb,UAAU,CAAC6R,SAAS,EAAE;QACnE,GAAG4L;MACL,CAAC,CAAC;MACF,IAAI,CAAC3T,EAAE,CAACQ,GAAG,CAACkT,MAAM,EAAE3L,SAAS,CAAC2L,MAAM,EAAEhC,YAAY,CAAC,EAAExb,UAAU,CAAC6R,SAAS,EAAE;QACzE2L,MAAM,EAAE;UACN,GAAG3L,SAAS,CAAC2L,MAAM;UACnB,GAAGA;QACL;MACF,CAAC,CAAC;;MAEF;MACA,IAAI,CAAC7d,KAAK,CAACuP,MAAM,IAAIvP,KAAK,CAACuP,MAAM,KAAK2N,UAAU,IAAI,CAAC/S,EAAE,CAACQ,GAAG,CAACuS,UAAU,EAAES,aAAa,EAAE9B,YAAY,CAAC,EAAE;QACpGqB,UAAU,GAAGS,aAAa;QAC1B,MAAMI,QAAQ,GAAGJ,aAAa,YAAY3hB,KAAK,CAACgiB,MAAM;QACtD,MAAMzO,MAAM,GAAGwO,QAAQ,GAAGJ,aAAa,GAAGF,YAAY,GAAG,IAAIzhB,KAAK,CAACiiB,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,IAAIjiB,KAAK,CAACkiB,iBAAiB,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;QAC5J,IAAI,CAACH,QAAQ,EAAE;UACbxO,MAAM,CAACmH,QAAQ,CAACyH,CAAC,GAAG,CAAC;UACrB,IAAIR,aAAa,EAAE;YACjBtd,UAAU,CAACkP,MAAM,EAAEoO,aAAa,CAAC;YACjC;YACA;YACA,IAAI,CAACpO,MAAM,CAACE,MAAM,EAAE;cAClB,IAAI,QAAQ,IAAIkO,aAAa,IAAI,MAAM,IAAIA,aAAa,IAAI,OAAO,IAAIA,aAAa,IAAI,QAAQ,IAAIA,aAAa,IAAI,KAAK,IAAIA,aAAa,EAAE;gBAC3IpO,MAAM,CAACE,MAAM,GAAG,IAAI;gBACpBF,MAAM,CAACU,sBAAsB,CAAC,CAAC;cACjC;YACF;UACF;UACA;UACA,IAAI,CAACjQ,KAAK,CAACuP,MAAM,IAAI,EAAEoO,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACS,QAAQ,CAAC,EAAE7O,MAAM,CAAC8O,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QACjG;QACAre,KAAK,CAACiJ,GAAG,CAAC;UACRsG;QACF,CAAC,CAAC;;QAEF;QACA;QACA2C,SAAS,CAAC3C,MAAM,GAAGA,MAAM;MAC3B;;MAEA;MACA,IAAI,CAACvP,KAAK,CAACE,KAAK,EAAE;QAChB,IAAIA,KAAK;QACT,IAAIod,YAAY,YAAYthB,KAAK,CAACsiB,KAAK,EAAE;UACvCpe,KAAK,GAAGod,YAAY;UACpBte,OAAO,CAACkB,KAAK,EAAE2Q,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;QAC/B,CAAC,MAAM;UACL3Q,KAAK,GAAG,IAAIlE,KAAK,CAACsiB,KAAK,CAAC,CAAC;UACzBtf,OAAO,CAACkB,KAAK,EAAE2Q,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;UAC7B,IAAIyM,YAAY,EAAEjd,UAAU,CAACH,KAAK,EAAEod,YAAY,CAAC;QACnD;QACAtd,KAAK,CAACiJ,GAAG,CAAC;UACR/I;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI,CAACF,KAAK,CAAC6X,EAAE,EAAE;QACb;QACA,MAAM0G,aAAa,GAAGA,CAACzG,SAAS,EAAE0G,KAAK,KAAK;UAC1C,MAAMxe,KAAK,GAAG6Q,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;UAC9B,IAAID,KAAK,CAACsY,SAAS,KAAK,OAAO,EAAE;UACjC9B,OAAO,CAACsB,SAAS,EAAE,IAAI,EAAE9X,KAAK,EAAEwe,KAAK,CAAC;QACxC,CAAC;;QAED;QACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;UAChC,MAAMze,KAAK,GAAG6Q,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;UAC9BD,KAAK,CAAC2X,EAAE,CAACE,EAAE,CAACvF,OAAO,GAAGtS,KAAK,CAAC2X,EAAE,CAACE,EAAE,CAAC6G,YAAY;UAC9C1e,KAAK,CAAC2X,EAAE,CAACE,EAAE,CAAC8G,gBAAgB,CAAC3e,KAAK,CAAC2X,EAAE,CAACE,EAAE,CAAC6G,YAAY,GAAGH,aAAa,GAAG,IAAI,CAAC;UAC7E,IAAI,CAACve,KAAK,CAAC2X,EAAE,CAACE,EAAE,CAAC6G,YAAY,EAAErP,UAAU,CAACrP,KAAK,CAAC;QAClD,CAAC;;QAED;QACA,MAAM6X,EAAE,GAAG;UACT+G,OAAOA,CAAA,EAAG;YACR,MAAMjH,EAAE,GAAG9G,KAAK,CAAC5Q,QAAQ,CAAC,CAAC,CAAC0X,EAAE;YAC9BA,EAAE,CAACE,EAAE,CAACgH,gBAAgB,CAAC,cAAc,EAAEJ,mBAAmB,CAAC;YAC3D9G,EAAE,CAACE,EAAE,CAACgH,gBAAgB,CAAC,YAAY,EAAEJ,mBAAmB,CAAC;UAC3D,CAAC;UACDK,UAAUA,CAAA,EAAG;YACX,MAAMnH,EAAE,GAAG9G,KAAK,CAAC5Q,QAAQ,CAAC,CAAC,CAAC0X,EAAE;YAC9BA,EAAE,CAACE,EAAE,CAACkH,mBAAmB,CAAC,cAAc,EAAEN,mBAAmB,CAAC;YAC9D9G,EAAE,CAACE,EAAE,CAACkH,mBAAmB,CAAC,YAAY,EAAEN,mBAAmB,CAAC;UAC9D;QACF,CAAC;;QAED;QACA,IAAI9G,EAAE,CAACE,EAAE,EAAEA,EAAE,CAAC+G,OAAO,CAAC,CAAC;QACvB5e,KAAK,CAACiJ,GAAG,CAAC;UACR4O;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIF,EAAE,CAACqH,SAAS,EAAE;QAChB,MAAMC,UAAU,GAAGtH,EAAE,CAACqH,SAAS,CAAC1M,OAAO;QACvC,MAAM4M,OAAO,GAAGvH,EAAE,CAACqH,SAAS,CAAC/gB,IAAI;QACjC0Z,EAAE,CAACqH,SAAS,CAAC1M,OAAO,GAAG,CAAC,CAACkL,OAAO;QAChC,IAAIrT,EAAE,CAACM,GAAG,CAAC+S,OAAO,CAAC,EAAE;UACnB7F,EAAE,CAACqH,SAAS,CAAC/gB,IAAI,GAAGjC,KAAK,CAACmjB,gBAAgB;QAC5C,CAAC,MAAM,IAAIhV,EAAE,CAACI,GAAG,CAACiT,OAAO,CAAC,EAAE;UAC1B,IAAI4B,cAAc;UAClB,MAAMC,KAAK,GAAG;YACZC,KAAK,EAAEtjB,KAAK,CAACujB,cAAc;YAC3BC,UAAU,EAAExjB,KAAK,CAACyjB,YAAY;YAC9BC,IAAI,EAAE1jB,KAAK,CAACmjB,gBAAgB;YAC5BQ,QAAQ,EAAE3jB,KAAK,CAAC4jB;UAClB,CAAC;UACDjI,EAAE,CAACqH,SAAS,CAAC/gB,IAAI,GAAG,CAACmhB,cAAc,GAAGC,KAAK,CAAC7B,OAAO,CAAC,KAAK,IAAI,GAAG4B,cAAc,GAAGpjB,KAAK,CAACmjB,gBAAgB;QACzG,CAAC,MAAM,IAAIhV,EAAE,CAACrC,GAAG,CAAC0V,OAAO,CAAC,EAAE;UAC1BpgB,MAAM,CAACW,MAAM,CAAC4Z,EAAE,CAACqH,SAAS,EAAExB,OAAO,CAAC;QACtC;QACA,IAAIyB,UAAU,KAAKtH,EAAE,CAACqH,SAAS,CAAC1M,OAAO,IAAI4M,OAAO,KAAKvH,EAAE,CAACqH,SAAS,CAAC/gB,IAAI,EAAE0Z,EAAE,CAACqH,SAAS,CAACa,WAAW,GAAG,IAAI;MAC3G;;MAEA;MACA;MACA,MAAMpY,eAAe,GAAGF,kBAAkB,CAAC,CAAC;MAC5C,IAAIE,eAAe,EAAE;QACnB,IAAI,SAAS,IAAIA,eAAe,EAAEA,eAAe,CAAC6K,OAAO,GAAG,CAAC0F,MAAM,CAAC,KAAK,IAAI,YAAY,IAAIvQ,eAAe,EAAEA,eAAe,CAACqY,UAAU,GAAG9H,MAAM;MACnJ;;MAEA;MACA,IAAI,CAACiF,UAAU,EAAE;QACf,MAAM8C,cAAc,GAAG,IAAI;QAC3B,MAAMzS,YAAY,GAAG,IAAI;QACzBjN,UAAU,CAACsX,EAAE,EAAE;UACbqI,cAAc,EAAE5R,MAAM,GAAG2R,cAAc,GAAGzS,YAAY;UACtD2S,WAAW,EAAEhI,IAAI,GAAGjc,KAAK,CAACkkB,aAAa,GAAGlkB,KAAK,CAACmkB;QAClD,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIngB,KAAK,CAACgY,MAAM,KAAKA,MAAM,EAAEhY,KAAK,CAACiJ,GAAG,CAAC,OAAO;QAC5C+O;MACF,CAAC,CAAC,CAAC;MACH,IAAIhY,KAAK,CAACoO,MAAM,KAAKA,MAAM,EAAEpO,KAAK,CAACiJ,GAAG,CAAC,OAAO;QAC5CmF;MACF,CAAC,CAAC,CAAC;MACH,IAAIpO,KAAK,CAACiY,IAAI,KAAKA,IAAI,EAAEjY,KAAK,CAACiJ,GAAG,CAAC,OAAO;QACxCgP;MACF,CAAC,CAAC,CAAC;;MAEH;MACA,IAAImF,QAAQ,IAAI,CAACjT,EAAE,CAACG,GAAG,CAAC8S,QAAQ,CAAC,IAAI,CAAChH,UAAU,CAACgH,QAAQ,CAAC,IAAI,CAACjT,EAAE,CAACQ,GAAG,CAACyS,QAAQ,EAAEzF,EAAE,EAAEkE,YAAY,CAAC,EAAExb,UAAU,CAACsX,EAAE,EAAEyF,QAAQ,CAAC;MAC3H;MACA,IAAIjL,MAAM,IAAI,CAACnS,KAAK,CAACmS,MAAM,CAACrG,QAAQ,EAAE9L,KAAK,CAACiJ,GAAG,CAAC;QAC9CkJ,MAAM,EAAEA,MAAM,CAACtB,KAAK;MACtB,CAAC,CAAC;MACF;MACA,MAAMrB,IAAI,GAAG6M,kBAAkB,CAACN,MAAM,EAAEsB,SAAS,CAAC;MAClD,IAAI,CAAClT,EAAE,CAACQ,GAAG,CAAC6E,IAAI,EAAExP,KAAK,CAACwP,IAAI,EAAEqM,YAAY,CAAC,EAAE;QAC3C7b,KAAK,CAAC4Y,OAAO,CAACpJ,IAAI,CAACG,KAAK,EAAEH,IAAI,CAACM,MAAM,EAAEN,IAAI,CAACK,GAAG,EAAEL,IAAI,CAACE,IAAI,CAAC;MAC7D;MACA;MACA,IAAI/F,GAAG,IAAI3J,KAAK,CAACyY,QAAQ,CAAC9O,GAAG,KAAKD,YAAY,CAACC,GAAG,CAAC,EAAE3J,KAAK,CAAC6Y,MAAM,CAAClP,GAAG,CAAC;MACtE;MACA,IAAI3J,KAAK,CAACsY,SAAS,KAAKA,SAAS,EAAEtY,KAAK,CAAC+Y,YAAY,CAACT,SAAS,CAAC;MAChE;MACA,IAAI,CAACtY,KAAK,CAACsV,eAAe,EAAEtV,KAAK,CAACiJ,GAAG,CAAC;QACpCqM;MACF,CAAC,CAAC;MACF;MACA,IAAImC,WAAW,IAAI,CAACtN,EAAE,CAACQ,GAAG,CAAC8M,WAAW,EAAEzX,KAAK,CAACyX,WAAW,EAAEoE,YAAY,CAAC,EAAE7b,KAAK,CAACiJ,GAAG,CAACjJ,KAAK,KAAK;QAC5FyX,WAAW,EAAE;UACX,GAAGzX,KAAK,CAACyX,WAAW;UACpB,GAAGA;QACL;MACF,CAAC,CAAC,CAAC;;MAEH;MACAuF,SAAS,GAAGO,iBAAiB;MAC7BN,UAAU,GAAG,IAAI;MACjB,OAAO,IAAI;IACb,CAAC;IACDzT,MAAMA,CAACjJ,QAAQ,EAAE;MACf;MACA,IAAI,CAAC0c,UAAU,EAAE,IAAI,CAACE,SAAS,CAAC,CAAC;MACjCpa,UAAU,CAACqd,eAAe,CAAE,aAAatjB,GAAG,CAACujB,QAAQ,EAAE;QACrDxP,KAAK,EAAEA,KAAK;QACZtQ,QAAQ,EAAEA,QAAQ;QAClByc,SAAS,EAAEA,SAAS;QACpBsD,WAAW,EAAEvE;MACf,CAAC,CAAC,EAAEna,KAAK,EAAE,IAAI,EAAE,MAAMnD,SAAS,CAAC;MACjC,OAAOoS,KAAK;IACd,CAAC;IACD0P,OAAOA,CAAA,EAAG;MACRC,sBAAsB,CAACzE,MAAM,CAAC;IAChC;EACF,CAAC;AACH;AACA,SAASvS,MAAMA,CAACjJ,QAAQ,EAAEwb,MAAM,EAAE0E,MAAM,EAAE;EACxChe,OAAO,CAACC,IAAI,CAAC,wEAAwE,CAAC;EACtF,MAAM7D,IAAI,GAAG4d,UAAU,CAACV,MAAM,CAAC;EAC/Bld,IAAI,CAACse,SAAS,CAACsD,MAAM,CAAC;EACtB,OAAO5hB,IAAI,CAAC2K,MAAM,CAACjJ,QAAQ,CAAC;AAC9B;AACA,SAAS8f,QAAQA,CAAC;EAChBxP,KAAK;EACLtQ,QAAQ;EACRyc,SAAS;EACTsD;AACF,CAAC,EAAE;EACDtY,yBAAyB,CAAC,MAAM;IAC9B,MAAMhI,KAAK,GAAG6Q,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;IAC9B;IACAD,KAAK,CAACiJ,GAAG,CAACjJ,KAAK,KAAK;MAClB4O,QAAQ,EAAE;QACR,GAAG5O,KAAK,CAAC4O,QAAQ;QACjByK,MAAM,EAAE;MACV;IACF,CAAC,CAAC,CAAC;IACH;IACA,IAAI2D,SAAS,EAAEA,SAAS,CAAChd,KAAK,CAAC;IAC/B;IACA;IACA,IAAI,CAAC6Q,KAAK,CAAC5Q,QAAQ,CAAC,CAAC,CAACkS,MAAM,CAACyF,SAAS,EAAE5X,KAAK,CAACmS,MAAM,CAACyM,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG5e,KAAK,CAACmS,MAAM,CAACyM,OAAO,CAAC0B,WAAW,CAAC;IACjH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaxjB,GAAG,CAACuZ,OAAO,CAACgK,QAAQ,EAAE;IACxCjc,KAAK,EAAEyM,KAAK;IACZtQ,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACA,SAASigB,sBAAsBA,CAACzE,MAAM,EAAEtI,QAAQ,EAAE;EAChD,MAAM5U,IAAI,GAAG+c,MAAM,CAAC3O,GAAG,CAAC8O,MAAM,CAAC;EAC/B,MAAMna,KAAK,GAAG/C,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC+C,KAAK;EAChD,IAAIA,KAAK,EAAE;IACT,MAAM5B,KAAK,GAAGnB,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACgS,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;IAC3D,IAAID,KAAK,EAAEA,KAAK,CAAC4O,QAAQ,CAACyK,MAAM,GAAG,KAAK;IACxCtW,UAAU,CAACqd,eAAe,CAAC,IAAI,EAAExe,KAAK,EAAE,IAAI,EAAE,MAAM;MAClD,IAAI5B,KAAK,EAAE;QACTyF,UAAU,CAAC,MAAM;UACf,IAAI;YACF,IAAIib,SAAS,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,UAAU;YAC5D7gB,KAAK,CAACmS,MAAM,CAAC2M,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG9e,KAAK,CAACmS,MAAM,CAAC2M,UAAU,CAAC,CAAC;YACpE,CAAC4B,SAAS,GAAG1gB,KAAK,CAAC2X,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACgJ,qBAAqB,GAAGD,SAAS,CAACI,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAAC1f,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG0f,qBAAqB,CAAC1f,OAAO,CAAC,CAAC;YAC7L,CAAC2f,UAAU,GAAG5gB,KAAK,CAAC2X,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiJ,UAAU,CAACG,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGH,UAAU,CAACG,gBAAgB,CAAC,CAAC;YACvH,IAAI,CAACF,UAAU,GAAG7gB,KAAK,CAAC2X,EAAE,KAAK,IAAI,IAAIkJ,UAAU,CAAChJ,EAAE,EAAE7X,KAAK,CAAC6X,EAAE,CAACiH,UAAU,CAAC,CAAC;YAC3E7d,OAAO,CAACjB,KAAK,CAACE,KAAK,CAAC;YACpB0b,MAAM,CAACjL,MAAM,CAACoL,MAAM,CAAC;YACrB,IAAItI,QAAQ,EAAEA,QAAQ,CAACsI,MAAM,CAAC;UAChC,CAAC,CAAC,OAAOiF,CAAC,EAAE;YACV;UAAA;QAEJ,CAAC,EAAE,GAAG,CAAC;MACT;IACF,CAAC,CAAC;EACJ;AACF;AACA,SAASC,YAAYA,CAAC1gB,QAAQ,EAAEgD,SAAS,EAAEvD,KAAK,EAAE;EAChD,OAAO,aAAalD,GAAG,CAACokB,MAAM,EAAE;IAC9B3gB,QAAQ,EAAEA,QAAQ;IAClBgD,SAAS,EAAEA,SAAS;IACpBvD,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AACA,SAASkhB,MAAMA,CAAC;EACdlhB,KAAK,GAAG,CAAC,CAAC;EACVO,QAAQ;EACRgD;AACF,CAAC,EAAE;EACD;AACF;AACA;AACA;AACA;EACE,MAAM;IACJ4O,MAAM;IACN3C,IAAI;IACJ,GAAG2R;EACL,CAAC,GAAGnhB,KAAK;EACT,MAAMqH,YAAY,GAAG4S,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAAC/H,SAAS,CAAC,GAAGjW,KAAK,CAACmlB,QAAQ,CAAC,MAAM,IAAIplB,KAAK,CAAC4hB,SAAS,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAChK,OAAO,CAAC,GAAG3X,KAAK,CAACmlB,QAAQ,CAAC,MAAM,IAAIplB,KAAK,CAAC0b,OAAO,CAAC,CAAC,CAAC;EAC3D,MAAM2J,MAAM,GAAG9Y,kBAAkB,CAAC,CAACoF,SAAS,EAAE2T,WAAW,KAAK;IAC5D,IAAI7I,QAAQ,GAAGha,SAAS;IACxB,IAAI6iB,WAAW,CAAC/R,MAAM,IAAIC,IAAI,EAAE;MAC9B,MAAMD,MAAM,GAAG+R,WAAW,CAAC/R,MAAM;MACjC;MACAkJ,QAAQ,GAAG9K,SAAS,CAAC8K,QAAQ,CAAC5B,kBAAkB,CAACtH,MAAM,EAAE,IAAIvT,KAAK,CAAC8X,OAAO,CAAC,CAAC,EAAEtE,IAAI,CAAC;MACnF;MACA,IAAID,MAAM,KAAK5B,SAAS,CAAC4B,MAAM,EAAED,YAAY,CAACC,MAAM,EAAEC,IAAI,CAAC;IAC7D;IACA,OAAO;MACL;MACA,GAAG7B,SAAS;MACZ,GAAG2T,WAAW;MACd;MACAphB,KAAK,EAAEqD,SAAS;MAChB2O,SAAS;MACT0B,OAAO;MACPyE,KAAK,EAAEzE,OAAO;MACd;MACAvM,YAAY;MACZ;MACA8K,MAAM,EAAE;QACN,GAAGxE,SAAS,CAACwE,MAAM;QACnB,GAAGmP,WAAW,CAACnP,MAAM;QACrB,GAAGA;MACL,CAAC;MACD3C,IAAI,EAAE;QACJ,GAAG7B,SAAS,CAAC6B,IAAI;QACjB,GAAGA;MACL,CAAC;MACDiJ,QAAQ,EAAE;QACR,GAAG9K,SAAS,CAAC8K,QAAQ;QACrB,GAAGA;MACL,CAAC;MACD;MACAE,SAAS,EAAExG,MAAM,IAAImP,WAAW,CAACrY,GAAG,CAACjJ,KAAK,KAAK;QAC7C,GAAGA,KAAK;QACRmS,MAAM,EAAE;UACN,GAAGnS,KAAK,CAACmS,MAAM;UACf,GAAGA;QACL;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;EACF,MAAMoP,cAAc,GAAGtlB,KAAK,CAAC2M,OAAO,CAAC,MAAM;IACzC;IACA,MAAMiI,KAAK,GAAGtU,oBAAoB,CAAC,CAAC0M,GAAG,EAAEgE,GAAG,MAAM;MAChD,GAAGkU,IAAI;MACPlY,GAAG;MACHgE;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMuU,QAAQ,GAAGC,IAAI,IAAI5Q,KAAK,CAAC6Q,QAAQ,CAAC1hB,KAAK,IAAIqhB,MAAM,CAAClf,OAAO,CAACsf,IAAI,EAAEzhB,KAAK,CAAC,CAAC;IAC7EwhB,QAAQ,CAACna,YAAY,CAACpH,QAAQ,CAAC,CAAC,CAAC;IACjCoH,YAAY,CAACiS,SAAS,CAACkI,QAAQ,CAAC;IAChC,OAAO3Q,KAAK;IACZ;EACF,CAAC,EAAE,CAACxJ,YAAY,EAAE9D,SAAS,CAAC,CAAC;EAC7B,OAAO,aAAazG,GAAG,CAACC,QAAQ,EAAE;IAChCwD,QAAQ,EAAEwC,UAAU,CAACke,YAAY,CAAE,aAAankB,GAAG,CAACuZ,OAAO,CAACgK,QAAQ,EAAE;MACpEjc,KAAK,EAAEmd,cAAc;MACrBhhB,QAAQ,EAAEA;IACZ,CAAC,CAAC,EAAEghB,cAAc,EAAE,IAAI;EAC1B,CAAC,CAAC;AACJ;AACAxe,UAAU,CAAC4e,kBAAkB,CAAC;EAC5BC,UAAU,EAAEzU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,CAAC,GAAG,CAAC;EACzDwU,mBAAmB,EAAE,oBAAoB;EACzCC,OAAO,EAAE7lB,KAAK,CAAC6lB;AACjB,CAAC,CAAC;AAEF,SAASC,UAAUA,CAACtO,QAAQ,EAAEuO,IAAI,EAAE;EAClC,MAAMC,GAAG,GAAG;IACVxO;EACF,CAAC;EACDuO,IAAI,CAACrhB,GAAG,CAACshB,GAAG,CAAC;EACb,OAAO,MAAM,KAAKD,IAAI,CAACrR,MAAM,CAACsR,GAAG,CAAC;AACpC;AACA,MAAMC,aAAa,GAAG,IAAInQ,GAAG,CAAC,CAAC;AAC/B,MAAMoQ,kBAAkB,GAAG,IAAIpQ,GAAG,CAAC,CAAC;AACpC,MAAMqQ,iBAAiB,GAAG,IAAIrQ,GAAG,CAAC,CAAC;;AAEnC;AACA;AACA;AACA;AACA,MAAMsQ,SAAS,GAAG5O,QAAQ,IAAIsO,UAAU,CAACtO,QAAQ,EAAEyO,aAAa,CAAC;;AAEjE;AACA;AACA;AACA;AACA,MAAMI,cAAc,GAAG7O,QAAQ,IAAIsO,UAAU,CAACtO,QAAQ,EAAE0O,kBAAkB,CAAC;;AAE3E;AACA;AACA;AACA;AACA,MAAMI,OAAO,GAAG9O,QAAQ,IAAIsO,UAAU,CAACtO,QAAQ,EAAE2O,iBAAiB,CAAC;AACnE,SAASI,GAAGA,CAACC,OAAO,EAAE3K,SAAS,EAAE;EAC/B,IAAI,CAAC2K,OAAO,CAACjT,IAAI,EAAE;EACnB,KAAK,MAAM;IACTiE;EACF,CAAC,IAAIgP,OAAO,CAACpP,MAAM,CAAC,CAAC,EAAE;IACrBI,QAAQ,CAACqE,SAAS,CAAC;EACrB;AACF;AACA,SAAS4K,kBAAkBA,CAACzkB,IAAI,EAAE6Z,SAAS,EAAE;EAC3C,QAAQ7Z,IAAI;IACV,KAAK,QAAQ;MACX,OAAOukB,GAAG,CAACN,aAAa,EAAEpK,SAAS,CAAC;IACtC,KAAK,OAAO;MACV,OAAO0K,GAAG,CAACL,kBAAkB,EAAErK,SAAS,CAAC;IAC3C,KAAK,MAAM;MACT,OAAO0K,GAAG,CAACJ,iBAAiB,EAAEtK,SAAS,CAAC;EAC5C;AACF;AACA,IAAIqB,WAAW;AACf,IAAIwJ,YAAY;AAChB,SAASC,MAAMA,CAAC9K,SAAS,EAAE9X,KAAK,EAAEwe,KAAK,EAAE;EACvC;EACA,IAAIhL,KAAK,GAAGxT,KAAK,CAACmY,KAAK,CAAC0K,QAAQ,CAAC,CAAC;;EAElC;EACA,IAAI7iB,KAAK,CAACsY,SAAS,KAAK,OAAO,IAAI,OAAOR,SAAS,KAAK,QAAQ,EAAE;IAChEtE,KAAK,GAAGsE,SAAS,GAAG9X,KAAK,CAACmY,KAAK,CAACc,WAAW;IAC3CjZ,KAAK,CAACmY,KAAK,CAAC2K,OAAO,GAAG9iB,KAAK,CAACmY,KAAK,CAACc,WAAW;IAC7CjZ,KAAK,CAACmY,KAAK,CAACc,WAAW,GAAGnB,SAAS;EACrC;;EAEA;EACAqB,WAAW,GAAGnZ,KAAK,CAAC4O,QAAQ,CAACuK,WAAW;EACxC,KAAK,IAAI1b,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0b,WAAW,CAAC1X,MAAM,EAAEhE,CAAC,EAAE,EAAE;IAC3CklB,YAAY,GAAGxJ,WAAW,CAAC1b,CAAC,CAAC;IAC7BklB,YAAY,CAAC3gB,GAAG,CAACG,OAAO,CAACwgB,YAAY,CAAC9R,KAAK,CAAC5Q,QAAQ,CAAC,CAAC,EAAEuT,KAAK,EAAEgL,KAAK,CAAC;EACvE;;EAEA;EACA,IAAI,CAACxe,KAAK,CAAC4O,QAAQ,CAACmE,QAAQ,IAAI/S,KAAK,CAAC2X,EAAE,CAACnO,MAAM,EAAExJ,KAAK,CAAC2X,EAAE,CAACnO,MAAM,CAACxJ,KAAK,CAACE,KAAK,EAAEF,KAAK,CAACuP,MAAM,CAAC;;EAE3F;EACAvP,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,GAAGtF,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEhK,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,GAAG,CAAC,CAAC;EAC9D,OAAOpP,KAAK,CAACsY,SAAS,KAAK,QAAQ,GAAG,CAAC,GAAGtY,KAAK,CAAC4O,QAAQ,CAACQ,MAAM;AACjE;AACA,IAAI2T,OAAO,GAAG,KAAK;AACnB,IAAIC,kBAAkB,GAAG,KAAK;AAC9B,IAAIC,MAAM;AACV,IAAIzE,KAAK;AACT,IAAIxe,KAAK;AACT,SAASkjB,IAAIA,CAACpL,SAAS,EAAE;EACvB0G,KAAK,GAAGhJ,qBAAqB,CAAC0N,IAAI,CAAC;EACnCH,OAAO,GAAG,IAAI;EACdE,MAAM,GAAG,CAAC;;EAEV;EACAP,kBAAkB,CAAC,QAAQ,EAAE5K,SAAS,CAAC;;EAEvC;EACAkL,kBAAkB,GAAG,IAAI;EACzB,KAAK,MAAMnkB,IAAI,IAAI+c,MAAM,CAACvI,MAAM,CAAC,CAAC,EAAE;IAClC,IAAI8P,YAAY;IAChBnjB,KAAK,GAAGnB,IAAI,CAACgS,KAAK,CAAC5Q,QAAQ,CAAC,CAAC;;IAE7B;IACA,IAAID,KAAK,CAAC4O,QAAQ,CAACyK,MAAM,KAAKrZ,KAAK,CAACsY,SAAS,KAAK,QAAQ,IAAItY,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC+T,YAAY,GAAGnjB,KAAK,CAAC2X,EAAE,CAACE,EAAE,KAAK,IAAI,IAAIsL,YAAY,CAACzE,YAAY,CAAC,EAAE;MAChKuE,MAAM,IAAIL,MAAM,CAAC9K,SAAS,EAAE9X,KAAK,CAAC;IACpC;EACF;EACAgjB,kBAAkB,GAAG,IAAI;;EAEzB;EACAN,kBAAkB,CAAC,OAAO,EAAE5K,SAAS,CAAC;;EAEtC;EACA,IAAImL,MAAM,KAAK,CAAC,EAAE;IAChB;IACAP,kBAAkB,CAAC,MAAM,EAAE5K,SAAS,CAAC;;IAErC;IACAiL,OAAO,GAAG,KAAK;IACf,OAAOK,oBAAoB,CAAC5E,KAAK,CAAC;EACpC;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASnP,UAAUA,CAACrP,KAAK,EAAEoP,MAAM,GAAG,CAAC,EAAE;EACrC,IAAIiU,aAAa;EACjB,IAAI,CAACrjB,KAAK,EAAE,OAAO4b,MAAM,CAAC1K,OAAO,CAACrS,IAAI,IAAIwQ,UAAU,CAACxQ,IAAI,CAACgS,KAAK,CAAC5Q,QAAQ,CAAC,CAAC,EAAEmP,MAAM,CAAC,CAAC;EACpF,IAAI,CAACiU,aAAa,GAAGrjB,KAAK,CAAC2X,EAAE,CAACE,EAAE,KAAK,IAAI,IAAIwL,aAAa,CAAC3E,YAAY,IAAI,CAAC1e,KAAK,CAAC4O,QAAQ,CAACyK,MAAM,IAAIrZ,KAAK,CAACsY,SAAS,KAAK,OAAO,EAAE;EAClI,IAAIlJ,MAAM,GAAG,CAAC,EAAE;IACd;IACA;IACApP,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,GAAGtF,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE/J,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,GAAGA,MAAM,CAAC;EACtE,CAAC,MAAM;IACL,IAAI4T,kBAAkB,EAAE;MACtB;MACAhjB,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,GAAG,CAAC;IAC3B,CAAC,MAAM;MACL;MACApP,KAAK,CAAC4O,QAAQ,CAACQ,MAAM,GAAG,CAAC;IAC3B;EACF;;EAEA;EACA,IAAI,CAAC2T,OAAO,EAAE;IACZA,OAAO,GAAG,IAAI;IACdvN,qBAAqB,CAAC0N,IAAI,CAAC;EAC7B;AACF;;AAEA;AACA;AACA;AACA;AACA,SAAS1M,OAAOA,CAACsB,SAAS,EAAEC,gBAAgB,GAAG,IAAI,EAAE/X,KAAK,EAAEwe,KAAK,EAAE;EACjE,IAAIzG,gBAAgB,EAAE2K,kBAAkB,CAAC,QAAQ,EAAE5K,SAAS,CAAC;EAC7D,IAAI,CAAC9X,KAAK,EAAE,KAAK,MAAMnB,IAAI,IAAI+c,MAAM,CAACvI,MAAM,CAAC,CAAC,EAAEuP,MAAM,CAAC9K,SAAS,EAAEjZ,IAAI,CAACgS,KAAK,CAAC5Q,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK2iB,MAAM,CAAC9K,SAAS,EAAE9X,KAAK,EAAEwe,KAAK,CAAC;EAC7H,IAAIzG,gBAAgB,EAAE2K,kBAAkB,CAAC,OAAO,EAAE5K,SAAS,CAAC;AAC9D;AAEA,SAASqC,QAAQ,IAAImJ,CAAC,EAAEta,KAAK,IAAIua,CAAC,EAAEjJ,QAAQ,IAAIkJ,CAAC,EAAEhJ,QAAQ,IAAIiJ,CAAC,EAAEta,aAAa,IAAIua,CAAC,EAAEjI,SAAS,IAAIkI,CAAC,EAAE/H,MAAM,IAAIgI,CAAC,EAAErb,kBAAkB,IAAI6B,CAAC,EAAEpC,yBAAyB,IAAI4C,CAAC,EAAEuG,YAAY,IAAI0S,CAAC,EAAEpH,UAAU,IAAIqH,CAAC,EAAElmB,MAAM,IAAIojB,CAAC,EAAER,sBAAsB,IAAIuD,CAAC,EAAErB,kBAAkB,IAAIsB,CAAC,EAAE3B,SAAS,IAAIjL,CAAC,EAAEvP,KAAK,IAAIpK,CAAC,EAAE6kB,cAAc,IAAI2B,CAAC,EAAE1B,OAAO,IAAI2B,CAAC,EAAE7U,UAAU,IAAI8U,CAAC,EAAE3N,OAAO,IAAI4N,CAAC,EAAE5a,MAAM,IAAI6a,CAAC,EAAEpD,YAAY,IAAIlQ,CAAC,EAAEsF,OAAO,IAAI/K,CAAC,EAAEjL,UAAU,IAAIikB,CAAC,EAAEvhB,UAAU,IAAIwhB,CAAC,EAAEta,YAAY,IAAIsP,CAAC,EAAEpc,UAAU,IAAIqnB,CAAC,EAAE9b,SAAS,IAAI+b,CAAC,EAAExjB,OAAO,IAAIyjB,CAAC,EAAEhd,GAAG,IAAI4P,CAAC,EAAEtM,UAAU,IAAI+I,CAAC,EAAEgG,iBAAiB,IAAI/F,CAAC,EAAEiG,QAAQ,IAAIkE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}