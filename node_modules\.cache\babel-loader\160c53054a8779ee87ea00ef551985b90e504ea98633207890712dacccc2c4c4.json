{"ast": null, "code": "'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The created error.\n */\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  response && (this.response = response);\n}\nutils.inherits(AxiosError, Error, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code,\n      status: this.response && this.response.status ? this.response.status : null\n    };\n  }\n});\nvar prototype = AxiosError.prototype;\nvar descriptors = {};\n['ERR_BAD_OPTION_VALUE', 'ERR_BAD_OPTION', 'ECONNABORTED', 'ETIMEDOUT', 'ERR_NETWORK', 'ERR_FR_TOO_MANY_REDIRECTS', 'ERR_DEPRECATED', 'ERR_BAD_RESPONSE', 'ERR_BAD_REQUEST', 'ERR_CANCELED'\n// eslint-disable-next-line func-names\n].forEach(function (code) {\n  descriptors[code] = {\n    value: code\n  };\n});\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {\n  value: true\n});\n\n// eslint-disable-next-line func-names\nAxiosError.from = function (error, code, config, request, response, customProps) {\n  var axiosError = Object.create(prototype);\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  });\n  AxiosError.call(axiosError, error.message, code, config, request, response);\n  axiosError.name = error.name;\n  customProps && Object.assign(axiosError, customProps);\n  return axiosError;\n};\nmodule.exports = AxiosError;", "map": {"version": 3, "names": ["utils", "require", "AxiosError", "message", "code", "config", "request", "response", "Error", "call", "name", "inherits", "toJSON", "description", "number", "fileName", "lineNumber", "columnNumber", "stack", "status", "prototype", "descriptors", "for<PERSON>ach", "value", "Object", "defineProperties", "defineProperty", "from", "error", "customProps", "axiosError", "create", "toFlatObject", "filter", "obj", "assign", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/axios/lib/core/AxiosError.js"], "sourcesContent": ["'use strict';\n\nvar utils = require('../utils');\n\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n * @returns {Error} The created error.\n */\nfunction AxiosError(message, code, config, request, response) {\n  Error.call(this);\n  this.message = message;\n  this.name = 'AxiosError';\n  code && (this.code = code);\n  config && (this.config = config);\n  request && (this.request = request);\n  response && (this.response = response);\n}\n\nutils.inherits(AxiosError, Error, {\n  toJSON: function toJSON() {\n    return {\n      // Standard\n      message: this.message,\n      name: this.name,\n      // Microsoft\n      description: this.description,\n      number: this.number,\n      // Mozilla\n      fileName: this.fileName,\n      lineNumber: this.lineNumber,\n      columnNumber: this.columnNumber,\n      stack: this.stack,\n      // Axios\n      config: this.config,\n      code: this.code,\n      status: this.response && this.response.status ? this.response.status : null\n    };\n  }\n});\n\nvar prototype = AxiosError.prototype;\nvar descriptors = {};\n\n[\n  'ERR_BAD_OPTION_VALUE',\n  'ERR_BAD_OPTION',\n  'ECONNABORTED',\n  'ETIMEDOUT',\n  'ERR_NETWORK',\n  'ERR_FR_TOO_MANY_REDIRECTS',\n  'ERR_DEPRECATED',\n  'ERR_BAD_RESPONSE',\n  'ERR_BAD_REQUEST',\n  'ERR_CANCELED'\n// eslint-disable-next-line func-names\n].forEach(function(code) {\n  descriptors[code] = {value: code};\n});\n\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype, 'isAxiosError', {value: true});\n\n// eslint-disable-next-line func-names\nAxiosError.from = function(error, code, config, request, response, customProps) {\n  var axiosError = Object.create(prototype);\n\n  utils.toFlatObject(error, axiosError, function filter(obj) {\n    return obj !== Error.prototype;\n  });\n\n  AxiosError.call(axiosError, error.message, code, config, request, response);\n\n  axiosError.name = error.name;\n\n  customProps && Object.assign(axiosError, customProps);\n\n  return axiosError;\n};\n\nmodule.exports = AxiosError;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EAC5DC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;EAChB,IAAI,CAACN,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACO,IAAI,GAAG,YAAY;EACxBN,IAAI,KAAK,IAAI,CAACA,IAAI,GAAGA,IAAI,CAAC;EAC1BC,MAAM,KAAK,IAAI,CAACA,MAAM,GAAGA,MAAM,CAAC;EAChCC,OAAO,KAAK,IAAI,CAACA,OAAO,GAAGA,OAAO,CAAC;EACnCC,QAAQ,KAAK,IAAI,CAACA,QAAQ,GAAGA,QAAQ,CAAC;AACxC;AAEAP,KAAK,CAACW,QAAQ,CAACT,UAAU,EAAEM,KAAK,EAAE;EAChCI,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB,OAAO;MACL;MACAT,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBO,IAAI,EAAE,IAAI,CAACA,IAAI;MACf;MACAG,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnB;MACAC,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,KAAK,EAAE,IAAI,CAACA,KAAK;MACjB;MACAb,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBD,IAAI,EAAE,IAAI,CAACA,IAAI;MACfe,MAAM,EAAE,IAAI,CAACZ,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACY,MAAM,GAAG,IAAI,CAACZ,QAAQ,CAACY,MAAM,GAAG;IACzE,CAAC;EACH;AACF,CAAC,CAAC;AAEF,IAAIC,SAAS,GAAGlB,UAAU,CAACkB,SAAS;AACpC,IAAIC,WAAW,GAAG,CAAC,CAAC;AAEpB,CACE,sBAAsB,EACtB,gBAAgB,EAChB,cAAc,EACd,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,gBAAgB,EAChB,kBAAkB,EAClB,iBAAiB,EACjB;AACF;AAAA,CACC,CAACC,OAAO,CAAC,UAASlB,IAAI,EAAE;EACvBiB,WAAW,CAACjB,IAAI,CAAC,GAAG;IAACmB,KAAK,EAAEnB;EAAI,CAAC;AACnC,CAAC,CAAC;AAEFoB,MAAM,CAACC,gBAAgB,CAACvB,UAAU,EAAEmB,WAAW,CAAC;AAChDG,MAAM,CAACE,cAAc,CAACN,SAAS,EAAE,cAAc,EAAE;EAACG,KAAK,EAAE;AAAI,CAAC,CAAC;;AAE/D;AACArB,UAAU,CAACyB,IAAI,GAAG,UAASC,KAAK,EAAExB,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,EAAEsB,WAAW,EAAE;EAC9E,IAAIC,UAAU,GAAGN,MAAM,CAACO,MAAM,CAACX,SAAS,CAAC;EAEzCpB,KAAK,CAACgC,YAAY,CAACJ,KAAK,EAAEE,UAAU,EAAE,SAASG,MAAMA,CAACC,GAAG,EAAE;IACzD,OAAOA,GAAG,KAAK1B,KAAK,CAACY,SAAS;EAChC,CAAC,CAAC;EAEFlB,UAAU,CAACO,IAAI,CAACqB,UAAU,EAAEF,KAAK,CAACzB,OAAO,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAE3EuB,UAAU,CAACpB,IAAI,GAAGkB,KAAK,CAAClB,IAAI;EAE5BmB,WAAW,IAAIL,MAAM,CAACW,MAAM,CAACL,UAAU,EAAED,WAAW,CAAC;EAErD,OAAOC,UAAU;AACnB,CAAC;AAEDM,MAAM,CAACC,OAAO,GAAGnC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}