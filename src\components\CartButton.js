import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../context/CartContext';

const CartButton = () => {
  const { getCartItemCount } = useCart();
  const itemCount = getCartItemCount();

  return (
    <Link to="/cart" className="floating-cart-button dark-glass-card">
      <div className="card-glow"></div>
      <div className="card-shine"></div>
      
      <div className="cart-icon">
        🛒
      </div>
      
      {itemCount > 0 && (
        <div className="cart-count">
          {itemCount > 99 ? '99+' : itemCount}
        </div>
      )}
      
      <div className="cart-tooltip">
        {itemCount === 0 ? 'කාර්ට් එක හිස්ය' : `${itemCount} කාඩ්පත්`}
      </div>
    </Link>
  );
};

export default CartButton;
