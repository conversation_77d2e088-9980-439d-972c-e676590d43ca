* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  /* Disable text selection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /* Disable right-click context menu */
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: 'Noto Sans Sinhala', sans-serif;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  color: #f4d03f;
  min-height: 100vh;
  overflow-x: hidden;
  /* Additional protection */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Disable image dragging and selection */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Disable selection on all text elements */
p, h1, h2, h3, h4, h5, h6, span, div, a {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.App {
  min-height: 100vh;
  position: relative;
}

/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.landing-header {
  text-align: center;
  margin-bottom: 4rem;
  z-index: 2;
  position: relative;
}

.main-title {
  font-size: 3.8rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow:
    0 0 20px rgba(244, 208, 63, 0.6),
    0 0 40px rgba(244, 208, 63, 0.4),
    0 0 60px rgba(244, 208, 63, 0.2);
  margin-bottom: 1.5rem;
  animation: divineGlow 3s ease-in-out infinite alternate;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.8rem;
  color: #e8f4fd;
  font-weight: 400;
  margin-bottom: 2rem;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  line-height: 1.4;
}

.description {
  font-size: 1.2rem;
  color: #d5dbdb;
  max-width: 700px;
  line-height: 1.8;
  text-align: center;
  margin: 0 auto 2rem auto;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.divine-blessing {
  margin-top: 2rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, rgba(244, 208, 63, 0.1) 0%, rgba(244, 208, 63, 0.05) 100%);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 25px;
  display: inline-block;
  backdrop-filter: blur(10px);
}

.blessing-text {
  color: #f4d03f;
  font-size: 1.1rem;
  font-weight: 500;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);
}

/* Premium Zodiac Grid */
.premium-zodiac-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2.5rem;
  max-width: 1400px;
  width: 100%;
  z-index: 2;
  padding: 0 1rem;
}

/* Dark Glass Card Design - Consistent with other components */
.dark-glass-card {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 20px;
  padding: 2.5rem;
  text-align: left;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  backdrop-filter: blur(15px);
  position: relative;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(244, 208, 63, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.dark-glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.1), transparent);
  transition: left 0.5s;
  pointer-events: none;
}

.dark-glass-card:hover::before {
  left: 100%;
}

.dark-glass-card:hover {
  transform: translateY(-10px);
  border-color: rgba(244, 208, 63, 0.5);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 10px 30px rgba(244, 208, 63, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(244, 208, 63, 0.08) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.dark-glass-card:hover .card-glow {
  opacity: 1;
}

.card-shine {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.6), transparent);
  opacity: 0.7;
}

/* Premium Card Content Styles */
.zodiac-header-section {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(244, 208, 63, 0.2);
}

.zodiac-icon-large {
  font-size: 4rem;
  margin-right: 1.5rem;
  color: #f4d03f;
  text-shadow:
    0 0 20px rgba(244, 208, 63, 0.6),
    0 0 40px rgba(244, 208, 63, 0.4);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.dark-glass-card:hover .zodiac-icon-large {
  text-shadow:
    0 0 30px rgba(244, 208, 63, 0.8),
    0 0 60px rgba(244, 208, 63, 0.6);
  transform: scale(1.05);
}

.zodiac-names-section {
  flex: 1;
}

.sinhala-name-large {
  font-size: 2rem;
  font-weight: 700;
  color: #f4d03f;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 10px rgba(244, 208, 63, 0.3);
  line-height: 1.2;
}

.english-name-small {
  font-size: 1.1rem;
  color: #d5dbdb;
  font-weight: 400;
  opacity: 0.9;
}

.zodiac-details {
  margin-bottom: 1.5rem;
  space-y: 0.8rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  padding: 0.6rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 0.95rem;
  color: #aeb6bf;
  font-weight: 500;
  opacity: 0.9;
}

.detail-value {
  font-size: 0.95rem;
  color: #f4d03f;
  font-weight: 600;
  text-align: right;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.zodiac-description {
  font-size: 1rem;
  color: #d5dbdb;
  font-style: italic;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  border-left: 3px solid rgba(244, 208, 63, 0.4);
  line-height: 1.4;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.card-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid rgba(244, 208, 63, 0.2);
  margin-top: auto;
  background: rgba(0, 0, 0, 0.05);
  margin: 1rem -2.5rem -2.5rem -2.5rem;
  padding: 1rem 2.5rem;
  backdrop-filter: blur(5px);
}

.action-text {
  font-size: 1rem;
  color: #f4d03f;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.action-arrow {
  font-size: 1.2rem;
  color: #f4d03f;
  transition: transform 0.3s ease;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.dark-glass-card:hover .action-arrow {
  transform: translateX(5px);
}

.dark-glass-card:hover .action-text {
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);
}

/* Zodiac Page Styles */
.zodiac-page {
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.back-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  background: rgba(244, 208, 63, 0.1);
  border: 1px solid #f4d03f;
  color: #f4d03f;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  text-decoration: none;
  font-family: 'Noto Sans Sinhala', sans-serif;
  font-weight: 500;
  transition: all 0.3s ease;
  z-index: 10;
}

.back-button:hover {
  background: rgba(244, 208, 63, 0.2);
  transform: translateX(-5px);
}

.zodiac-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  padding-top: 4rem;
  z-index: 2;
  position: relative;
}

.zodiac-title {
  font-size: 4rem;
  font-weight: 700;
  color: #f4d03f;
  margin-bottom: 1rem;
  text-shadow: 0 0 30px rgba(244, 208, 63, 0.6);
}

.zodiac-subtitle {
  font-size: 1.5rem;
  color: #d5dbdb;
  margin-bottom: 3rem;
}

.horoscope-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 20px;
  padding: 3rem;
  margin: 2rem 0;
  backdrop-filter: blur(10px);
  max-width: 1200px;
  width: 100%;
}

.horoscope-title {
  font-size: 2rem;
  color: #f4d03f;
  margin-bottom: 1.5rem;
}

.horoscope-content {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #d5dbdb;
  text-align: left;
}

.structured-horoscope-display {
  max-width: 1200px !important;
  width: 100%;
  text-align: left;
}

.horoscope-category-card {
  text-align: left !important;
}

.horoscope-category-card p {
  text-align: left !important;
}

/* Divine Background Image Styles */
.divine-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
  overflow: hidden;
}

.god-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.08;
  filter: blur(0.5px) sepia(20%) saturate(150%) hue-rotate(30deg);
  transition: all 0.3s ease;
  object-fit: cover;
  object-position: center;
}

/* Enhance the divine presence on hover */
.zodiac-page:hover .god-image {
  opacity: 0.12;
  filter: blur(0.3px) sepia(25%) saturate(160%) hue-rotate(30deg);
  transform: scale(1.02);
}

/* Additional divine glow effect */
.divine-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(244, 208, 63, 0.03) 0%, transparent 70%);
  z-index: 1;
  animation: divineGlow 4s ease-in-out infinite alternate;
}

@keyframes divineGlow {
  0% {
    opacity: 0.3;
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.loading {
  font-size: 1.1rem;
  color: #aeb6bf;
  font-style: italic;
}

.error {
  color: #e74c3c;
  font-size: 1.1rem;
}

/* Animations */
@keyframes divineGlow {
  0% {
    text-shadow:
      0 0 20px rgba(244, 208, 63, 0.6),
      0 0 40px rgba(244, 208, 63, 0.4),
      0 0 60px rgba(244, 208, 63, 0.2);
  }
  50% {
    text-shadow:
      0 0 30px rgba(244, 208, 63, 0.8),
      0 0 60px rgba(244, 208, 63, 0.6),
      0 0 90px rgba(244, 208, 63, 0.4);
  }
  100% {
    text-shadow:
      0 0 20px rgba(244, 208, 63, 0.6),
      0 0 40px rgba(244, 208, 63, 0.4),
      0 0 60px rgba(244, 208, 63, 0.2);
  }
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px rgba(244, 208, 63, 0.5);
  }
  to {
    text-shadow: 0 0 30px rgba(244, 208, 63, 0.8), 0 0 40px rgba(244, 208, 63, 0.6);
  }
}

@keyframes premiumFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-5px) rotate(0.5deg);
  }
  50% {
    transform: translateY(-10px) rotate(0deg);
  }
  75% {
    transform: translateY(-5px) rotate(-0.5deg);
  }
}

.premium-zodiac-card {
  animation: premiumFloat 6s ease-in-out infinite;
}

.premium-zodiac-card:nth-child(even) {
  animation-delay: -3s;
}

.premium-zodiac-card:nth-child(3n) {
  animation-delay: -1.5s;
}

.premium-zodiac-card:nth-child(4n) {
  animation-delay: -4.5s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.floating {
  animation: float 3s ease-in-out infinite;
}

/* Particles Background */
.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

/* Smoke Animation */
.smoke-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.smoke {
  position: absolute;
  bottom: -50px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(244, 208, 63, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  animation: smokeRise 8s linear infinite;
}

@keyframes smokeRise {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-100vh) scale(2);
    opacity: 0;
  }
}

/* Enhanced Mobile Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .landing-page {
    padding: 1.5rem;
  }
  
  .zodiac-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
  }
  
  .main-title {
    font-size: 3rem;
  }
  
  .zodiac-title {
    font-size: 3.5rem;
  }
}

/* Mobile landscape and small tablets */
@media (max-width: 768px) {
  /* Adjust divine background for tablets */
  .god-image {
    opacity: 0.15;
  }
  
  .main-title {
    font-size: 2.8rem;
    margin-bottom: 1rem;
    letter-spacing: 1px;
  }

  .subtitle {
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
    padding: 0 1rem;
  }

  .description {
    font-size: 1rem;
    padding: 0 1.5rem;
    max-width: 90%;
  }

  .divine-blessing {
    margin: 1.5rem 1rem 0 1rem;
    padding: 0.8rem 1.5rem;
  }

  .blessing-text {
    font-size: 1rem;
  }

  .premium-zodiac-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 1rem;
  }

  .dark-glass-card {
    padding: 2rem 1.5rem;
  }

  .card-action {
    margin: 1rem -1.5rem -2rem -1.5rem;
    padding: 1rem 1.5rem;
  }

  .zodiac-header-section {
    flex-direction: column;
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .zodiac-icon-large {
    font-size: 3.5rem;
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .sinhala-name-large {
    font-size: 1.8rem;
  }

  .english-name-small {
    font-size: 1rem;
  }

  .detail-row {
    flex-direction: column;
    text-align: center;
    gap: 0.3rem;
  }

  .detail-value {
    text-align: center;
  }

  .zodiac-description {
    font-size: 0.95rem;
    text-align: center;
  }
  
  .zodiac-title {
    font-size: 2.5rem;
    margin-bottom: 0.8rem;
  }
  
  .zodiac-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
  }
  
  .horoscope-section {
    padding: 2rem 1.5rem;
    margin: 1.5rem 0;
  }
  
  .horoscope-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
  }
  
  .horoscope-content {
    font-size: 1.1rem;
    line-height: 1.7;
  }
  
  .back-button {
    top: 1rem;
    left: 1rem;
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
  
  .zodiac-content {
    padding-top: 3.5rem;
  }
}

/* Mobile portrait */
@media (max-width: 480px) {
  .landing-page {
    padding: 1rem 0.5rem;
  }
  
  .zodiac-page {
    padding: 1rem 0.5rem;
  }
  
  /* Adjust divine background for mobile */
  .god-image {
    opacity: 0.12;
  }
  
  .main-title {
    font-size: 2.2rem;
    line-height: 1.2;
  }

  .subtitle {
    font-size: 1.2rem;
    padding: 0 0.5rem;
  }

  .description {
    font-size: 0.95rem;
    padding: 0 0.5rem;
  }

  .divine-blessing {
    margin: 1rem 0.5rem 0 0.5rem;
    padding: 0.6rem 1rem;
  }

  .blessing-text {
    font-size: 0.9rem;
  }

  .premium-zodiac-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
  }

  .dark-glass-card {
    padding: 1.5rem 1rem;
  }

  .card-action {
    margin: 1rem -1rem -1.5rem -1rem;
    padding: 1rem;
  }

  .zodiac-icon-large {
    font-size: 3rem;
    margin-bottom: 0.8rem;
  }

  .sinhala-name-large {
    font-size: 1.5rem;
  }

  .english-name-small {
    font-size: 0.9rem;
  }

  .zodiac-details {
    margin-bottom: 1rem;
  }

  .detail-row {
    margin-bottom: 0.6rem;
    padding: 0.3rem 0;
  }

  .detail-label, .detail-value {
    font-size: 0.85rem;
  }

  .zodiac-description {
    font-size: 0.9rem;
    padding: 0.8rem;
    margin-bottom: 1rem;
  }

  .card-action {
    padding-top: 0.8rem;
  }

  .action-text {
    font-size: 0.9rem;
  }
  
  .zodiac-title {
    font-size: 2rem;
    line-height: 1.2;
  }
  
  .zodiac-subtitle {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
  }
  
  .horoscope-section {
    padding: 1.5rem 1rem;
    margin: 1rem 0;
  }
  
  .horoscope-title {
    font-size: 1.5rem;
  }
  
  .horoscope-content {
    font-size: 1rem;
    line-height: 1.6;
  }
  
  .back-button {
    top: 0.8rem;
    left: 0.8rem;
    padding: 0.5rem 0.8rem;
    font-size: 0.85rem;
  }
  
  .zodiac-content {
    padding-top: 3rem;
  }
  
  .controls {
    margin-top: 1.5rem !important;
  }
  
  .sound-toggle {
    padding: 0.6rem 1rem !important;
    font-size: 0.9rem !important;
  }
  
  .spiritual-message {
    margin-top: 2rem !important;
    padding: 1.5rem !important;
  }
  
  .spiritual-message p {
    font-size: 1rem !important;
  }
}

/* Very small screens */
@media (max-width: 360px) {
  .main-title {
    font-size: 1.8rem;
  }
  
  .zodiac-grid {
    gap: 0.6rem;
  }
  
  .zodiac-card {
    padding: 1rem 0.6rem;
  }
  
  .zodiac-icon {
    font-size: 2rem;
  }
  
  .zodiac-name {
    font-size: 0.9rem;
  }
  
  .zodiac-english {
    font-size: 0.8rem;
  }
  
  .zodiac-title {
    font-size: 1.8rem;
  }
  
  .horoscope-section {
    padding: 1.2rem 0.8rem;
  }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
  .landing-page {
    padding: 1rem;
  }
  
  .main-title {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }
  
  .subtitle {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  .description {
    font-size: 0.9rem;
  }
  
  .zodiac-grid {
    gap: 0.8rem;
  }
  
  .zodiac-card {
    padding: 1rem;
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  .zodiac-card {
    transition: transform 0.2s ease;
  }
  
  .zodiac-card:active {
    transform: scale(0.98);
  }
  
  .back-button:active {
    transform: scale(0.95);
  }
  
  .sound-toggle:active {
    transform: scale(0.95);
  }
}

/* Kubera Guide Page Styles */
.kubera-guide-page {
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

.zodiac-navigation {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  z-index: 2;
  position: relative;
}

.zodiac-nav-btn {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 15px;
  color: #f4d03f;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.zodiac-nav-btn:hover {
  transform: translateY(-3px);
  border-color: rgba(244, 208, 63, 0.5);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.zodiac-nav-btn .nav-icon {
  font-size: 1.3rem;
}

.zodiac-nav-btn .nav-arrow {
  transition: transform 0.3s ease;
}

.zodiac-nav-btn:hover .nav-arrow {
  transform: translateX(5px);
}

.back-to-guide-btn {
  padding: 0.8rem 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 10px;
  color: #f4d03f;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 2rem;
}

.back-to-guide-btn:hover {
  transform: translateY(-2px);
  border-color: rgba(244, 208, 63, 0.5);
}

.kubera-content-section {
  margin-bottom: 3rem;
  display: flex;
  justify-content: center;
}

.kubera-content-card {
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  animation: fadeInUp 0.8s ease-out;
}

.content-header {
  margin-bottom: 2rem;
  text-align: center;
}

.content-title {
  font-size: 2rem;
  color: #f4d03f;
  font-weight: 600;
  text-shadow: 0 0 20px rgba(244, 208, 63, 0.4);
  margin-bottom: 1rem;
}

.content-body {
  line-height: 1.8;
  font-size: 1.1rem;
  color: #e8f4fd;
}

.content-body p {
  margin-bottom: 1.5rem;
  text-align: justify;
}

/* Mantra Card Specific Styles */
.mantra-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(244, 208, 63, 0.02));
}

.mantra-section {
  text-align: center;
}

.mantra-subtitle {
  font-size: 1.3rem;
  color: #f4d03f;
  margin: 2rem 0 1rem 0;
  font-weight: 500;
}

.sanskrit-mantra {
  font-size: 1.4rem;
  color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  margin: 1rem 0;
  border: 1px solid rgba(255, 215, 0, 0.2);
  font-family: 'Noto Sans Devanagari', serif;
  line-height: 1.6;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.sinhala-pronunciation {
  font-size: 1.3rem;
  color: #e8f4fd;
  background: rgba(232, 244, 253, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  margin: 1rem 0;
  border: 1px solid rgba(232, 244, 253, 0.2);
  line-height: 1.6;
}

.mantra-meaning {
  font-size: 1.1rem;
  color: #d5dbdb;
  background: rgba(213, 219, 219, 0.1);
  padding: 1.5rem;
  border-radius: 10px;
  margin: 1rem 0;
  border: 1px solid rgba(213, 219, 219, 0.2);
  text-align: justify;
  line-height: 1.7;
}

/* Usage Guidelines Styles */
.usage-guidelines {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.guideline-item {
  background: rgba(255, 255, 255, 0.02);
  padding: 1.5rem;
  border-radius: 10px;
  border-left: 4px solid #f4d03f;
}

.guideline-title {
  font-size: 1.2rem;
  color: #f4d03f;
  margin-bottom: 0.8rem;
  font-weight: 600;
}

.guideline-item p {
  margin-bottom: 0;
  color: #e8f4fd;
}

/* Benefits Card Styles */
.benefits-card {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.05), rgba(255, 255, 255, 0.02));
}

.benefits-list {
  display: grid;
  gap: 1.5rem;
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  background: rgba(255, 255, 255, 0.02);
  padding: 1.5rem;
  border-radius: 10px;
  border: 1px solid rgba(46, 204, 113, 0.2);
  transition: all 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-2px);
  border-color: rgba(46, 204, 113, 0.4);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.benefit-icon {
  font-size: 2rem;
  min-width: 3rem;
  text-align: center;
}

.benefit-content h4 {
  font-size: 1.2rem;
  color: #2ecc71;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.benefit-content p {
  margin-bottom: 0;
  color: #e8f4fd;
  line-height: 1.6;
}

/* Important Notes Card Styles */
.important-notes-card {
  background: linear-gradient(135deg, rgba(231, 76, 60, 0.05), rgba(255, 255, 255, 0.02));
  border-color: rgba(231, 76, 60, 0.3);
}

.important-note {
  background: rgba(231, 76, 60, 0.1);
  padding: 2rem;
  border-radius: 10px;
  border: 1px solid rgba(231, 76, 60, 0.2);
}

.important-note p {
  color: #ffeaa7;
  font-weight: 500;
  text-align: justify;
  margin-bottom: 1.5rem;
}

.important-note p:last-child {
  margin-bottom: 0;
}

/* Footer Styles */
.kubera-footer {
  margin-top: 4rem;
  text-align: center;
  padding: 2rem 0;
}

.kubera-footer .divine-blessing {
  background: rgba(255, 255, 255, 0.05);
  padding: 1.5rem 2rem;
  border-radius: 15px;
  border: 1px solid rgba(244, 208, 63, 0.3);
  display: inline-block;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.floating {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(244, 208, 63, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }
}

/* Responsive Design for Kubera Guide */
@media (max-width: 768px) {
  .kubera-guide-page {
    padding: 1rem 0.5rem;
  }

  .content-title {
    font-size: 1.6rem;
  }

  .content-body {
    font-size: 1rem;
  }

  .sanskrit-mantra {
    font-size: 1.2rem;
    padding: 1rem;
  }

  .sinhala-pronunciation {
    font-size: 1.1rem;
    padding: 1rem;
  }

  .mantra-meaning {
    font-size: 1rem;
    padding: 1rem;
  }

  .benefit-item {
    flex-direction: column;
    text-align: center;
  }

  .benefit-icon {
    min-width: auto;
  }

  .zodiac-nav-btn {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.4rem;
  }

  .content-title {
    font-size: 1.4rem;
  }

  .kubera-content-card {
    padding: 1.5rem;
  }

  .guideline-item {
    padding: 1rem;
  }

  .benefit-item {
    padding: 1rem;
  }

  .important-note {
    padding: 1.5rem;
  }

  /* Kubera Cards Mobile Styles */
  .kubera-cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .card-content {
    padding: 1.5rem;
  }

  .view-all-btn, .zodiac-navigation-btn {
    padding: 1.5rem 2rem;
    font-size: 1.1rem;
  }
}

/* Kubera Cards Showcase Styles */
.kubera-cards-showcase {
  margin: 3rem 0;
}

.content-subtitle {
  font-size: 1.1rem;
  color: #d5dbdb;
  margin-top: 1rem;
  line-height: 1.6;
  text-align: center;
}

.kubera-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.kubera-card-item {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 20px;
  padding: 0;
  text-decoration: none;
  color: inherit;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(15px);
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.kubera-card-item:hover {
  transform: translateY(-15px);
  border-color: rgba(244, 208, 63, 0.6);
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.4),
    0 15px 40px rgba(244, 208, 63, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.card-image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.kubera-card-item:hover .card-image {
  transform: scale(1.05);
}

.discount-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
  animation: pulse 2s infinite;
}

.card-content {
  padding: 2rem;
}

.card-name {
  font-size: 1.4rem;
  font-weight: 700;
  color: #f4d03f;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.card-description {
  font-size: 1rem;
  color: #d5dbdb;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-pricing {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.current-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.original-price {
  font-size: 1.1rem;
  color: #95a5a6;
  text-decoration: line-through;
}

.card-rating {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.stars {
  display: flex;
  gap: 0.2rem;
}

.star {
  font-size: 1rem;
  opacity: 0.3;
  transition: opacity 0.3s ease;
}

.star.filled {
  opacity: 1;
  filter: drop-shadow(0 0 5px rgba(244, 208, 63, 0.5));
}

.rating-text {
  font-size: 0.9rem;
  color: #95a5a6;
}

.view-all-cards {
  text-align: center;
  margin-top: 3rem;
}

.view-all-btn {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f4d03f;
  text-decoration: none;
  transition: all 0.4s ease;
}

.view-all-btn:hover {
  transform: translateY(-5px);
  color: #f4d03f;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);
}

.btn-arrow {
  font-size: 1.5rem;
  transition: transform 0.3s ease;
}

.view-all-btn:hover .btn-arrow {
  transform: translateX(5px);
}

/* Navigation Card Styles */
.navigation-card {
  text-align: center;
  margin: 3rem 0;
}

.zodiac-navigation-btn {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem 3rem;
  font-size: 1.3rem;
  font-weight: 600;
  color: #f4d03f;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.4s ease;
  margin-top: 2rem;
}

.zodiac-navigation-btn:hover {
  transform: translateY(-5px);
  color: #f4d03f;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);
}

.btn-icon {
  font-size: 2rem;
  animation: float 3s ease-in-out infinite;
}

.btn-text {
  font-size: 1.3rem;
}

.zodiac-navigation-btn:hover .btn-arrow {
  transform: translateX(5px);
}

/* Additional responsive styles for Kubera Cards */
@media (max-width: 768px) {
  .kubera-cards-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .card-content {
    padding: 1.5rem;
  }

  .view-all-btn, .zodiac-navigation-btn {
    padding: 1.5rem 2rem;
    font-size: 1.1rem;
  }

  .card-name {
    font-size: 1.2rem;
  }

  .current-price {
    font-size: 1.3rem;
  }
}

/* Product Details Page Styles */
.product-details-page {
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.product-details-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1400px;
  margin: 0 auto;
  padding-top: 6rem;
  z-index: 2;
  position: relative;
}

/* Product Images Section */
.product-images-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.main-image-container {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  backdrop-filter: blur(15px);
}

.main-product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.main-image-container:hover .main-product-image {
  transform: scale(1.05);
}

.discount-badge-large {
  position: absolute;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: 25px;
  font-size: 1.2rem;
  font-weight: 700;
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
  animation: pulse 2s infinite;
}

.thumbnail-images {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  object-fit: cover;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.thumbnail:hover {
  opacity: 1;
  border-color: rgba(244, 208, 63, 0.5);
}

.thumbnail.active {
  opacity: 1;
  border-color: #f4d03f;
  box-shadow: 0 0 15px rgba(244, 208, 63, 0.4);
}

/* Product Info Section */
.product-info-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.product-header {
  padding: 3rem;
}

.product-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #f4d03f;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.4);
}

.product-english-name {
  font-size: 1.2rem;
  color: #95a5a6;
  margin-bottom: 2rem;
  font-style: italic;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.rating-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #f4d03f;
}

.review-count {
  font-size: 1rem;
  color: #95a5a6;
}

.product-pricing {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.product-pricing .current-price {
  font-size: 2.5rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.4);
}

.product-pricing .original-price {
  font-size: 1.5rem;
  color: #95a5a6;
  text-decoration: line-through;
}

.savings {
  font-size: 1.1rem;
  color: #27ae60;
  font-weight: 600;
}

.product-description {
  font-size: 1.2rem;
  color: #d5dbdb;
  line-height: 1.8;
}

/* Purchase Section */
.purchase-section {
  padding: 3rem;
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.quantity-selector label {
  font-size: 1.2rem;
  color: #f4d03f;
  font-weight: 600;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.quantity-btn {
  width: 40px;
  height: 40px;
  border: 1px solid rgba(244, 208, 63, 0.3);
  background: rgba(255, 255, 255, 0.05);
  color: #f4d03f;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.2rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-btn:hover:not(:disabled) {
  background: rgba(244, 208, 63, 0.1);
  border-color: rgba(244, 208, 63, 0.5);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-controls input {
  width: 60px;
  height: 40px;
  text-align: center;
  border: 1px solid rgba(244, 208, 63, 0.3);
  background: rgba(255, 255, 255, 0.05);
  color: #f4d03f;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
}

.total-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: #f4d03f;
  text-align: center;
  margin-bottom: 2rem;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.purchase-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.add-to-cart-btn, .buy-now-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1.5rem 2rem;
  font-size: 1.2rem;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.4s ease;
  text-decoration: none;
}

.add-to-cart-btn {
  color: #f4d03f;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
}

.buy-now-btn.primary {
  color: #1a1a2e;
  background: linear-gradient(135deg, #f4d03f, #f39c12);
  border: 1px solid #f4d03f;
  box-shadow: 0 8px 25px rgba(244, 208, 63, 0.3);
}

.add-to-cart-btn:hover {
  transform: translateY(-3px);
  border-color: rgba(244, 208, 63, 0.6);
  box-shadow: 0 10px 30px rgba(244, 208, 63, 0.2);
}

.buy-now-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(244, 208, 63, 0.4);
}

.btn-icon {
  font-size: 1.5rem;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(244, 208, 63, 0.2);
}

.payment-method, .delivery-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.1rem;
  color: #d5dbdb;
}

.payment-icon, .delivery-icon {
  font-size: 1.5rem;
}

/* Loading and Error States */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  z-index: 2;
  position: relative;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(244, 208, 63, 0.3);
  border-top: 3px solid #f4d03f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.back-to-cards-btn {
  padding: 1.5rem 3rem;
  font-size: 1.2rem;
  color: #f4d03f;
  text-decoration: none;
  margin-top: 2rem;
}

/* Product Details Sections */
.product-details-sections {
  max-width: 1400px;
  margin: 4rem auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  z-index: 2;
  position: relative;
}

.details-section {
  padding: 3rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: #f4d03f;
  margin-bottom: 2rem;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
  text-align: center;
}

.benefits-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  border-color: rgba(244, 208, 63, 0.4);
  transform: translateX(10px);
}

.benefit-icon {
  font-size: 1.5rem;
  color: #f4d03f;
}

.benefit-text {
  font-size: 1.1rem;
  color: #d5dbdb;
  line-height: 1.6;
}

.long-description {
  line-height: 1.8;
}

.description-paragraph {
  font-size: 1.1rem;
  color: #d5dbdb;
  margin-bottom: 1.5rem;
}

.specifications-list {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.spec-item:hover {
  border-color: rgba(244, 208, 63, 0.4);
}

.spec-label {
  font-size: 1.1rem;
  color: #f4d03f;
  font-weight: 600;
}

.spec-value {
  font-size: 1.1rem;
  color: #d5dbdb;
}

/* Related Products Section */
.related-products-section {
  max-width: 1400px;
  margin: 4rem auto;
  z-index: 2;
  position: relative;
}

.section-header {
  text-align: center;
  padding: 2rem;
  margin-bottom: 2rem;
}

.related-products-grid {
  display: flex;
  justify-content: center;
}

.view-all-products-btn {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem 3rem;
  font-size: 1.3rem;
  font-weight: 600;
  color: #f4d03f;
  text-decoration: none;
  transition: all 0.4s ease;
}

.view-all-products-btn:hover {
  transform: translateY(-5px);
  color: #f4d03f;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);
}

.view-all-products-btn:hover .btn-arrow {
  transform: translateX(5px);
}

/* Product Footer */
.product-footer {
  text-align: center;
  margin: 4rem 0 2rem 0;
  z-index: 2;
  position: relative;
}

/* Responsive Design for Product Details */
@media (max-width: 1024px) {
  .product-details-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    padding-top: 4rem;
  }

  .main-image-container {
    height: 400px;
  }

  .product-title {
    font-size: 2rem;
  }

  .product-pricing .current-price {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .product-details-page {
    padding: 1rem;
  }

  .product-header, .purchase-section, .details-section {
    padding: 2rem;
  }

  .main-image-container {
    height: 300px;
  }

  .product-title {
    font-size: 1.8rem;
  }

  .purchase-buttons {
    gap: 1rem;
  }

  .add-to-cart-btn, .buy-now-btn {
    padding: 1.2rem 1.5rem;
    font-size: 1.1rem;
  }

  .benefits-list, .specifications-list {
    gap: 1rem;
  }

  .benefit-item, .spec-item {
    padding: 1rem;
  }
}

/* Kubera Cards Page Styles */
.kubera-cards-page {
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.page-header {
  text-align: center;
  margin: 6rem 0 4rem 0;
  z-index: 2;
  position: relative;
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow:
    0 0 20px rgba(244, 208, 63, 0.6),
    0 0 40px rgba(244, 208, 63, 0.4);
  margin-bottom: 1rem;
  animation: divineGlow 3s ease-in-out infinite alternate;
}

.page-subtitle {
  font-size: 1.3rem;
  color: #d5dbdb;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Category Filter */
.category-filter {
  max-width: 1200px;
  margin: 0 auto 4rem auto;
  z-index: 2;
  position: relative;
}

.filter-container {
  padding: 2.5rem;
  text-align: center;
}

.filter-title {
  font-size: 1.5rem;
  color: #f4d03f;
  margin-bottom: 2rem;
  font-weight: 600;
}

.category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  justify-content: center;
}

.category-btn {
  padding: 1rem 2rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 25px;
  color: #d5dbdb;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.category-btn:hover {
  border-color: rgba(244, 208, 63, 0.5);
  color: #f4d03f;
  transform: translateY(-2px);
}

.category-btn.active {
  background: linear-gradient(135deg, rgba(244, 208, 63, 0.2), rgba(244, 208, 63, 0.1));
  border-color: #f4d03f;
  color: #f4d03f;
  box-shadow: 0 5px 20px rgba(244, 208, 63, 0.3);
}

/* Cards Grid */
.cards-grid-container {
  max-width: 1400px;
  margin: 0 auto;
  z-index: 2;
  position: relative;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  margin-bottom: 4rem;
}

.card-item {
  background: rgba(255, 255, 255, 0.02) !important;
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 20px;
  padding: 0;
  text-decoration: none;
  color: inherit;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(15px);
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.card-item:hover {
  transform: translateY(-15px);
  border-color: rgba(244, 208, 63, 0.6);
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.4),
    0 15px 40px rgba(244, 208, 63, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.featured-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
}

.card-benefits {
  margin-top: 1.5rem;
}

.benefits-preview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.benefit-preview {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #95a5a6;
}

.benefit-preview .benefit-icon {
  font-size: 0.8rem;
  color: #f4d03f;
}

.more-benefits {
  font-size: 0.9rem;
  color: #f4d03f;
  font-weight: 500;
  margin-top: 0.5rem;
  text-align: center;
}

/* No Cards Message */
.no-cards-message {
  text-align: center;
  padding: 4rem;
  margin: 2rem 0;
}

.no-cards-message h3 {
  font-size: 1.8rem;
  color: #f4d03f;
  margin-bottom: 1rem;
}

.no-cards-message p {
  font-size: 1.1rem;
  color: #d5dbdb;
  line-height: 1.6;
}

/* Page Footer */
.page-footer {
  text-align: center;
  margin: 4rem 0 2rem 0;
  z-index: 2;
  position: relative;
}

/* Responsive Design for Cards Page */
@media (max-width: 768px) {
  .kubera-cards-page {
    padding: 1rem;
  }

  .page-title {
    font-size: 2.2rem;
  }

  .page-subtitle {
    font-size: 1.1rem;
  }

  .category-buttons {
    flex-direction: column;
    align-items: center;
  }

  .category-btn {
    width: 100%;
    max-width: 300px;
  }

  .cards-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .filter-container {
    padding: 2rem;
  }
}

/* Cart Page Styles */
.cart-page {
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.cart-header {
  text-align: center;
  margin: 6rem 0 4rem 0;
  z-index: 2;
  position: relative;
}

.cart-title {
  font-size: 3rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow:
    0 0 20px rgba(244, 208, 63, 0.6),
    0 0 40px rgba(244, 208, 63, 0.4);
  margin-bottom: 1rem;
  animation: divineGlow 3s ease-in-out infinite alternate;
}

.cart-subtitle {
  font-size: 1.3rem;
  color: #d5dbdb;
  line-height: 1.6;
}

/* Empty Cart Styles */
.empty-cart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  z-index: 2;
  position: relative;
}

.empty-cart-card {
  text-align: center;
  padding: 4rem;
  max-width: 600px;
}

.empty-cart-icon {
  font-size: 5rem;
  margin-bottom: 2rem;
  opacity: 0.7;
}

.empty-cart-title {
  font-size: 2rem;
  color: #f4d03f;
  margin-bottom: 1rem;
  font-weight: 600;
}

.empty-cart-message {
  font-size: 1.2rem;
  color: #d5dbdb;
  line-height: 1.6;
  margin-bottom: 3rem;
}

.empty-cart-actions {
  display: flex;
  justify-content: center;
}

.shop-now-btn {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f4d03f;
  text-decoration: none;
  transition: all 0.4s ease;
}

.shop-now-btn:hover {
  transform: translateY(-5px);
  color: #f4d03f;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);
}

/* Cart Container */
.cart-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
  z-index: 2;
  position: relative;
}

/* Cart Items Section */
.cart-items-section {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.cart-items-header {
  padding: 2rem;
  text-align: center;
}

.cart-items-header h3 {
  font-size: 1.8rem;
  color: #f4d03f;
  font-weight: 600;
}

.cart-items-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.cart-item {
  display: grid;
  grid-template-columns: 120px 1fr auto;
  gap: 2rem;
  padding: 2rem;
  align-items: center;
}

.item-image {
  width: 120px;
  height: 120px;
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgba(244, 208, 63, 0.3);
}

.item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.item-name {
  font-size: 1.4rem;
  color: #f4d03f;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.item-price {
  font-size: 1.2rem;
  color: #d5dbdb;
  font-weight: 500;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.quantity-btn {
  width: 35px;
  height: 35px;
  border: 1px solid rgba(244, 208, 63, 0.3);
  background: rgba(255, 255, 255, 0.05);
  color: #f4d03f;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background: rgba(244, 208, 63, 0.1);
  border-color: rgba(244, 208, 63, 0.5);
}

.quantity-display {
  font-size: 1.2rem;
  color: #f4d03f;
  font-weight: 600;
  min-width: 30px;
  text-align: center;
}

.item-total {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 1rem;
}

.total-price {
  font-size: 1.5rem;
  color: #f4d03f;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.remove-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  opacity: 0.7;
  transition: all 0.3s ease;
  padding: 0.5rem;
  border-radius: 8px;
}

.remove-btn:hover {
  opacity: 1;
  background: rgba(231, 76, 60, 0.1);
  transform: scale(1.1);
}

/* Cart Summary Section */
.cart-summary-section {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.cart-summary {
  padding: 3rem;
}

.summary-title {
  font-size: 1.8rem;
  color: #f4d03f;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
}

.summary-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  color: #d5dbdb;
}

.summary-row span:last-child {
  font-weight: 600;
  color: #f4d03f;
}

.summary-divider {
  height: 1px;
  background: rgba(244, 208, 63, 0.3);
  margin: 1rem 0;
}

.total-row {
  font-size: 1.4rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(244, 208, 63, 0.2);
}

.payment-method, .delivery-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1rem;
  color: #d5dbdb;
}

.payment-icon, .delivery-icon {
  font-size: 1.3rem;
}

.checkout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a2e;
  background: linear-gradient(135deg, #f4d03f, #f39c12);
  border: 1px solid #f4d03f;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.4s ease;
  margin-bottom: 2rem;
  box-shadow: 0 8px 25px rgba(244, 208, 63, 0.3);
}

.checkout-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(244, 208, 63, 0.4);
}

.continue-shopping-btn {
  display: block;
  text-align: center;
  color: #95a5a6;
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.continue-shopping-btn:hover {
  color: #f4d03f;
}

/* Cart Footer */
.cart-footer {
  text-align: center;
  margin: 4rem 0 2rem 0;
  z-index: 2;
  position: relative;
}

/* Responsive Design for Cart */
@media (max-width: 1024px) {
  .cart-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .cart-summary-section {
    position: static;
  }
}

@media (max-width: 768px) {
  .cart-page {
    padding: 1rem;
  }

  .cart-title {
    font-size: 2.2rem;
  }

  .cart-item {
    grid-template-columns: 80px 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }

  .item-image {
    width: 80px;
    height: 80px;
  }

  .item-total {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(244, 208, 63, 0.2);
  }

  .cart-summary {
    padding: 2rem;
  }

  .empty-cart-card {
    padding: 3rem 2rem;
  }

  .empty-cart-icon {
    font-size: 4rem;
  }

  .empty-cart-title {
    font-size: 1.6rem;
  }
}

/* Checkout Page Styles */
.checkout-page {
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.checkout-header {
  text-align: center;
  margin: 6rem 0 4rem 0;
  z-index: 2;
  position: relative;
}

.checkout-title {
  font-size: 3rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow:
    0 0 20px rgba(244, 208, 63, 0.6),
    0 0 40px rgba(244, 208, 63, 0.4);
  margin-bottom: 1rem;
  animation: divineGlow 3s ease-in-out infinite alternate;
}

.checkout-subtitle {
  font-size: 1.3rem;
  color: #d5dbdb;
  line-height: 1.6;
}

.checkout-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 3rem;
  max-width: 1400px;
  margin: 0 auto;
  z-index: 2;
  position: relative;
}

/* Form Section */
.checkout-form-section {
  display: flex;
  flex-direction: column;
}

.form-card {
  padding: 3rem;
}

.form-title {
  font-size: 2rem;
  color: #f4d03f;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
}

.checkout-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.form-group label {
  font-size: 1.1rem;
  color: #f4d03f;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  padding: 1rem 1.5rem;
  border: 1px solid rgba(244, 208, 63, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  color: #d5dbdb;
  font-size: 1rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: rgba(244, 208, 63, 0.6);
  box-shadow: 0 0 15px rgba(244, 208, 63, 0.2);
  background: rgba(255, 255, 255, 0.08);
}

.form-group input.error,
.form-group textarea.error {
  border-color: #e74c3c;
  box-shadow: 0 0 15px rgba(231, 76, 60, 0.2);
}

.error-message {
  color: #e74c3c;
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* Payment Method Info */
.payment-method-info {
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 15px;
  margin-top: 1rem;
}

.payment-method-info h4 {
  font-size: 1.3rem;
  color: #f4d03f;
  margin-bottom: 1rem;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.payment-icon {
  font-size: 2rem;
}

.payment-details strong {
  color: #f4d03f;
  font-size: 1.1rem;
}

.payment-details p {
  color: #95a5a6;
  margin-top: 0.25rem;
}

/* Submit Button */
.submit-order-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem;
  font-size: 1.3rem;
  font-weight: 700;
  color: #1a1a2e;
  background: linear-gradient(135deg, #f4d03f, #f39c12);
  border: 1px solid #f4d03f;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.4s ease;
  margin-top: 2rem;
  box-shadow: 0 8px 25px rgba(244, 208, 63, 0.3);
}

.submit-order-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(244, 208, 63, 0.4);
}

.submit-order-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(26, 26, 46, 0.3);
  border-top: 2px solid #1a1a2e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Order Summary Section */
.order-summary-section {
  position: sticky;
  top: 2rem;
  height: fit-content;
}

.summary-card {
  padding: 3rem;
}

.summary-title {
  font-size: 1.8rem;
  color: #f4d03f;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
}

.order-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.order-item {
  display: grid;
  grid-template-columns: 60px 1fr auto;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 10px;
}

.order-item .item-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
}

.order-item .item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.order-item .item-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.order-item .item-name {
  font-size: 1rem;
  color: #f4d03f;
  font-weight: 500;
}

.order-item .item-quantity {
  font-size: 0.9rem;
  color: #95a5a6;
}

.order-item .item-price {
  font-size: 0.9rem;
  color: #d5dbdb;
}

.order-item .item-total {
  font-size: 1.1rem;
  color: #f4d03f;
  font-weight: 600;
  text-align: right;
}

.order-totals {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  color: #d5dbdb;
}

.total-row span:last-child {
  font-weight: 600;
  color: #f4d03f;
}

.total-divider {
  height: 1px;
  background: rgba(244, 208, 63, 0.3);
  margin: 0.5rem 0;
}

.final-total {
  font-size: 1.3rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

.delivery-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(244, 208, 63, 0.2);
}

.delivery-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.delivery-icon {
  font-size: 1.5rem;
}

.delivery-details strong {
  color: #f4d03f;
  font-size: 1rem;
}

.delivery-details p {
  color: #95a5a6;
  font-size: 0.9rem;
  margin-top: 0.25rem;
}

/* Checkout Footer */
.checkout-footer {
  text-align: center;
  margin: 4rem 0 2rem 0;
  z-index: 2;
  position: relative;
}

/* Responsive Design for Checkout */
@media (max-width: 1024px) {
  .checkout-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .order-summary-section {
    position: static;
    order: -1;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .checkout-page {
    padding: 1rem;
  }

  .checkout-title {
    font-size: 2.2rem;
  }

  .form-card, .summary-card {
    padding: 2rem;
  }

  .order-item {
    grid-template-columns: 50px 1fr;
    gap: 1rem;
  }

  .order-item .item-image {
    width: 50px;
    height: 50px;
  }

  .order-item .item-total {
    grid-column: 1 / -1;
    text-align: left;
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid rgba(244, 208, 63, 0.2);
  }

  .submit-order-btn {
    padding: 1.5rem;
    font-size: 1.1rem;
  }
}

/* Order Confirmation Page Styles */
.order-confirmation-page {
  min-height: 100vh;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.confirmation-header {
  text-align: center;
  margin: 6rem 0 4rem 0;
  z-index: 2;
  position: relative;
}

.success-icon {
  font-size: 5rem;
  margin-bottom: 2rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.confirmation-title {
  font-size: 3rem;
  font-weight: 700;
  color: #27ae60;
  text-shadow:
    0 0 20px rgba(39, 174, 96, 0.6),
    0 0 40px rgba(39, 174, 96, 0.4);
  margin-bottom: 1rem;
  animation: divineGlow 3s ease-in-out infinite alternate;
}

.confirmation-subtitle {
  font-size: 1.3rem;
  color: #d5dbdb;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.confirmation-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto 4rem auto;
  z-index: 2;
  position: relative;
}

/* Card Styles */
.order-details-card,
.customer-info-card,
.order-items-card,
.next-steps-card {
  padding: 3rem;
}

.card-title {
  font-size: 1.8rem;
  color: #f4d03f;
  font-weight: 600;
  margin-bottom: 2rem;
  text-align: center;
}

/* Order Details */
.order-info {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 10px;
}

.info-label {
  font-size: 1.1rem;
  color: #d5dbdb;
}

.info-value {
  font-size: 1.1rem;
  color: #f4d03f;
  font-weight: 600;
}

.order-id {
  font-family: monospace;
  background: rgba(244, 208, 63, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(244, 208, 63, 0.3);
}

.status-pending {
  color: #f39c12;
  background: rgba(243, 156, 18, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(243, 156, 18, 0.3);
}

/* Customer Information */
.customer-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 10px;
}

.detail-icon {
  font-size: 1.5rem;
  color: #f4d03f;
  margin-top: 0.25rem;
}

.detail-content {
  flex: 1;
  color: #d5dbdb;
  line-height: 1.6;
}

.detail-content strong {
  color: #f4d03f;
  margin-right: 0.5rem;
}

.address {
  margin-top: 0.5rem;
  line-height: 1.5;
}

/* Order Items */
.order-items-card {
  grid-column: 1 / -1;
}

.ordered-items {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.ordered-item {
  display: grid;
  grid-template-columns: 80px 1fr auto;
  gap: 1.5rem;
  align-items: center;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 15px;
}

.ordered-item .item-image {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
  border: 1px solid rgba(244, 208, 63, 0.3);
}

.ordered-item .item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ordered-item .item-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.ordered-item .item-name {
  font-size: 1.2rem;
  color: #f4d03f;
  font-weight: 600;
}

.ordered-item .item-quantity,
.ordered-item .item-unit-price {
  font-size: 1rem;
  color: #95a5a6;
}

.ordered-item .item-total {
  font-size: 1.3rem;
  color: #f4d03f;
  font-weight: 700;
  text-align: right;
}

.order-summary {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(244, 208, 63, 0.3);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1rem;
  color: #d5dbdb;
}

.summary-row span:last-child {
  font-weight: 600;
  color: #f4d03f;
}

.summary-divider {
  height: 1px;
  background: rgba(244, 208, 63, 0.3);
  margin: 0.5rem 0;
}

.total-row {
  font-size: 1.4rem;
  font-weight: 700;
  color: #f4d03f;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

/* Next Steps */
.next-steps-card {
  grid-column: 1 / -1;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(244, 208, 63, 0.2);
  border-radius: 15px;
  transition: all 0.3s ease;
}

.step-item:hover {
  border-color: rgba(244, 208, 63, 0.4);
  transform: translateX(10px);
}

.step-number {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #f4d03f, #f39c12);
  color: #1a1a2e;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  font-weight: 700;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(244, 208, 63, 0.3);
}

.step-content {
  flex: 1;
}

.step-content strong {
  color: #f4d03f;
  font-size: 1.2rem;
  display: block;
  margin-bottom: 0.5rem;
}

.step-content p {
  color: #d5dbdb;
  line-height: 1.6;
  margin: 0;
}

/* Action Buttons */
.confirmation-actions {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 4rem 0;
  z-index: 2;
  position: relative;
}

.continue-shopping-btn,
.home-btn {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f4d03f;
  text-decoration: none;
  transition: all 0.4s ease;
}

.continue-shopping-btn:hover,
.home-btn:hover {
  transform: translateY(-5px);
  color: #f4d03f;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);
}

/* Error States */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  z-index: 2;
  position: relative;
}

.error-card {
  text-align: center;
  padding: 4rem;
  max-width: 600px;
}

.error-icon {
  font-size: 5rem;
  margin-bottom: 2rem;
  opacity: 0.7;
}

.error-title {
  font-size: 2rem;
  color: #e74c3c;
  margin-bottom: 1rem;
  font-weight: 600;
}

.error-message {
  font-size: 1.2rem;
  color: #d5dbdb;
  line-height: 1.6;
  margin-bottom: 3rem;
}

.error-actions {
  display: flex;
  justify-content: center;
}

/* Confirmation Footer */
.confirmation-footer {
  text-align: center;
  margin: 4rem 0 2rem 0;
  z-index: 2;
  position: relative;
}

/* Responsive Design for Order Confirmation */
@media (max-width: 1024px) {
  .confirmation-container {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .order-items-card,
  .next-steps-card {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .order-confirmation-page {
    padding: 1rem;
  }

  .confirmation-title {
    font-size: 2.2rem;
  }

  .success-icon {
    font-size: 4rem;
  }

  .order-details-card,
  .customer-info-card,
  .order-items-card,
  .next-steps-card {
    padding: 2rem;
  }

  .ordered-item {
    grid-template-columns: 60px 1fr;
    gap: 1rem;
  }

  .ordered-item .item-image {
    width: 60px;
    height: 60px;
  }

  .ordered-item .item-total {
    grid-column: 1 / -1;
    text-align: left;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(244, 208, 63, 0.2);
  }

  .confirmation-actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .continue-shopping-btn,
  .home-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .step-item {
    padding: 1.5rem;
  }

  .step-number {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
}

/* Floating Cart Button Styles */
.floating-cart-button {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 5px 15px rgba(244, 208, 63, 0.4);
  animation: float 3s ease-in-out infinite;
}

.floating-cart-button:hover {
  transform: translateY(-5px) scale(1.1);
  box-shadow:
    0 20px 50px rgba(0, 0, 0, 0.4),
    0 10px 25px rgba(244, 208, 63, 0.6);
}

.cart-icon {
  font-size: 2rem;
  color: #f4d03f;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);
}

.cart-count {
  position: absolute;
  top: -5px;
  right: -5px;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 700;
  box-shadow: 0 2px 10px rgba(231, 76, 60, 0.5);
  animation: pulse 2s infinite;
}

.cart-tooltip {
  position: absolute;
  bottom: 80px;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  color: #f4d03f;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(244, 208, 63, 0.3);
}

.cart-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 20px;
  border: 5px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}

.floating-cart-button:hover .cart-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateY(-5px);
}

/* Back Button Styles */
.back-button {
  position: fixed;
  top: 2rem;
  left: 2rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: #f4d03f;
  text-decoration: none;
  z-index: 1000;
  transition: all 0.3s ease;
}

.back-button:hover {
  transform: translateX(-5px);
  color: #f4d03f;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);
}

.back-arrow {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.back-button:hover .back-arrow {
  transform: translateX(-3px);
}

/* Responsive Design for Navigation Elements */
@media (max-width: 768px) {
  .floating-cart-button {
    width: 60px;
    height: 60px;
    bottom: 1.5rem;
    right: 1.5rem;
  }

  .cart-icon {
    font-size: 1.8rem;
  }

  .cart-count {
    width: 22px;
    height: 22px;
    font-size: 0.7rem;
  }

  .cart-tooltip {
    bottom: 70px;
    font-size: 0.8rem;
  }

  .back-button {
    top: 1rem;
    left: 1rem;
    padding: 0.8rem 1.2rem;
    font-size: 0.9rem;
  }
}

/* Card Action Styles - Consistent across all ecommerce components */
.card-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  margin-top: auto;
  border-top: 1px solid rgba(244, 208, 63, 0.2);
  background: rgba(244, 208, 63, 0.05);
  transition: all 0.3s ease;
}

.kubera-card-item:hover .card-action,
.card-item:hover .card-action {
  background: rgba(244, 208, 63, 0.1);
  border-top-color: rgba(244, 208, 63, 0.4);
}

.action-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #f4d03f;
  text-shadow: 0 0 5px rgba(244, 208, 63, 0.3);
}

.action-arrow {
  font-size: 1.5rem;
  color: #f4d03f;
  transition: transform 0.3s ease;
}

.kubera-card-item:hover .action-arrow,
.card-item:hover .action-arrow {
  transform: translateX(5px);
}

/* Enhanced Glass Morphism Effects */
.dark-glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.02) 100%
  );
  border-radius: inherit;
  pointer-events: none;
  z-index: 1;
}

.dark-glass-card > * {
  position: relative;
  z-index: 2;
}

/* Spiritual Theme Enhancements */
.spiritual-glow {
  box-shadow:
    0 0 20px rgba(244, 208, 63, 0.3),
    0 0 40px rgba(244, 208, 63, 0.2),
    0 0 60px rgba(244, 208, 63, 0.1);
}

.divine-energy {
  background: radial-gradient(
    circle at center,
    rgba(244, 208, 63, 0.1) 0%,
    rgba(244, 208, 63, 0.05) 50%,
    transparent 100%
  );
}

/* Premium Button Styles */
.premium-btn {
  background: linear-gradient(135deg, #f4d03f, #f39c12);
  color: #1a1a2e;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s ease;
  box-shadow: 0 8px 25px rgba(244, 208, 63, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.premium-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(244, 208, 63, 0.4);
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

/* Consistent Typography */
.spiritual-title {
  font-family: 'Georgia', serif;
  color: #f4d03f;
  text-shadow: 0 0 15px rgba(244, 208, 63, 0.4);
  font-weight: 700;
}

.spiritual-text {
  color: #d5dbdb;
  line-height: 1.8;
  font-size: 1.1rem;
}

.price-text {
  color: #f4d03f;
  font-weight: 700;
  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);
}

/* Loading States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #f4d03f;
}

.loading-state .loading-spinner {
  margin-right: 1rem;
}

/* Success States */
.success-state {
  color: #27ae60;
  background: rgba(39, 174, 96, 0.1);
  border: 1px solid rgba(39, 174, 96, 0.3);
  padding: 1rem;
  border-radius: 10px;
  text-align: center;
}

/* Error States */
.error-state {
  color: #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.3);
  padding: 1rem;
  border-radius: 10px;
  text-align: center;
}