{"ast": null, "code": "import * as o from \"react\";\nconst f = /* @__PURE__ */(() => {\n  var e, t;\n  return typeof window != \"undefined\" && (((e = window.document) == null ? void 0 : e.createElement) || ((t = window.navigator) == null ? void 0 : t.product) === \"ReactNative\");\n})() ? o.useLayoutEffect : o.useEffect;\nfunction i(e, t, r) {\n  if (!e) return;\n  if (r(e) === !0) return e;\n  let n = t ? e.return : e.child;\n  for (; n;) {\n    const u = i(n, t, r);\n    if (u) return u;\n    n = t ? null : n.sibling;\n  }\n}\nfunction l(e) {\n  try {\n    return Object.defineProperties(e, {\n      _currentRenderer: {\n        get() {\n          return null;\n        },\n        set() {}\n      },\n      _currentRenderer2: {\n        get() {\n          return null;\n        },\n        set() {}\n      }\n    });\n  } catch (t) {\n    return e;\n  }\n}\nconst a = /* @__PURE__ */l(/* @__PURE__ */o.createContext(null));\nclass m extends o.Component {\n  render() {\n    return /* @__PURE__ */o.createElement(a.Provider, {\n      value: this._reactInternals\n    }, this.props.children);\n  }\n}\nfunction c() {\n  const e = o.useContext(a);\n  if (e === null) throw new Error(\"its-fine: useFiber must be called within a <FiberProvider />!\");\n  const t = o.useId();\n  return o.useMemo(() => {\n    for (const n of [e, e == null ? void 0 : e.alternate]) {\n      if (!n) continue;\n      const u = i(n, !1, d => {\n        let s = d.memoizedState;\n        for (; s;) {\n          if (s.memoizedState === t) return !0;\n          s = s.next;\n        }\n      });\n      if (u) return u;\n    }\n  }, [e, t]);\n}\nfunction w() {\n  const e = c(),\n    t = o.useMemo(() => i(e, !0, r => {\n      var n;\n      return ((n = r.stateNode) == null ? void 0 : n.containerInfo) != null;\n    }), [e]);\n  return t == null ? void 0 : t.stateNode.containerInfo;\n}\nfunction v(e) {\n  const t = c(),\n    r = o.useRef(void 0);\n  return f(() => {\n    var n;\n    r.current = (n = i(t, !1, u => typeof u.type == \"string\" && (e === void 0 || u.type === e))) == null ? void 0 : n.stateNode;\n  }, [t]), r;\n}\nfunction y(e) {\n  const t = c(),\n    r = o.useRef(void 0);\n  return f(() => {\n    var n;\n    r.current = (n = i(t, !0, u => typeof u.type == \"string\" && (e === void 0 || u.type === e))) == null ? void 0 : n.stateNode;\n  }, [t]), r;\n}\nconst p = Symbol.for(\"react.context\"),\n  b = e => e !== null && typeof e == \"object\" && \"$$typeof\" in e && e.$$typeof === p;\nfunction h() {\n  const e = c(),\n    [t] = o.useState(() => /* @__PURE__ */new Map());\n  t.clear();\n  let r = e;\n  for (; r;) {\n    const n = r.type;\n    b(n) && n !== a && !t.has(n) && t.set(n, o.use(l(n))), r = r.return;\n  }\n  return t;\n}\nfunction x() {\n  const e = h();\n  return o.useMemo(() => Array.from(e.keys()).reduce((t, r) => n => /* @__PURE__ */o.createElement(t, null, /* @__PURE__ */o.createElement(r.Provider, {\n    ...n,\n    value: e.get(r)\n  })), t => /* @__PURE__ */o.createElement(m, {\n    ...t\n  })), [e]);\n}\nexport { m as FiberProvider, i as traverseFiber, w as useContainer, x as useContextBridge, h as useContextMap, c as useFiber, v as useNearestChild, y as useNearestParent };", "map": {"version": 3, "names": ["f", "window", "e", "document", "createElement", "t", "navigator", "product", "o", "useLayoutEffect", "useEffect", "i", "r", "n", "return", "child", "u", "sibling", "l", "Object", "defineProperties", "_current<PERSON><PERSON><PERSON>", "get", "set", "_currentRenderer2", "a", "createContext", "m", "Component", "render", "Provider", "value", "_reactInternals", "props", "children", "c", "useContext", "Error", "useId", "useMemo", "alternate", "d", "s", "memoizedState", "next", "w", "stateNode", "containerInfo", "v", "useRef", "current", "type", "y", "p", "Symbol", "for", "b", "$$typeof", "h", "useState", "Map", "clear", "has", "use", "x", "Array", "from", "keys", "reduce"], "sources": ["C:\\Users\\<USER>\\Desktop\\Horoscope\\node_modules\\its-fine\\src\\index.tsx"], "sourcesContent": ["import * as React from 'react'\r\nimport type <PERSON>actR<PERSON>onciler from 'react-reconciler'\r\n\r\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\r\nconst useIsomorphicLayoutEffect = /* @__PURE__ */ (() =>\r\n  typeof window !== 'undefined' && (window.document?.createElement || window.navigator?.product === 'ReactNative'))()\r\n  ? React.useLayoutEffect\r\n  : React.useEffect\r\n\r\n/**\r\n * Represents a react-internal Fiber node.\r\n */\r\nexport type Fiber<T = any> = Omit<ReactReconciler.Fiber, 'stateNode'> & { stateNode: T }\r\n\r\n/**\r\n * Represents a {@link Fiber} node selector for traversal.\r\n */\r\nexport type FiberSelector<T = any> = (\r\n  /** The current {@link Fiber} node. */\r\n  node: Fiber<T | null>,\r\n) => boolean | void\r\n\r\n/**\r\n * Traverses up or down a {@link Fiber}, return `true` to stop and select a node.\r\n */\r\nexport function traverseFiber<T = any>(\r\n  /** Input {@link Fiber} to traverse. */\r\n  fiber: Fiber | undefined,\r\n  /** Whether to ascend and walk up the tree. Will walk down if `false`. */\r\n  ascending: boolean,\r\n  /** A {@link Fiber} node selector, returns the first match when `true` is passed. */\r\n  selector: FiberSelector<T>,\r\n): Fiber<T> | undefined {\r\n  if (!fiber) return\r\n  if (selector(fiber) === true) return fiber\r\n\r\n  let child = ascending ? fiber.return : fiber.child\r\n  while (child) {\r\n    const match = traverseFiber(child, ascending, selector)\r\n    if (match) return match\r\n\r\n    child = ascending ? null : child.sibling\r\n  }\r\n}\r\n\r\n// In development, React will warn about using contexts between renderers.\r\n// Hide the warning because its-fine fixes this issue\r\n// https://github.com/facebook/react/pull/12779\r\nfunction wrapContext<T>(context: React.Context<T>): React.Context<T> {\r\n  try {\r\n    return Object.defineProperties(context, {\r\n      _currentRenderer: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n      _currentRenderer2: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n    })\r\n  } catch (_) {\r\n    return context\r\n  }\r\n}\r\n\r\nconst FiberContext = /* @__PURE__ */ wrapContext(/* @__PURE__ */ React.createContext<Fiber>(null!))\r\n\r\n/**\r\n * A react-internal {@link Fiber} provider. This component binds React children to the React Fiber tree. Call its-fine hooks within this.\r\n */\r\nexport class FiberProvider extends React.Component<{ children?: React.ReactNode }> {\r\n  private _reactInternals!: Fiber\r\n\r\n  render() {\r\n    return <FiberContext.Provider value={this._reactInternals}>{this.props.children}</FiberContext.Provider>\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the current react-internal {@link Fiber}. This is an implementation detail of [react-reconciler](https://github.com/facebook/react/tree/main/packages/react-reconciler).\r\n */\r\nexport function useFiber(): Fiber<null> | undefined {\r\n  const root = React.useContext(FiberContext)\r\n  if (root === null) throw new Error('its-fine: useFiber must be called within a <FiberProvider />!')\r\n\r\n  const id = React.useId()\r\n  const fiber = React.useMemo(() => {\r\n    for (const maybeFiber of [root, root?.alternate]) {\r\n      if (!maybeFiber) continue\r\n      const fiber = traverseFiber<null>(maybeFiber, false, (node) => {\r\n        let state = node.memoizedState\r\n        while (state) {\r\n          if (state.memoizedState === id) return true\r\n          state = state.next\r\n        }\r\n      })\r\n      if (fiber) return fiber\r\n    }\r\n  }, [root, id])\r\n\r\n  return fiber\r\n}\r\n\r\n/**\r\n * Represents a react-reconciler container instance.\r\n */\r\nexport interface ContainerInstance<T = any> {\r\n  containerInfo: T\r\n}\r\n\r\n/**\r\n * Returns the current react-reconciler container info passed to {@link ReactReconciler.Reconciler.createContainer}.\r\n *\r\n * In react-dom, a container will point to the root DOM element; in react-three-fiber, it will point to the root Zustand store.\r\n */\r\nexport function useContainer<T = any>(): T | undefined {\r\n  const fiber = useFiber()\r\n  const root = React.useMemo(\r\n    () => traverseFiber<ContainerInstance<T>>(fiber, true, (node) => node.stateNode?.containerInfo != null),\r\n    [fiber],\r\n  )\r\n\r\n  return root?.stateNode.containerInfo\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler child instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestChild<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const childRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    childRef.current = traverseFiber<T>(\r\n      fiber,\r\n      false,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return childRef\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler parent instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestParent<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const parentRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    parentRef.current = traverseFiber<T>(\r\n      fiber,\r\n      true,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return parentRef\r\n}\r\n\r\nexport type ContextMap = Map<React.Context<any>, any> & {\r\n  get<T>(context: React.Context<T>): T | undefined\r\n}\r\n\r\nconst REACT_CONTEXT_TYPE = Symbol.for('react.context')\r\n\r\nconst isContext = <T,>(type: unknown): type is React.Context<T> =>\r\n  type !== null && typeof type === 'object' && '$$typeof' in type && type.$$typeof === REACT_CONTEXT_TYPE\r\n\r\n/**\r\n * Returns a map of all contexts and their values.\r\n */\r\nexport function useContextMap(): ContextMap {\r\n  const fiber = useFiber()\r\n  const [contextMap] = React.useState(() => new Map<React.Context<any>, any>())\r\n\r\n  // Collect live context\r\n  contextMap.clear()\r\n  let node = fiber\r\n  while (node) {\r\n    const context = node.type\r\n    if (isContext(context) && context !== FiberContext && !contextMap.has(context)) {\r\n      contextMap.set(context, React.use(wrapContext(context)))\r\n    }\r\n\r\n    node = node.return!\r\n  }\r\n\r\n  return contextMap\r\n}\r\n\r\n/**\r\n * Represents a react-context bridge provider component.\r\n */\r\nexport type ContextBridge = React.FC<React.PropsWithChildren<{}>>\r\n\r\n/**\r\n * React Context currently cannot be shared across [React renderers](https://reactjs.org/docs/codebase-overview.html#renderers) but explicitly forwarded between providers (see [react#17275](https://github.com/facebook/react/issues/17275)). This hook returns a {@link ContextBridge} of live context providers to pierce Context across renderers.\r\n *\r\n * Pass {@link ContextBridge} as a component to a secondary renderer to enable context-sharing within its children.\r\n */\r\nexport function useContextBridge(): ContextBridge {\r\n  const contextMap = useContextMap()\r\n\r\n  // Flatten context and their memoized values into a `ContextBridge` provider\r\n  return React.useMemo(\r\n    () =>\r\n      Array.from(contextMap.keys()).reduce(\r\n        (Prev, context) => (props) =>\r\n          (\r\n            <Prev>\r\n              <context.Provider {...props} value={contextMap.get(context)} />\r\n            </Prev>\r\n          ),\r\n        (props) => <FiberProvider {...props} />,\r\n      ),\r\n    [contextMap],\r\n  )\r\n}\r\n"], "mappings": ";AAYA,MAAMA,CAAA,GAA6C;;EACjD,cAAOC,MAAA,IAAW,kBAAgBC,CAAA,GAAAD,MAAA,CAAOE,QAAA,KAAP,gBAAAD,CAAA,CAAiBE,aAAA,OAAiBC,CAAA,GAAAJ,MAAA,CAAOK,SAAA,KAAP,gBAAAD,CAAA,CAAkBE,OAAA,MAAY;AAAA,OAChGC,CAAA,CAAMC,eAAA,GACND,CAAA,CAAME,SAAA;AAkBM,SAAAC,EAEdT,CAAA,EAEAG,CAAA,EAEAO,CAAA,EACsB;EACtB,IAAI,CAACV,CAAA,EAAO;EACZ,IAAIU,CAAA,CAASV,CAAK,MAAM,IAAa,OAAAA,CAAA;EAErC,IAAIW,CAAA,GAAQR,CAAA,GAAYH,CAAA,CAAMY,MAAA,GAASZ,CAAA,CAAMa,KAAA;EAC7C,OAAOF,CAAA,GAAO;IACZ,MAAMG,CAAA,GAAQL,CAAA,CAAcE,CAAA,EAAOR,CAAA,EAAWO,CAAQ;IACtD,IAAII,CAAA,EAAc,OAAAA,CAAA;IAEVH,CAAA,GAAAR,CAAA,GAAY,OAAOQ,CAAA,CAAMI,OAAA;EAAA;AAErC;AAKA,SAASC,EAAehB,CAAA,EAA6C;EAC/D;IACK,OAAAiB,MAAA,CAAOC,gBAAA,CAAiBlB,CAAA,EAAS;MACtCmB,gBAAA,EAAkB;QAChBC,IAAA,EAAM;UACG;QACT;QACAC,IAAA,EAAM;MACR;MACAC,iBAAA,EAAmB;QACjBF,IAAA,EAAM;UACG;QACT;QACAC,IAAA,EAAM;MAAC;IACT,CACD;EAAA,SACMlB,CAAA,EAAG;IACH,OAAAH,CAAA;EAAA;AAEX;AAEA,MAAMuB,CAAA,GAA+B,eAAAP,CAAA,CAAkC,eAAAV,CAAA,CAAAkB,aAAA,CAAqB,IAAK,CAAC;AAKrF,MAAAC,CAAA,SAAsBnB,CAAA,CAAMoB,SAAA,CAA0C;EAGjFC,OAAA,EAAS;IACA,sBAAArB,CAAA,CAAAJ,aAAA,CAACqB,CAAA,CAAaK,QAAA,EAAb;MAAsBC,KAAA,EAAO,KAAKC;IAAA,GAAkB,KAAKC,KAAA,CAAMC,QAAS;EAAA;AAEpF;AAKO,SAASC,EAAA,EAAoC;EAC5C,MAAAjC,CAAA,GAAOM,CAAA,CAAM4B,UAAA,CAAWX,CAAY;EAC1C,IAAIvB,CAAA,KAAS,MAAY,UAAImC,KAAA,CAAM,+DAA+D;EAE5F,MAAAhC,CAAA,GAAKG,CAAA,CAAM8B,KAAA,CAAM;EAehB,OAdO9B,CAAA,CAAM+B,OAAA,CAAQ,MAAM;IAChC,WAAW1B,CAAA,IAAc,CAACX,CAAA,EAAMA,CAAA,oBAAAA,CAAA,CAAMsC,SAAS,GAAG;MAChD,IAAI,CAAC3B,CAAA,EAAY;MACjB,MAAMG,CAAA,GAAQL,CAAA,CAAoBE,CAAA,EAAY,IAAQ4B,CAAA,IAAS;QAC7D,IAAIC,CAAA,GAAQD,CAAA,CAAKE,aAAA;QACjB,OAAOD,CAAA,GAAO;UACR,IAAAA,CAAA,CAAMC,aAAA,KAAkBtC,CAAA,EAAW;UACvCqC,CAAA,GAAQA,CAAA,CAAME,IAAA;QAAA;MAChB,CACD;MACD,IAAI5B,CAAA,EAAc,OAAAA,CAAA;IAAA;EACpB,GACC,CAACd,CAAA,EAAMG,CAAE,CAAC;AAGf;AAcO,SAASwC,EAAA,EAAuC;EACrD,MAAM3C,CAAA,GAAQiC,CAAA,CAAS;IACjB9B,CAAA,GAAOG,CAAA,CAAM+B,OAAA,CACjB,MAAM5B,CAAA,CAAoCT,CAAA,EAAO,IAAOU,CAAA,IAAS;;MAAA,SAAAC,CAAA,GAAAD,CAAA,CAAKkC,SAAA,KAAL,gBAAAjC,CAAA,CAAgBkC,aAAA,KAAiB;IAAA,CAAI,GACtG,CAAC7C,CAAK,CACR;EAEA,OAAOG,CAAA,oBAAAA,CAAA,CAAMyC,SAAA,CAAUC,aAAA;AACzB;AAOO,SAASC,EAEd9C,CAAA,EACgC;EAChC,MAAMG,CAAA,GAAQ8B,CAAA,CAAS;IACjBvB,CAAA,GAAWJ,CAAA,CAAMyC,MAAA,CAAU,MAAS;EAE1C,OAAAjD,CAAA,CAA0B,MAAM;;IAC9BY,CAAA,CAASsC,OAAA,IAAUrC,CAAA,GAAAF,CAAA,CACjBN,CAAA,EACA,IACCW,CAAA,IAAS,OAAOA,CAAA,CAAKmC,IAAA,IAAS,aAAajD,CAAA,KAAS,UAAac,CAAA,CAAKmC,IAAA,KAASjD,CAAA,OAH/D,gBAAAW,CAAA,CAIhBiC,SAAA;EAAA,GACF,CAACzC,CAAK,CAAC,GAEHO,CAAA;AACT;AAOO,SAASwC,EAEdlD,CAAA,EACgC;EAChC,MAAMG,CAAA,GAAQ8B,CAAA,CAAS;IACjBvB,CAAA,GAAYJ,CAAA,CAAMyC,MAAA,CAAU,MAAS;EAE3C,OAAAjD,CAAA,CAA0B,MAAM;;IAC9BY,CAAA,CAAUsC,OAAA,IAAUrC,CAAA,GAAAF,CAAA,CAClBN,CAAA,EACA,IACCW,CAAA,IAAS,OAAOA,CAAA,CAAKmC,IAAA,IAAS,aAAajD,CAAA,KAAS,UAAac,CAAA,CAAKmC,IAAA,KAASjD,CAAA,OAH9D,gBAAAW,CAAA,CAIjBiC,SAAA;EAAA,GACF,CAACzC,CAAK,CAAC,GAEHO,CAAA;AACT;AAMA,MAAMyC,CAAA,GAAqBC,MAAA,CAAOC,GAAA,CAAI,eAAe;EAE/CC,CAAA,GAAiBtD,CAAA,IACrBA,CAAA,KAAS,QAAQ,OAAOA,CAAA,IAAS,YAAY,cAAcA,CAAA,IAAQA,CAAA,CAAKuD,QAAA,KAAaJ,CAAA;AAKhF,SAASK,EAAA,EAA4B;EAC1C,MAAMxD,CAAA,GAAQiC,CAAA,CAAS;IACjB,CAAC9B,CAAU,IAAIG,CAAA,CAAMmD,QAAA,CAAS,MAAM,mBAAIC,GAAA,EAA8B;EAG5EvD,CAAA,CAAWwD,KAAA,CAAM;EACjB,IAAIjD,CAAA,GAAOV,CAAA;EACX,OAAOU,CAAA,GAAM;IACX,MAAMC,CAAA,GAAUD,CAAA,CAAKuC,IAAA;IACjBK,CAAA,CAAU3C,CAAO,KAAKA,CAAA,KAAYY,CAAA,IAAgB,CAACpB,CAAA,CAAWyD,GAAA,CAAIjD,CAAO,KAC3ER,CAAA,CAAWkB,GAAA,CAAIV,CAAA,EAASL,CAAA,CAAMuD,GAAA,CAAI7C,CAAA,CAAYL,CAAO,CAAC,CAAC,GAGzDD,CAAA,GAAOA,CAAA,CAAKE,MAAA;EAAA;EAGP,OAAAT,CAAA;AACT;AAYO,SAAS2D,EAAA,EAAkC;EAChD,MAAM9D,CAAA,GAAawD,CAAA,CAAc;EAGjC,OAAOlD,CAAA,CAAM+B,OAAA,CACX,MACE0B,KAAA,CAAMC,IAAA,CAAKhE,CAAA,CAAWiE,IAAA,EAAM,EAAEC,MAAA,CAC5B,CAAC/D,CAAA,EAAMO,CAAA,KAAaC,CAAA,IAEhB,eAAAL,CAAA,CAAAJ,aAAA,CAACC,CAAA,QACE,eAAAG,CAAA,CAAAJ,aAAA,CAAAQ,CAAA,CAAQkB,QAAA,EAAR;IAAkB,GAAGjB,CAAA;IAAOkB,KAAA,EAAO7B,CAAA,CAAWoB,GAAA,CAAIV,CAAO;EAAA,CAAG,CAC/D,GAEHP,CAAA,IAAW,eAAAG,CAAA,CAAAJ,aAAA,CAAAuB,CAAA;IAAe,GAAGtB;EAAO,EACvC,GACF,CAACH,CAAU,CACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}