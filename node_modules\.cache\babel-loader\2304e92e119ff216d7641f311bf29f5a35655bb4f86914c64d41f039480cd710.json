{"ast": null, "code": "import { e as extend, u as useBridge, a as useMutableCallback, b as useIsomorphicLayoutEffect, c as createRoot, i as isRef, E as ErrorBoundary, B as Block, d as unmountComponentAtNode, f as createPointerEvents } from './events-dc44c1b8.esm.js';\nexport { t as ReactThreeFiber, _ as _roots, w as act, k as addAfterEffect, j as addEffect, l as addTail, n as advance, q as applyProps, x as buildGraph, p as context, g as createEvents, o as createPortal, c as createRoot, v as dispose, f as events, e as extend, h as flushGlobalEffects, s as getRootState, m as invalidate, r as reconciler, d as unmountComponentAtNode, C as useFrame, D as useGraph, y as useInstanceHandle, F as useLoader, z as useStore, A as useThree } from './events-dc44c1b8.esm.js';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport useMeasure from 'react-use-measure';\nimport { FiberProvider } from 'its-fine';\nimport { jsx } from 'react/jsx-runtime';\nimport 'react-reconciler/constants';\nimport 'zustand/traditional';\nimport 'react-reconciler';\nimport 'scheduler';\nimport 'suspend-react';\nfunction CanvasImpl({\n  ref,\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = createPointerEvents,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  React.useMemo(() => extend(THREE), []);\n  const Bridge = useBridge();\n  const [containerRef, containerRect] = useMeasure({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = React.useRef(null);\n  const divRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => canvasRef.current);\n  const handlePointerMissed = useMutableCallback(onPointerMissed);\n  const [block, setBlock] = React.useState(false);\n  const [error, setError] = React.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = React.useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = createRoot(canvas);\n      async function run() {\n        await root.current.configure({\n          gl,\n          scene,\n          events,\n          shadows,\n          linear,\n          flat,\n          legacy,\n          orthographic,\n          frameloop,\n          dpr,\n          performance,\n          raycaster,\n          camera,\n          size: containerRect,\n          // Pass mutable reference to onPointerMissed so it's free to update\n          onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n          onCreated: state => {\n            // Connect to event source\n            state.events.connect == null ? void 0 : state.events.connect(eventSource ? isRef(eventSource) ? eventSource.current : eventSource : divRef.current);\n            // Set up compute function\n            if (eventPrefix) {\n              state.setEvents({\n                compute: (event, state) => {\n                  const x = event[eventPrefix + 'X'];\n                  const y = event[eventPrefix + 'Y'];\n                  state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                  state.raycaster.setFromCamera(state.pointer, state.camera);\n                }\n              });\n            }\n            // Call onCreated callback\n            onCreated == null ? void 0 : onCreated(state);\n          }\n        });\n        root.current.render(/*#__PURE__*/jsx(Bridge, {\n          children: /*#__PURE__*/jsx(ErrorBoundary, {\n            set: setError,\n            children: /*#__PURE__*/jsx(React.Suspense, {\n              fallback: /*#__PURE__*/jsx(Block, {\n                set: setBlock\n              }),\n              children: children != null ? children : null\n            })\n          })\n        }));\n      }\n      run();\n    }\n  });\n  React.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => unmountComponentAtNode(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/jsx(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/jsx(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n}\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nfunction Canvas(props) {\n  return /*#__PURE__*/jsx(FiberProvider, {\n    children: /*#__PURE__*/jsx(CanvasImpl, {\n      ...props\n    })\n  });\n}\nexport { Canvas };", "map": {"version": 3, "names": ["e", "extend", "u", "useBridge", "a", "useMutableCallback", "b", "useIsomorphicLayoutEffect", "c", "createRoot", "i", "isRef", "E", "Error<PERSON>ou<PERSON><PERSON>", "B", "Block", "d", "unmountComponentAtNode", "f", "createPointerEvents", "t", "ReactThreeFiber", "_", "_roots", "w", "act", "k", "addAfterEffect", "j", "addEffect", "l", "addTail", "n", "advance", "q", "applyProps", "x", "buildGraph", "p", "context", "g", "createEvents", "o", "createPortal", "v", "dispose", "events", "h", "flushGlobalEffects", "s", "getRootState", "m", "invalidate", "r", "reconciler", "C", "useFrame", "D", "useGraph", "y", "useInstanceHandle", "F", "useLoader", "z", "useStore", "A", "useThree", "React", "THREE", "useMeasure", "FiberProvider", "jsx", "CanvasImpl", "ref", "children", "fallback", "resize", "style", "gl", "eventSource", "eventPrefix", "shadows", "linear", "flat", "legacy", "orthographic", "frameloop", "dpr", "performance", "raycaster", "camera", "scene", "onPointerMissed", "onCreated", "props", "useMemo", "Bridge", "containerRef", "containerRect", "scroll", "debounce", "canvasRef", "useRef", "divRef", "useImperativeHandle", "current", "handlePointerMissed", "block", "setBlock", "useState", "error", "setError", "root", "canvas", "width", "height", "run", "configure", "size", "args", "state", "connect", "setEvents", "compute", "event", "pointer", "set", "setFromCamera", "render", "Suspense", "useEffect", "pointerEvents", "position", "overflow", "display", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js"], "sourcesContent": ["import { e as extend, u as useBridge, a as useMutableCallback, b as useIsomorphicLayoutEffect, c as createRoot, i as isRef, E as ErrorBoundary, B as Block, d as unmountComponentAtNode, f as createPointerEvents } from './events-dc44c1b8.esm.js';\nexport { t as ReactThreeFiber, _ as _roots, w as act, k as addAfterEffect, j as addEffect, l as addTail, n as advance, q as applyProps, x as buildGraph, p as context, g as createEvents, o as createPortal, c as createRoot, v as dispose, f as events, e as extend, h as flushGlobalEffects, s as getRootState, m as invalidate, r as reconciler, d as unmountComponentAtNode, C as useFrame, D as useGraph, y as useInstanceHandle, F as useLoader, z as useStore, A as useThree } from './events-dc44c1b8.esm.js';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport useMeasure from 'react-use-measure';\nimport { FiberProvider } from 'its-fine';\nimport { jsx } from 'react/jsx-runtime';\nimport 'react-reconciler/constants';\nimport 'zustand/traditional';\nimport 'react-reconciler';\nimport 'scheduler';\nimport 'suspend-react';\n\nfunction CanvasImpl({\n  ref,\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = createPointerEvents,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  React.useMemo(() => extend(THREE), []);\n  const Bridge = useBridge();\n  const [containerRef, containerRect] = useMeasure({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = React.useRef(null);\n  const divRef = React.useRef(null);\n  React.useImperativeHandle(ref, () => canvasRef.current);\n  const handlePointerMissed = useMutableCallback(onPointerMissed);\n  const [block, setBlock] = React.useState(false);\n  const [error, setError] = React.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = React.useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = createRoot(canvas);\n      async function run() {\n        await root.current.configure({\n          gl,\n          scene,\n          events,\n          shadows,\n          linear,\n          flat,\n          legacy,\n          orthographic,\n          frameloop,\n          dpr,\n          performance,\n          raycaster,\n          camera,\n          size: containerRect,\n          // Pass mutable reference to onPointerMissed so it's free to update\n          onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n          onCreated: state => {\n            // Connect to event source\n            state.events.connect == null ? void 0 : state.events.connect(eventSource ? isRef(eventSource) ? eventSource.current : eventSource : divRef.current);\n            // Set up compute function\n            if (eventPrefix) {\n              state.setEvents({\n                compute: (event, state) => {\n                  const x = event[eventPrefix + 'X'];\n                  const y = event[eventPrefix + 'Y'];\n                  state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                  state.raycaster.setFromCamera(state.pointer, state.camera);\n                }\n              });\n            }\n            // Call onCreated callback\n            onCreated == null ? void 0 : onCreated(state);\n          }\n        });\n        root.current.render( /*#__PURE__*/jsx(Bridge, {\n          children: /*#__PURE__*/jsx(ErrorBoundary, {\n            set: setError,\n            children: /*#__PURE__*/jsx(React.Suspense, {\n              fallback: /*#__PURE__*/jsx(Block, {\n                set: setBlock\n              }),\n              children: children != null ? children : null\n            })\n          })\n        }));\n      }\n      run();\n    }\n  });\n  React.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => unmountComponentAtNode(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/jsx(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/jsx(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n}\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nfunction Canvas(props) {\n  return /*#__PURE__*/jsx(FiberProvider, {\n    children: /*#__PURE__*/jsx(CanvasImpl, {\n      ...props\n    })\n  });\n}\n\nexport { Canvas };\n"], "mappings": "AAAA,SAASA,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,mBAAmB,QAAQ,0BAA0B;AACnP,SAASC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,YAAY,EAAEnC,CAAC,IAAIC,UAAU,EAAEmC,CAAC,IAAIC,OAAO,EAAE3B,CAAC,IAAI4B,MAAM,EAAE9C,CAAC,IAAIC,MAAM,EAAE8C,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,UAAU,EAAEtC,CAAC,IAAIC,sBAAsB,EAAEsC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,QAAQ,QAAQ,0BAA0B;AACrf,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,SAASC,aAAa,QAAQ,UAAU;AACxC,SAASC,GAAG,QAAQ,mBAAmB;AACvC,OAAO,4BAA4B;AACnC,OAAO,qBAAqB;AAC5B,OAAO,kBAAkB;AACzB,OAAO,WAAW;AAClB,OAAO,eAAe;AAEtB,SAASC,UAAUA,CAAC;EAClBC,GAAG;EACHC,QAAQ;EACRC,QAAQ;EACRC,MAAM;EACNC,KAAK;EACLC,EAAE;EACFhC,MAAM,GAAG3B,mBAAmB;EAC5B4D,WAAW;EACXC,WAAW;EACXC,OAAO;EACPC,MAAM;EACNC,IAAI;EACJC,MAAM;EACNC,YAAY;EACZC,SAAS;EACTC,GAAG;EACHC,WAAW;EACXC,SAAS;EACTC,MAAM;EACNC,KAAK;EACLC,eAAe;EACfC,SAAS;EACT,GAAGC;AACL,CAAC,EAAE;EACD;EACA;EACA;EACA3B,KAAK,CAAC4B,OAAO,CAAC,MAAM9F,MAAM,CAACmE,KAAK,CAAC,EAAE,EAAE,CAAC;EACtC,MAAM4B,MAAM,GAAG7F,SAAS,CAAC,CAAC;EAC1B,MAAM,CAAC8F,YAAY,EAAEC,aAAa,CAAC,GAAG7B,UAAU,CAAC;IAC/C8B,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE;MACRD,MAAM,EAAE,EAAE;MACVvB,MAAM,EAAE;IACV,CAAC;IACD,GAAGA;EACL,CAAC,CAAC;EACF,MAAMyB,SAAS,GAAGlC,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,MAAM,GAAGpC,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EACjCnC,KAAK,CAACqC,mBAAmB,CAAC/B,GAAG,EAAE,MAAM4B,SAAS,CAACI,OAAO,CAAC;EACvD,MAAMC,mBAAmB,GAAGrG,kBAAkB,CAACuF,eAAe,CAAC;EAC/D,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,KAAK,CAAC0C,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG5C,KAAK,CAAC0C,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACA,IAAIF,KAAK,EAAE,MAAMA,KAAK;EACtB;EACA,IAAIG,KAAK,EAAE,MAAMA,KAAK;EACtB,MAAME,IAAI,GAAG7C,KAAK,CAACmC,MAAM,CAAC,IAAI,CAAC;EAC/B/F,yBAAyB,CAAC,MAAM;IAC9B,MAAM0G,MAAM,GAAGZ,SAAS,CAACI,OAAO;IAChC,IAAIP,aAAa,CAACgB,KAAK,GAAG,CAAC,IAAIhB,aAAa,CAACiB,MAAM,GAAG,CAAC,IAAIF,MAAM,EAAE;MACjE,IAAI,CAACD,IAAI,CAACP,OAAO,EAAEO,IAAI,CAACP,OAAO,GAAGhG,UAAU,CAACwG,MAAM,CAAC;MACpD,eAAeG,GAAGA,CAAA,EAAG;QACnB,MAAMJ,IAAI,CAACP,OAAO,CAACY,SAAS,CAAC;UAC3BvC,EAAE;UACFa,KAAK;UACL7C,MAAM;UACNmC,OAAO;UACPC,MAAM;UACNC,IAAI;UACJC,MAAM;UACNC,YAAY;UACZC,SAAS;UACTC,GAAG;UACHC,WAAW;UACXC,SAAS;UACTC,MAAM;UACN4B,IAAI,EAAEpB,aAAa;UACnB;UACAN,eAAe,EAAEA,CAAC,GAAG2B,IAAI,KAAKb,mBAAmB,CAACD,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGC,mBAAmB,CAACD,OAAO,CAAC,GAAGc,IAAI,CAAC;UACjH1B,SAAS,EAAE2B,KAAK,IAAI;YAClB;YACAA,KAAK,CAAC1E,MAAM,CAAC2E,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGD,KAAK,CAAC1E,MAAM,CAAC2E,OAAO,CAAC1C,WAAW,GAAGpE,KAAK,CAACoE,WAAW,CAAC,GAAGA,WAAW,CAAC0B,OAAO,GAAG1B,WAAW,GAAGwB,MAAM,CAACE,OAAO,CAAC;YACnJ;YACA,IAAIzB,WAAW,EAAE;cACfwC,KAAK,CAACE,SAAS,CAAC;gBACdC,OAAO,EAAEA,CAACC,KAAK,EAAEJ,KAAK,KAAK;kBACzB,MAAMpF,CAAC,GAAGwF,KAAK,CAAC5C,WAAW,GAAG,GAAG,CAAC;kBAClC,MAAMrB,CAAC,GAAGiE,KAAK,CAAC5C,WAAW,GAAG,GAAG,CAAC;kBAClCwC,KAAK,CAACK,OAAO,CAACC,GAAG,CAAC1F,CAAC,GAAGoF,KAAK,CAACF,IAAI,CAACJ,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAEvD,CAAC,GAAG6D,KAAK,CAACF,IAAI,CAACH,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;kBACjFK,KAAK,CAAC/B,SAAS,CAACsC,aAAa,CAACP,KAAK,CAACK,OAAO,EAAEL,KAAK,CAAC9B,MAAM,CAAC;gBAC5D;cACF,CAAC,CAAC;YACJ;YACA;YACAG,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC2B,KAAK,CAAC;UAC/C;QACF,CAAC,CAAC;QACFR,IAAI,CAACP,OAAO,CAACuB,MAAM,CAAE,aAAazD,GAAG,CAACyB,MAAM,EAAE;UAC5CtB,QAAQ,EAAE,aAAaH,GAAG,CAAC1D,aAAa,EAAE;YACxCiH,GAAG,EAAEf,QAAQ;YACbrC,QAAQ,EAAE,aAAaH,GAAG,CAACJ,KAAK,CAAC8D,QAAQ,EAAE;cACzCtD,QAAQ,EAAE,aAAaJ,GAAG,CAACxD,KAAK,EAAE;gBAChC+G,GAAG,EAAElB;cACP,CAAC,CAAC;cACFlC,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG;YAC1C,CAAC;UACH,CAAC;QACH,CAAC,CAAC,CAAC;MACL;MACA0C,GAAG,CAAC,CAAC;IACP;EACF,CAAC,CAAC;EACFjD,KAAK,CAAC+D,SAAS,CAAC,MAAM;IACpB,MAAMjB,MAAM,GAAGZ,SAAS,CAACI,OAAO;IAChC,IAAIQ,MAAM,EAAE,OAAO,MAAMhG,sBAAsB,CAACgG,MAAM,CAAC;EACzD,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA,MAAMkB,aAAa,GAAGpD,WAAW,GAAG,MAAM,GAAG,MAAM;EACnD,OAAO,aAAaR,GAAG,CAAC,KAAK,EAAE;IAC7BE,GAAG,EAAE8B,MAAM;IACX1B,KAAK,EAAE;MACLuD,QAAQ,EAAE,UAAU;MACpBlB,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdkB,QAAQ,EAAE,QAAQ;MAClBF,aAAa;MACb,GAAGtD;IACL,CAAC;IACD,GAAGiB,KAAK;IACRpB,QAAQ,EAAE,aAAaH,GAAG,CAAC,KAAK,EAAE;MAChCE,GAAG,EAAEwB,YAAY;MACjBpB,KAAK,EAAE;QACLqC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE;MACV,CAAC;MACDzC,QAAQ,EAAE,aAAaH,GAAG,CAAC,QAAQ,EAAE;QACnCE,GAAG,EAAE4B,SAAS;QACdxB,KAAK,EAAE;UACLyD,OAAO,EAAE;QACX,CAAC;QACD5D,QAAQ,EAAEC;MACZ,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAAS4D,MAAMA,CAACzC,KAAK,EAAE;EACrB,OAAO,aAAavB,GAAG,CAACD,aAAa,EAAE;IACrCI,QAAQ,EAAE,aAAaH,GAAG,CAACC,UAAU,EAAE;MACrC,GAAGsB;IACL,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,SAASyC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}